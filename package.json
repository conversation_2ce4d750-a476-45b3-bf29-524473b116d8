{"name": "KhelSportsPlayerApp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@d11/react-native-fast-image": "^8.10.0", "@react-native-async-storage/async-storage": "^1.22.3", "@react-native-community/checkbox": "^0.5.17", "@react-native-community/datetimepicker": "^7.7.0", "@react-native-community/geolocation": "^3.2.1", "@react-native-community/slider": "^4.3.1", "@react-native-google-signin/google-signin": "^11.0.1", "@react-native-picker/picker": "^2.7.2", "@react-navigation/drawer": "^6.6.6", "@react-navigation/native-stack": "^6.9.17", "@rneui/themed": "^4.0.0-rc.8", "axios": "^1.10.0", "country-state-city": "^3.2.1", "formik": "^2.4.5", "lodash": "^4.17.21", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "radio-buttons-react-native": "^1.0.4", "react": "18.2.0", "react-datepicker": "^6.6.0", "react-modern-calendar-datepicker": "^3.1.6", "react-native": "0.73.3", "react-native-asset": "^2.1.1", "react-native-calendar-picker": "^8.0.2", "react-native-calendar-timetable": "^1.0.6", "react-native-calendars": "^1.1304.1", "react-native-check-box": "^2.1.7", "react-native-date-picker": "^4.4.2", "react-native-dotenv": "^3.4.9", "react-native-drawer": "^2.5.1", "react-native-dropdown-picker": "^5.4.6", "react-native-dropdown-select-list": "^2.0.5", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.15.0", "react-native-google-places-autocomplete": "^1.3.9", "react-native-html-to-pdf": "^0.12.0", "react-native-linear-gradient": "^2.8.3", "react-native-modal-datetime-picker": "^17.1.0", "react-native-modern-datepicker": "^1.0.0-beta.91", "react-native-orientation-locker": "^1.7.0", "react-native-paper": "^5.12.3", "react-native-pdf": "^6.7.5", "react-native-pell-rich-editor": "^1.9.0", "react-native-permission": "^0.0.1", "react-native-permissions": "^4.1.5", "react-native-pure-jwt": "^3.0.2", "react-native-qrcode-scanner": "^1.5.5", "react-native-ratings": "^8.1.0", "react-native-razorpay": "^2.3.0", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^4.9.0", "react-native-scoped-storage": "^1.9.5", "react-native-screens": "^3.29.0", "react-native-select-dropdown": "^3.4.0", "react-native-share": "^10.2.1", "react-native-size-matters": "^0.4.2", "react-native-swiper": "^1.6.0", "react-native-vector-icons": "^10.1.0", "react-native-vision-camera": "^4.0.1", "react-native-walkthrough-tooltip": "^1.6.0", "react-native-web": "^0.19.10", "react-native-webview": "^13.8.1", "rn-fetch-blob": "^0.12.0", "yup": "^1.3.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.73.20", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.4", "@react-native/typescript-config": "0.73.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}