#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffcb80ce0a9, pid=10500, tid=2856
#
# JRE version: OpenJDK Runtime Environment Microsoft-8035246 (17.0.8+7) (build 17.0.8+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Microsoft-8035246 (17.0.8+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# Problematic frame:
# V  [jvm.dll+0x2be0a9]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://github.com/microsoft/openjdk/issues
#

---------------  S U M M A R Y ------------

Command Line: -XX:MaxMetaspaceSize=512m --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx2048m -Dfile.encoding=windows-1252 -Duser.country=IN -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.3-all\6en3ugtfdg5xnpx44z4qbwgas\gradle-8.3\lib\agents\gradle-instrumentation-agent-8.3.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.3

Host: 12th Gen Intel(R) Core(TM) i7-12650H, 16 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.3085)
Time: Mon Feb 12 10:01:08 2024 India Standard Time elapsed time: 24.202430 seconds (0d 0h 0m 24s)

---------------  T H R E A D  ---------------

Current thread (0x00000266b2fa8a90):  JavaThread "C2 CompilerThread3" daemon [_thread_in_native, id=2856, stack(0x000000f1c3a00000,0x000000f1c3b00000)]


Current CompileTask:
C2:  24202 16179   !   4       org.gradle.cache.internal.DefaultMultiProcessSafeIndexedCache::getIfPresent (25 bytes)

Stack: [0x000000f1c3a00000,0x000000f1c3b00000],  sp=0x000000f1c3afb860,  free space=1006k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x2be0a9]
V  [jvm.dll+0x21ba8b]
V  [jvm.dll+0x21944b]
V  [jvm.dll+0x1a56b6]
V  [jvm.dll+0x2291ea]
V  [jvm.dll+0x22724c]
V  [jvm.dll+0x7efd0c]
V  [jvm.dll+0x7ea13a]
V  [jvm.dll+0x67ad85]
C  [ucrtbase.dll+0x29363]
C  [KERNEL32.DLL+0x1257d]
C  [ntdll.dll+0x5aa58]


siginfo: EXCEPTION_ACCESS_VIOLATION (0xc0000005), reading address 0x0000000000000028


Register to memory mapping:

RIP=0x00007ffcb80ce0a9 jvm.dll
RAX=0x00000266bfcb48a0 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
RBX=0x0000000000001924 is an unknown value
RCX=0x0 is NULL
RDX=0x0 is NULL
RSP=0x000000f1c3afb860 is pointing into the stack for thread: 0x00000266b2fa8a90
RBP=0x000000f1c3afb8d0 is pointing into the stack for thread: 0x00000266b2fa8a90
RSI=0x0 is NULL
RDI=0x000000000000004d is an unknown value
R8 =0x000000000000ffe0 is an unknown value
R9 =0x0 is NULL
R10=0x00007ffd2eb80000 VCRUNTIME140.dll
R11=0x00000266bfcb48c0 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
R12=0x0000000000000050 is an unknown value
R13=0x0 is NULL
R14=0x0 is NULL
R15=0x000000f1c3afea80 is pointing into the stack for thread: 0x00000266b2fa8a90


Registers:
RAX=0x00000266bfcb48a0, RBX=0x0000000000001924, RCX=0x0000000000000000, RDX=0x0000000000000000
RSP=0x000000f1c3afb860, RBP=0x000000f1c3afb8d0, RSI=0x0000000000000000, RDI=0x000000000000004d
R8 =0x000000000000ffe0, R9 =0x0000000000000000, R10=0x00007ffd2eb80000, R11=0x00000266bfcb48c0
R12=0x0000000000000050, R13=0x0000000000000000, R14=0x0000000000000000, R15=0x000000f1c3afea80
RIP=0x00007ffcb80ce0a9, EFLAGS=0x0000000000010202

Top of Stack: (sp=0x000000f1c3afb860)
0x000000f1c3afb860:   0000000000001924 0000000000000000
0x000000f1c3afb870:   000000f1c3afea80 00007ffcb80338d1
0x000000f1c3afb880:   00000266b7e7f990 00007ffc00002000
0x000000f1c3afb890:   00000266bfcb48a0 0000000000000000
0x000000f1c3afb8a0:   0000000000000002 00000266c0d43f10
0x000000f1c3afb8b0:   0000000000000002 00000266b7e7f990
0x000000f1c3afb8c0:   0000000000000000 0000000000000000
0x000000f1c3afb8d0:   00000266b23f7f50 00000266f36d94d0
0x000000f1c3afb8e0:   000000000000004d 00007ffcb8754c08
0x000000f1c3afb8f0:   000000f1c3afba00 00007ffcb802ba8b
0x000000f1c3afb900:   000000f1c3afea80 00007ffcb8754c08
0x000000f1c3afb910:   000000f1c3afea80 000000f1c3afba00
0x000000f1c3afb920:   0000026600000000 00007ffcb842b7fb
0x000000f1c3afb930:   000000f10000000f 000000f1c3afea80
0x000000f1c3afb940:   000000f1c3afbab0 0000000000000000
0x000000f1c3afb950:   0000026600000100 00007ffcb8754c08 

Instructions: (pc=0x00007ffcb80ce0a9)
0x00007ffcb80cdfa9:   3b d0 75 0b ff c1 41 3b c9 7c dc 48 8b c2 c3 33
0x00007ffcb80cdfb9:   c0 c3 cc cc cc cc cc 48 89 5c 24 18 55 56 57 41
0x00007ffcb80cdfc9:   55 41 57 48 8b ec 48 83 ec 70 65 48 8b 04 25 58
0x00007ffcb80cdfd9:   00 00 00 48 8b f2 44 8b 05 d2 8b 90 00 4c 8b f9
0x00007ffcb80cdfe9:   4a 8b 1c c0 b8 50 00 00 00 80 3c 18 00 75 05 e8
0x00007ffcb80cdff9:   8b 6f 5d 00 b8 20 00 00 00 48 8b 04 18 48 8b 98
0x00007ffcb80ce009:   38 02 00 00 48 89 5d b0 c7 45 b8 04 00 00 00 48
0x00007ffcb80ce019:   8b 43 18 48 83 f8 df 76 18 4c 8d 05 37 a9 65 00
0x00007ffcb80ce029:   ba 20 00 00 00 48 8b cb e8 5a eb de ff 48 8b 43
0x00007ffcb80ce039:   18 48 8d 48 20 48 3b 4b 20 76 11 45 33 c0 48 8b
0x00007ffcb80ce049:   cb 41 8d 50 20 e8 fd e8 de ff eb 04 48 89 4b 18
0x00007ffcb80ce059:   44 8b 45 b8 33 d2 49 c1 e0 03 48 8b c8 48 89 45
0x00007ffcb80ce069:   c0 e8 e3 80 5d 00 45 33 ed 48 8d 4d d0 44 89 6d
0x00007ffcb80ce079:   c8 e8 01 59 56 00 41 8b 9f 40 02 00 00 41 2b 9f
0x00007ffcb80ce089:   68 02 00 00 44 89 6d f0 3b 5d b8 72 0b 8b d3 48
0x00007ffcb80ce099:   8d 4d b0 e8 6f b3 39 00 48 8b 45 c0 4c 89 2c d8
0x00007ffcb80ce0a9:   8b 7e 28 8b df c1 eb 05 3b 5d d0 72 0b 8b d3 48
0x00007ffcb80ce0b9:   8d 4d d0 e8 1f 59 56 00 48 8b 45 d8 83 e7 1f 48
0x00007ffcb80ce0c9:   8d 14 98 40 0f b6 cf 41 b8 01 00 00 00 41 d3 e0
0x00007ffcb80ce0d9:   8b 0a 8b c1 41 0b c0 89 02 41 85 c8 75 21 8b 5d
0x00007ffcb80ce0e9:   c8 8d 43 01 89 45 c8 3b 5d b8 72 0b 8b d3 48 8d
0x00007ffcb80ce0f9:   4d b0 e8 10 b3 39 00 48 8b 45 c0 48 89 34 d8 44
0x00007ffcb80ce109:   39 6d c8 0f 86 77 01 00 00 4c 89 a4 24 a0 00 00
0x00007ffcb80ce119:   00 4c 89 b4 24 a8 00 00 00 48 8b 45 c0 41 8b cd
0x00007ffcb80ce129:   48 8b 34 c8 8b 46 2c 25 ff 1f 00 00 3d 04 10 00
0x00007ffcb80ce139:   00 0f 85 a7 00 00 00 bb 01 00 00 00 39 5e 18 76
0x00007ffcb80ce149:   39 66 0f 1f 44 00 00 48 8b 46 08 8b cb 48 8b 14
0x00007ffcb80ce159:   c8 48 85 d2 74 1d 48 83 7a 10 00 74 16 80 7a 2c
0x00007ffcb80ce169:   84 74 10 e8 7f 7e ee ff 48 8b d0 49 8b cf e8 a4
0x00007ffcb80ce179:   61 f6 ff ff c3 3b 5e 18 72 cd 48 8b 5e 10 8b 46
0x00007ffcb80ce189:   20 4c 8d 34 c3 49 3b de 73 54 48 8b 3b 8b 47 2c
0x00007ffcb80ce199:   83 e0 03 3c 03 75 2e 48 8b 07 48 8b cf ff 90 88 


Stack slot to memory mapping:
stack at sp + 0 slots: 0x0000000000001924 is an unknown value
stack at sp + 1 slots: 0x0 is NULL
stack at sp + 2 slots: 0x000000f1c3afea80 is pointing into the stack for thread: 0x00000266b2fa8a90
stack at sp + 3 slots: 0x00007ffcb80338d1 jvm.dll
stack at sp + 4 slots: 0x00000266b7e7f990 points into unknown readable memory: 0x0000000000000006 | 06 00 00 00 00 00 00 00
stack at sp + 5 slots: 0x00007ffc00002000 is an unknown value
stack at sp + 6 slots: 0x00000266bfcb48a0 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
stack at sp + 7 slots: 0x0 is NULL


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000266b54c9990, length=246, elements={
0x00000266f36c23c0, 0x00000266ff666bd0, 0x00000266ff667960, 0x00000266ff676060,
0x00000266ff677940, 0x00000266ff67a220, 0x00000266ff67cf00, 0x00000266ff6812c0,
0x00000266ff685390, 0x00000266ff68ac10, 0x00000266ff7c4660, 0x00000266ff89a7a0,
0x00000266b39189b0, 0x00000266b37c6700, 0x00000266b31bcf00, 0x00000266b3fe75e0,
0x00000266b42b5470, 0x00000266b411b0c0, 0x00000266b411b5d0, 0x00000266b411d430,
0x00000266b411e870, 0x00000266b411bae0, 0x00000266b411c500, 0x00000266b411ca10,
0x00000266b411cf20, 0x00000266b411d940, 0x00000266b411de50, 0x00000266b411e360,
0x00000266b4a1a360, 0x00000266b4a14d50, 0x00000266b4a19e50, 0x00000266b4a1a870,
0x00000266b4a15260, 0x00000266b4a1ad80, 0x00000266b4a15c80, 0x00000266b4a19430,
0x00000266b4a14840, 0x00000266b4a15770, 0x00000266b4a16190, 0x00000266b4a18f20,
0x00000266b4a166a0, 0x00000266b4a170c0, 0x00000266b4a1b290, 0x00000266b4a19940,
0x00000266b4a13e20, 0x00000266b4a1b7a0, 0x00000266b4a17ff0, 0x00000266b4a14330,
0x00000266b4a175d0, 0x00000266b4a18500, 0x00000266b3076fc0, 0x00000266b3075160,
0x00000266b3076ab0, 0x00000266b30774d0, 0x00000266b3075670, 0x00000266b3074c50,
0x00000266b3075b80, 0x00000266b30765a0, 0x00000266b3070060, 0x00000266b30779e0,
0x00000266b3074230, 0x00000266b3074740, 0x00000266b3073d20, 0x00000266b30714a0,
0x00000266b3070570, 0x00000266b3070a80, 0x00000266b3070f90, 0x00000266b3073810,
0x00000266b30719b0, 0x00000266b3071ec0, 0x00000266b30728e0, 0x00000266b3072df0,
0x00000266b3073300, 0x00000266b7670060, 0x00000266b766c3a0, 0x00000266b766f640,
0x00000266b7670f90, 0x00000266b766f130, 0x00000266b7670a80, 0x00000266b7671ec0,
0x00000266b766d7e0, 0x00000266b766dcf0, 0x00000266b76728e0, 0x00000266b76714a0,
0x00000266b76723d0, 0x00000266b7672df0, 0x00000266b766fb50, 0x00000266b7673300,
0x00000266b766be90, 0x00000266b766ec20, 0x00000266b7673810, 0x00000266b766c8b0,
0x00000266b766cdc0, 0x00000266b7670570, 0x00000266b76719b0, 0x00000266b766e200,
0x00000266b766d2d0, 0x00000266b766e710, 0x00000266b3be57c0, 0x00000266b3be01b0,
0x00000266b3be2520, 0x00000266b3be5cd0, 0x00000266b3be4890, 0x00000266b3be3960,
0x00000266b3be4da0, 0x00000266b3be52b0, 0x00000266b3be06c0, 0x00000266b3be15f0,
0x00000266b3bde860, 0x00000266b3be4380, 0x00000266b3be61e0, 0x00000266b3be2010,
0x00000266b3be2a30, 0x00000266b3be2f40, 0x00000266b3bded70, 0x00000266b3bdf280,
0x00000266b3bdf790, 0x00000266b3bdfca0, 0x00000266b3be0bd0, 0x00000266b3be3450,
0x00000266b3be10e0, 0x00000266b3be1b00, 0x00000266b3be3e70, 0x00000266b7dd5670,
0x00000266b7dd0570, 0x00000266b7dd3810, 0x00000266b7dcfb50, 0x00000266b7dd4c50,
0x00000266b7dd6fc0, 0x00000266b7dd3d20, 0x00000266b7dd5160, 0x00000266b2fa3040,
0x00000266b2fa1b00, 0x00000266b2fa6560, 0x00000266b2fa15b0, 0x00000266b7dd0060,
0x00000266b2fa6010, 0x00000266b7dd3300, 0x00000266b7dd0f90, 0x00000266b7dd4740,
0x00000266b7dd14a0, 0x00000266b7dd19b0, 0x00000266b7dd23d0, 0x00000266b7dd28e0,
0x00000266b6060f40, 0x00000266b6060010, 0x00000266b605be40, 0x00000266b605ebd0,
0x00000266b605f0e0, 0x00000266b605cd70, 0x00000266b605d790, 0x00000266b6061450,
0x00000266b2fa8a90, 0x00000266b605d280, 0x00000266b6060a30, 0x00000266b6061960,
0x00000266b6061e70, 0x00000266b605aa00, 0x00000266b6062380, 0x00000266b605e1b0,
0x00000266b605af10, 0x00000266b605b420, 0x00000266b605f5f0, 0x00000266b605b930,
0x00000266b605fb00, 0x00000266b6060520, 0x00000266b605c350, 0x00000266b605c860,
0x00000266b944ac20, 0x00000266b9451160, 0x00000266b944b640, 0x00000266b944c060,
0x00000266b9450740, 0x00000266b9451670, 0x00000266b944e3d0, 0x00000266b944c570,
0x00000266b944f810, 0x00000266b9451b80, 0x00000266b944bb50, 0x00000266b944ca80,
0x00000266b944a710, 0x00000266b944dec0, 0x00000266b9450230, 0x00000266b2fa4ad0,
0x00000266b2fa25a0, 0x00000266b2fa2af0, 0x00000266b2fa7aa0, 0x00000266b944d9b0,
0x00000266b944e8e0, 0x00000266b411bff0, 0x00000266b9adf420, 0x00000266b9ae26c0,
0x00000266b9ae5450, 0x00000266b9ae5960, 0x00000266b9ae4010, 0x00000266b9ae4520,
0x00000266b9ae5e70, 0x00000266b9ae0350, 0x00000266b9ae30e0, 0x00000266b9ae4f40,
0x00000266b9ae21b0, 0x00000266b9ae2bd0, 0x00000266b9ae35f0, 0x00000266b9ae3b00,
0x00000266b9ae4a30, 0x00000266b9ae6380, 0x00000266b9adef10, 0x00000266b9ae6890,
0x00000266b9adf930, 0x00000266b9adfe40, 0x00000266b9ae0860, 0x00000266b9ae0d70,
0x00000266b9ae1280, 0x00000266b9ae1790, 0x00000266b9ae1ca0, 0x00000266bf99adf0,
0x00000266bf99e090, 0x00000266bf999ec0, 0x00000266bf99bd20, 0x00000266bf9a0e20,
0x00000266bf9a1330, 0x00000266bf9a0910, 0x00000266bf99d160, 0x00000266bf9a1840,
0x00000266bf99e5a0, 0x00000266bf99c230, 0x00000266bf99b300, 0x00000266bf99eab0,
0x00000266bf99b810, 0x00000266bf9a0400, 0x00000266bf99a3d0, 0x00000266bf99db80,
0x00000266bf99efc0, 0x00000266bf99c740, 0x00000266bf99a8e0, 0x00000266bf9a3190,
0x00000266bf99f9e0, 0x00000266bf99f4d0, 0x00000266bf9a2260, 0x00000266bf99fef0,
0x00000266bf9a36a0, 0x00000266bf9a1d50, 0x00000266bf9a2770, 0x00000266bf9a2c80,
0x00000266bf99cc50, 0x00000266bf9a3bb0
}

Java Threads: ( => current thread )
  0x00000266f36c23c0 JavaThread "main" [_thread_blocked, id=16756, stack(0x000000f1b8400000,0x000000f1b8500000)]
  0x00000266ff666bd0 JavaThread "Reference Handler" daemon [_thread_blocked, id=20944, stack(0x000000f1b8b00000,0x000000f1b8c00000)]
  0x00000266ff667960 JavaThread "Finalizer" daemon [_thread_blocked, id=7880, stack(0x000000f1b8c00000,0x000000f1b8d00000)]
  0x00000266ff676060 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=18820, stack(0x000000f1b8d00000,0x000000f1b8e00000)]
  0x00000266ff677940 JavaThread "Attach Listener" daemon [_thread_blocked, id=16632, stack(0x000000f1b8e00000,0x000000f1b8f00000)]
  0x00000266ff67a220 JavaThread "Service Thread" daemon [_thread_blocked, id=18736, stack(0x000000f1b8f00000,0x000000f1b9000000)]
  0x00000266ff67cf00 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=1268, stack(0x000000f1b9000000,0x000000f1b9100000)]
  0x00000266ff6812c0 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=24164, stack(0x000000f1b9100000,0x000000f1b9200000)]
  0x00000266ff685390 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=8776, stack(0x000000f1b9200000,0x000000f1b9300000)]
  0x00000266ff68ac10 JavaThread "Sweeper thread" daemon [_thread_blocked, id=25276, stack(0x000000f1b9300000,0x000000f1b9400000)]
  0x00000266ff7c4660 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=10984, stack(0x000000f1b9500000,0x000000f1b9600000)]
  0x00000266ff89a7a0 JavaThread "Notification Thread" daemon [_thread_blocked, id=19592, stack(0x000000f1b9800000,0x000000f1b9900000)]
  0x00000266b39189b0 JavaThread "Daemon health stats" [_thread_blocked, id=18940, stack(0x000000f1ba300000,0x000000f1ba400000)]
  0x00000266b37c6700 JavaThread "Incoming local TCP Connector on port 60633" [_thread_in_native, id=12064, stack(0x000000f1ba400000,0x000000f1ba500000)]
  0x00000266b31bcf00 JavaThread "Daemon periodic checks" [_thread_blocked, id=14752, stack(0x000000f1ba500000,0x000000f1ba600000)]
  0x00000266b3fe75e0 JavaThread "Daemon" [_thread_blocked, id=11564, stack(0x000000f1ba600000,0x000000f1ba700000)]
  0x00000266b42b5470 JavaThread "Handler for socket connection from /127.0.0.1:60633 to /127.0.0.1:60634" [_thread_in_native, id=12196, stack(0x000000f1ba700000,0x000000f1ba800000)]
  0x00000266b411b0c0 JavaThread "Cancel handler" [_thread_blocked, id=27548, stack(0x000000f1ba800000,0x000000f1ba900000)]
  0x00000266b411b5d0 JavaThread "Daemon worker" [_thread_blocked, id=19112, stack(0x000000f1ba900000,0x000000f1baa00000)]
  0x00000266b411d430 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:60633 to /127.0.0.1:60634" [_thread_blocked, id=24900, stack(0x000000f1baa00000,0x000000f1bab00000)]
  0x00000266b411e870 JavaThread "Stdin handler" [_thread_blocked, id=28364, stack(0x000000f1bab00000,0x000000f1bac00000)]
  0x00000266b411bae0 JavaThread "Daemon client event forwarder" [_thread_blocked, id=28604, stack(0x000000f1bac00000,0x000000f1bad00000)]
  0x00000266b411c500 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=4072, stack(0x000000f1bad00000,0x000000f1bae00000)]
  0x00000266b411ca10 JavaThread "File lock request listener" [_thread_in_native, id=28620, stack(0x000000f1bae00000,0x000000f1baf00000)]
  0x00000266b411cf20 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.3\fileHashes)" [_thread_blocked, id=23904, stack(0x000000f1baf00000,0x000000f1bb000000)]
  0x00000266b411d940 JavaThread "File watcher server" daemon [_thread_in_native, id=18504, stack(0x000000f1bb000000,0x000000f1bb100000)]
  0x00000266b411de50 JavaThread "File watcher consumer" daemon [_thread_blocked, id=8992, stack(0x000000f1bb100000,0x000000f1bb200000)]
  0x00000266b411e360 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\Github\KhelSportsPlayerApp\android\.gradle\8.3\checksums)" [_thread_blocked, id=24740, stack(0x000000f1bb200000,0x000000f1bb300000)]
  0x00000266b4a1a360 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\Github\KhelSportsPlayerApp\android\.gradle\8.3\fileHashes)" [_thread_blocked, id=13280, stack(0x000000f1bb300000,0x000000f1bb400000)]
  0x00000266b4a14d50 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.3\md-supplier)" [_thread_blocked, id=19872, stack(0x000000f1bb400000,0x000000f1bb500000)]
  0x00000266b4a19e50 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.3\fileContent)" [_thread_blocked, id=7480, stack(0x000000f1bb500000,0x000000f1bb600000)]
  0x00000266b4a1a870 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.3\md-rule)" [_thread_blocked, id=28120, stack(0x000000f1b9c00000,0x000000f1b9d00000)]
  0x00000266b4a15260 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\.gradle\caches\8.3\executionHistory)" [_thread_blocked, id=6732, stack(0x000000f1bbf00000,0x000000f1bc000000)]
  0x00000266b4a1ad80 JavaThread "jar transforms" [_thread_blocked, id=14780, stack(0x000000f1bc100000,0x000000f1bc200000)]
  0x00000266b4a15c80 JavaThread "jar transforms Thread 2" [_thread_blocked, id=11592, stack(0x000000f1bc200000,0x000000f1bc300000)]
  0x00000266b4a19430 JavaThread "jar transforms Thread 3" [_thread_blocked, id=2124, stack(0x000000f1bc300000,0x000000f1bc400000)]
  0x00000266b4a14840 JavaThread "jar transforms Thread 4" [_thread_blocked, id=12732, stack(0x000000f1bc400000,0x000000f1bc500000)]
  0x00000266b4a15770 JavaThread "Cache worker for kotlin-dsl (C:\Users\<USER>\.gradle\caches\8.3\kotlin-dsl)" [_thread_blocked, id=22220, stack(0x000000f1b9b00000,0x000000f1b9c00000)]
  0x00000266b4a16190 JavaThread "jar transforms Thread 5" [_thread_blocked, id=27376, stack(0x000000f1b9d00000,0x000000f1b9e00000)]
  0x00000266b4a18f20 JavaThread "jar transforms Thread 6" [_thread_blocked, id=12216, stack(0x000000f1bc000000,0x000000f1bc100000)]
  0x00000266b4a166a0 JavaThread "Unconstrained build operations" [_thread_blocked, id=19136, stack(0x000000f1b9700000,0x000000f1b9800000)]
  0x00000266b4a170c0 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=3764, stack(0x000000f1b9a00000,0x000000f1b9b00000)]
  0x00000266b4a1b290 JavaThread "jar transforms Thread 7" [_thread_blocked, id=18952, stack(0x000000f1bc500000,0x000000f1bc600000)]
  0x00000266b4a19940 JavaThread "jar transforms Thread 8" [_thread_blocked, id=20056, stack(0x000000f1bc600000,0x000000f1bc700000)]
  0x00000266b4a13e20 JavaThread "jar transforms Thread 9" [_thread_blocked, id=6376, stack(0x000000f1bc800000,0x000000f1bc900000)]
  0x00000266b4a1b7a0 JavaThread "jar transforms Thread 10" [_thread_blocked, id=17664, stack(0x000000f1bc900000,0x000000f1bca00000)]
  0x00000266b4a17ff0 JavaThread "jar transforms Thread 11" [_thread_blocked, id=10072, stack(0x000000f1bcc00000,0x000000f1bcd00000)]
  0x00000266b4a14330 JavaThread "jar transforms Thread 12" [_thread_blocked, id=20424, stack(0x000000f1bcd00000,0x000000f1bce00000)]
  0x00000266b4a175d0 JavaThread "Cache worker for dependencies-accessors (C:\Users\<USER>\Github\KhelSportsPlayerApp\android\.gradle\8.3\dependencies-accessors)" [_thread_blocked, id=28256, stack(0x000000f1bce00000,0x000000f1bcf00000)]
  0x00000266b4a18500 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\Github\KhelSportsPlayerApp\node_modules\@react-native\gradle-plugin\.gradle\buildOutputCleanup)" [_thread_blocked, id=6628, stack(0x000000f1bd000000,0x000000f1bd100000)]
  0x00000266b3076fc0 JavaThread "jar transforms Thread 13" [_thread_blocked, id=22152, stack(0x000000f1bd100000,0x000000f1bd200000)]
  0x00000266b3075160 JavaThread "jar transforms Thread 14" [_thread_blocked, id=13192, stack(0x000000f1bd200000,0x000000f1bd300000)]
  0x00000266b3076ab0 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=25148, stack(0x000000f1bd300000,0x000000f1bd400000)]
  0x00000266b30774d0 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=12652, stack(0x000000f1bd400000,0x000000f1bd500000)]
  0x00000266b3075670 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=9184, stack(0x000000f1bd500000,0x000000f1bd600000)]
  0x00000266b3074c50 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=18644, stack(0x000000f1bd600000,0x000000f1bd700000)]
  0x00000266b3075b80 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=13700, stack(0x000000f1bd700000,0x000000f1bd800000)]
  0x00000266b30765a0 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=19488, stack(0x000000f1bd800000,0x000000f1bd900000)]
  0x00000266b3070060 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=1752, stack(0x000000f1bd900000,0x000000f1bda00000)]
  0x00000266b30779e0 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=25944, stack(0x000000f1bda00000,0x000000f1bdb00000)]
  0x00000266b3074230 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=25740, stack(0x000000f1bdb00000,0x000000f1bdc00000)]
  0x00000266b3074740 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=21220, stack(0x000000f1bdc00000,0x000000f1bdd00000)]
  0x00000266b3073d20 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=26700, stack(0x000000f1bdd00000,0x000000f1bde00000)]
  0x00000266b30714a0 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=28448, stack(0x000000f1bde00000,0x000000f1bdf00000)]
  0x00000266b3070570 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=8060, stack(0x000000f1bdf00000,0x000000f1be000000)]
  0x00000266b3070a80 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=23976, stack(0x000000f1be000000,0x000000f1be100000)]
  0x00000266b3070f90 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=10884, stack(0x000000f1be100000,0x000000f1be200000)]
  0x00000266b3073810 JavaThread "jar transforms Thread 15" [_thread_blocked, id=24428, stack(0x000000f1be200000,0x000000f1be300000)]
  0x00000266b30719b0 JavaThread "jar transforms Thread 16" [_thread_blocked, id=3480, stack(0x000000f1be300000,0x000000f1be400000)]
  0x00000266b3071ec0 JavaThread "Memory manager" [_thread_blocked, id=24400, stack(0x000000f1be400000,0x000000f1be500000)]
  0x00000266b30728e0 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\Github\KhelSportsPlayerApp\android\.gradle\buildOutputCleanup)" [_thread_blocked, id=22796, stack(0x000000f1be500000,0x000000f1be600000)]
  0x00000266b3072df0 JavaThread "ForkJoinPool.commonPool-worker-1" daemon [_thread_blocked, id=2668, stack(0x000000f1be600000,0x000000f1be700000)]
  0x00000266b3073300 JavaThread "ForkJoinPool.commonPool-worker-2" daemon [_thread_blocked, id=22412, stack(0x000000f1be700000,0x000000f1be800000)]
  0x00000266b7670060 JavaThread "ForkJoinPool.commonPool-worker-3" daemon [_thread_blocked, id=10088, stack(0x000000f1be800000,0x000000f1be900000)]
  0x00000266b766c3a0 JavaThread "ForkJoinPool.commonPool-worker-4" daemon [_thread_blocked, id=15304, stack(0x000000f1be900000,0x000000f1bea00000)]
  0x00000266b766f640 JavaThread "ForkJoinPool.commonPool-worker-5" daemon [_thread_blocked, id=25696, stack(0x000000f1bea00000,0x000000f1beb00000)]
  0x00000266b7670f90 JavaThread "ForkJoinPool.commonPool-worker-6" daemon [_thread_blocked, id=22244, stack(0x000000f1beb00000,0x000000f1bec00000)]
  0x00000266b766f130 JavaThread "ForkJoinPool.commonPool-worker-7" daemon [_thread_blocked, id=18792, stack(0x000000f1bec00000,0x000000f1bed00000)]
  0x00000266b7670a80 JavaThread "ForkJoinPool.commonPool-worker-8" daemon [_thread_blocked, id=3508, stack(0x000000f1bed00000,0x000000f1bee00000)]
  0x00000266b7671ec0 JavaThread "ForkJoinPool.commonPool-worker-9" daemon [_thread_blocked, id=26076, stack(0x000000f1bee00000,0x000000f1bef00000)]
  0x00000266b766d7e0 JavaThread "ForkJoinPool.commonPool-worker-10" daemon [_thread_blocked, id=24352, stack(0x000000f1bef00000,0x000000f1bf000000)]
  0x00000266b766dcf0 JavaThread "ForkJoinPool.commonPool-worker-11" daemon [_thread_blocked, id=13260, stack(0x000000f1bf000000,0x000000f1bf100000)]
  0x00000266b76728e0 JavaThread "included builds" [_thread_blocked, id=23156, stack(0x000000f1bf200000,0x000000f1bf300000)]
  0x00000266b76714a0 JavaThread "Execution worker" [_thread_blocked, id=5336, stack(0x000000f1bf300000,0x000000f1bf400000)]
  0x00000266b76723d0 JavaThread "Execution worker Thread 2" [_thread_blocked, id=27892, stack(0x000000f1bf400000,0x000000f1bf500000)]
  0x00000266b7672df0 JavaThread "Execution worker Thread 3" [_thread_blocked, id=10196, stack(0x000000f1bf500000,0x000000f1bf600000)]
  0x00000266b766fb50 JavaThread "Execution worker Thread 4" [_thread_blocked, id=2208, stack(0x000000f1bf600000,0x000000f1bf700000)]
  0x00000266b7673300 JavaThread "Execution worker Thread 5" [_thread_blocked, id=12172, stack(0x000000f1bf700000,0x000000f1bf800000)]
  0x00000266b766be90 JavaThread "Execution worker Thread 6" [_thread_blocked, id=8028, stack(0x000000f1bf800000,0x000000f1bf900000)]
  0x00000266b766ec20 JavaThread "Execution worker Thread 7" [_thread_blocked, id=16520, stack(0x000000f1bf900000,0x000000f1bfa00000)]
  0x00000266b7673810 JavaThread "Execution worker Thread 8" [_thread_blocked, id=18924, stack(0x000000f1bfa00000,0x000000f1bfb00000)]
  0x00000266b766c8b0 JavaThread "Execution worker Thread 9" [_thread_blocked, id=15224, stack(0x000000f1bfb00000,0x000000f1bfc00000)]
  0x00000266b766cdc0 JavaThread "Execution worker Thread 10" [_thread_blocked, id=25504, stack(0x000000f1bfc00000,0x000000f1bfd00000)]
  0x00000266b7670570 JavaThread "Execution worker Thread 11" [_thread_blocked, id=13976, stack(0x000000f1bfd00000,0x000000f1bfe00000)]
  0x00000266b76719b0 JavaThread "Execution worker Thread 12" [_thread_blocked, id=8752, stack(0x000000f1bfe00000,0x000000f1bff00000)]
  0x00000266b766e200 JavaThread "Execution worker Thread 13" [_thread_blocked, id=11344, stack(0x000000f1bff00000,0x000000f1c0000000)]
  0x00000266b766d2d0 JavaThread "Execution worker Thread 14" [_thread_blocked, id=3372, stack(0x000000f1c0000000,0x000000f1c0100000)]
  0x00000266b766e710 JavaThread "Execution worker Thread 15" [_thread_blocked, id=896, stack(0x000000f1c0100000,0x000000f1c0200000)]
  0x00000266b3be57c0 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\Github\KhelSportsPlayerApp\node_modules\@react-native\gradle-plugin\.gradle\8.3\executionHistory)" [_thread_blocked, id=12240, stack(0x000000f1c0200000,0x000000f1c0300000)]
  0x00000266b3be01b0 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=20364, stack(0x000000f1c0300000,0x000000f1c0400000)]
  0x00000266b3be2520 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=17828, stack(0x000000f1c0400000,0x000000f1c0500000)]
  0x00000266b3be5cd0 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=24536, stack(0x000000f1c0500000,0x000000f1c0600000)]
  0x00000266b3be4890 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=20776, stack(0x000000f1c0600000,0x000000f1c0700000)]
  0x00000266b3be3960 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=19336, stack(0x000000f1c0700000,0x000000f1c0800000)]
  0x00000266b3be4da0 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=9724, stack(0x000000f1c0800000,0x000000f1c0900000)]
  0x00000266b3be52b0 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=2276, stack(0x000000f1c0900000,0x000000f1c0a00000)]
  0x00000266b3be06c0 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=28312, stack(0x000000f1c0a00000,0x000000f1c0b00000)]
  0x00000266b3be15f0 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=19144, stack(0x000000f1c0b00000,0x000000f1c0c00000)]
  0x00000266b3bde860 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=13828, stack(0x000000f1c0c00000,0x000000f1c0d00000)]
  0x00000266b3be4380 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=2996, stack(0x000000f1c0d00000,0x000000f1c0e00000)]
  0x00000266b3be61e0 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=24624, stack(0x000000f1c0e00000,0x000000f1c0f00000)]
  0x00000266b3be2010 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=8780, stack(0x000000f1c0f00000,0x000000f1c1000000)]
  0x00000266b3be2a30 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=25008, stack(0x000000f1c1000000,0x000000f1c1100000)]
  0x00000266b3be2f40 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=16548, stack(0x000000f1c1100000,0x000000f1c1200000)]
  0x00000266b3bded70 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=21272, stack(0x000000f1c1200000,0x000000f1c1300000)]
  0x00000266b3bdf280 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=10380, stack(0x000000f1c1300000,0x000000f1c1400000)]
  0x00000266b3bdf790 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=16876, stack(0x000000f1c1400000,0x000000f1c1500000)]
  0x00000266b3bdfca0 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=18584, stack(0x000000f1c1500000,0x000000f1c1600000)]
  0x00000266b3be0bd0 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=27344, stack(0x000000f1c1600000,0x000000f1c1700000)]
  0x00000266b3be3450 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=26860, stack(0x000000f1c1700000,0x000000f1c1800000)]
  0x00000266b3be10e0 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=9844, stack(0x000000f1c1800000,0x000000f1c1900000)]
  0x00000266b3be1b00 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=10696, stack(0x000000f1c1900000,0x000000f1c1a00000)]
  0x00000266b3be3e70 JavaThread "Unconstrained build operations Thread 41" [_thread_blocked, id=17736, stack(0x000000f1c1a00000,0x000000f1c1b00000)]
  0x00000266b7dd5670 JavaThread "Unconstrained build operations Thread 42" [_thread_blocked, id=14072, stack(0x000000f1c1b00000,0x000000f1c1c00000)]
  0x00000266b7dd0570 JavaThread "Unconstrained build operations Thread 43" [_thread_blocked, id=13624, stack(0x000000f1c1c00000,0x000000f1c1d00000)]
  0x00000266b7dd3810 JavaThread "Unconstrained build operations Thread 44" [_thread_blocked, id=15924, stack(0x000000f1c1d00000,0x000000f1c1e00000)]
  0x00000266b7dcfb50 JavaThread "Unconstrained build operations Thread 45" [_thread_blocked, id=20820, stack(0x000000f1c1e00000,0x000000f1c1f00000)]
  0x00000266b7dd4c50 JavaThread "Unconstrained build operations Thread 46" [_thread_blocked, id=5452, stack(0x000000f1c1f00000,0x000000f1c2000000)]
  0x00000266b7dd6fc0 JavaThread "Unconstrained build operations Thread 47" [_thread_blocked, id=8312, stack(0x000000f1c2000000,0x000000f1c2100000)]
  0x00000266b7dd3d20 JavaThread "Unconstrained build operations Thread 48" [_thread_blocked, id=23420, stack(0x000000f1c2100000,0x000000f1c2200000)]
  0x00000266b7dd5160 JavaThread "Unconstrained build operations Thread 49" [_thread_blocked, id=7536, stack(0x000000f1c2200000,0x000000f1c2300000)]
  0x00000266b2fa3040 JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=15336, stack(0x000000f1bca00000,0x000000f1bcb00000)]
  0x00000266b2fa1b00 JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=14088, stack(0x000000f1b9400000,0x000000f1b9500000)]
  0x00000266b2fa6560 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=22276, stack(0x000000f1b9600000,0x000000f1b9700000)]
  0x00000266b2fa15b0 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=9812, stack(0x000000f1bcb00000,0x000000f1bcc00000)]
  0x00000266b7dd0060 JavaThread "Unconstrained build operations Thread 50" [_thread_blocked, id=9324, stack(0x000000f1bf100000,0x000000f1bf200000)]
  0x00000266b2fa6010 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=3024, stack(0x000000f1c2300000,0x000000f1c2400000)]
  0x00000266b7dd3300 JavaThread "Unconstrained build operations Thread 51" [_thread_blocked, id=5700, stack(0x000000f1c2400000,0x000000f1c2500000)]
  0x00000266b7dd0f90 JavaThread "Unconstrained build operations Thread 52" [_thread_blocked, id=9200, stack(0x000000f1c2500000,0x000000f1c2600000)]
  0x00000266b7dd4740 JavaThread "Unconstrained build operations Thread 53" [_thread_blocked, id=15732, stack(0x000000f1c2600000,0x000000f1c2700000)]
  0x00000266b7dd14a0 JavaThread "Unconstrained build operations Thread 54" [_thread_blocked, id=26344, stack(0x000000f1c2700000,0x000000f1c2800000)]
  0x00000266b7dd19b0 JavaThread "Unconstrained build operations Thread 55" [_thread_blocked, id=27056, stack(0x000000f1c2800000,0x000000f1c2900000)]
  0x00000266b7dd23d0 JavaThread "Unconstrained build operations Thread 56" [_thread_blocked, id=24024, stack(0x000000f1c2900000,0x000000f1c2a00000)]
  0x00000266b7dd28e0 JavaThread "Unconstrained build operations Thread 57" [_thread_blocked, id=7276, stack(0x000000f1c2a00000,0x000000f1c2b00000)]
  0x00000266b6060f40 JavaThread "Unconstrained build operations Thread 58" [_thread_blocked, id=7188, stack(0x000000f1c2b00000,0x000000f1c2c00000)]
  0x00000266b6060010 JavaThread "Unconstrained build operations Thread 59" [_thread_blocked, id=27024, stack(0x000000f1c2c00000,0x000000f1c2d00000)]
  0x00000266b605be40 JavaThread "Unconstrained build operations Thread 60" [_thread_blocked, id=23932, stack(0x000000f1c2d00000,0x000000f1c2e00000)]
  0x00000266b605ebd0 JavaThread "Unconstrained build operations Thread 61" [_thread_blocked, id=14400, stack(0x000000f1c2e00000,0x000000f1c2f00000)]
  0x00000266b605f0e0 JavaThread "Unconstrained build operations Thread 62" [_thread_blocked, id=15332, stack(0x000000f1c2f00000,0x000000f1c3000000)]
  0x00000266b605cd70 JavaThread "Unconstrained build operations Thread 63" [_thread_blocked, id=14148, stack(0x000000f1c3000000,0x000000f1c3100000)]
  0x00000266b605d790 JavaThread "Unconstrained build operations Thread 64" [_thread_blocked, id=5804, stack(0x000000f1c3100000,0x000000f1c3200000)]
  0x00000266b6061450 JavaThread "Unconstrained build operations Thread 65" [_thread_blocked, id=5956, stack(0x000000f1c3200000,0x000000f1c3300000)]
=>0x00000266b2fa8a90 JavaThread "C2 CompilerThread3" daemon [_thread_in_native, id=2856, stack(0x000000f1c3a00000,0x000000f1c3b00000)]
  0x00000266b605d280 JavaThread "Unconstrained build operations Thread 66" [_thread_blocked, id=27184, stack(0x000000f1c3b00000,0x000000f1c3c00000)]
  0x00000266b6060a30 JavaThread "Unconstrained build operations Thread 67" [_thread_blocked, id=22704, stack(0x000000f1c3c00000,0x000000f1c3d00000)]
  0x00000266b6061960 JavaThread "Unconstrained build operations Thread 68" [_thread_blocked, id=18984, stack(0x000000f1c3d00000,0x000000f1c3e00000)]
  0x00000266b6061e70 JavaThread "Unconstrained build operations Thread 69" [_thread_blocked, id=26920, stack(0x000000f1c3e00000,0x000000f1c3f00000)]
  0x00000266b605aa00 JavaThread "Unconstrained build operations Thread 70" [_thread_blocked, id=12628, stack(0x000000f1c3f00000,0x000000f1c4000000)]
  0x00000266b6062380 JavaThread "Unconstrained build operations Thread 71" [_thread_blocked, id=23888, stack(0x000000f1c4000000,0x000000f1c4100000)]
  0x00000266b605e1b0 JavaThread "Unconstrained build operations Thread 72" [_thread_blocked, id=21544, stack(0x000000f1c4100000,0x000000f1c4200000)]
  0x00000266b605af10 JavaThread "Unconstrained build operations Thread 73" [_thread_blocked, id=28084, stack(0x000000f1c4200000,0x000000f1c4300000)]
  0x00000266b605b420 JavaThread "Unconstrained build operations Thread 74" [_thread_blocked, id=12056, stack(0x000000f1c4300000,0x000000f1c4400000)]
  0x00000266b605f5f0 JavaThread "Unconstrained build operations Thread 75" [_thread_blocked, id=24828, stack(0x000000f1c4400000,0x000000f1c4500000)]
  0x00000266b605b930 JavaThread "Unconstrained build operations Thread 76" [_thread_blocked, id=1664, stack(0x000000f1c4500000,0x000000f1c4600000)]
  0x00000266b605fb00 JavaThread "Unconstrained build operations Thread 77" [_thread_blocked, id=24488, stack(0x000000f1c4600000,0x000000f1c4700000)]
  0x00000266b6060520 JavaThread "Unconstrained build operations Thread 78" [_thread_blocked, id=1012, stack(0x000000f1c4700000,0x000000f1c4800000)]
  0x00000266b605c350 JavaThread "Unconstrained build operations Thread 79" [_thread_blocked, id=22884, stack(0x000000f1c4800000,0x000000f1c4900000)]
  0x00000266b605c860 JavaThread "Unconstrained build operations Thread 80" [_thread_blocked, id=6884, stack(0x000000f1c4900000,0x000000f1c4a00000)]
  0x00000266b944ac20 JavaThread "Unconstrained build operations Thread 81" [_thread_blocked, id=10408, stack(0x000000f1c4a00000,0x000000f1c4b00000)]
  0x00000266b9451160 JavaThread "Unconstrained build operations Thread 82" [_thread_blocked, id=26936, stack(0x000000f1c4b00000,0x000000f1c4c00000)]
  0x00000266b944b640 JavaThread "Unconstrained build operations Thread 83" [_thread_blocked, id=9708, stack(0x000000f1c4c00000,0x000000f1c4d00000)]
  0x00000266b944c060 JavaThread "Unconstrained build operations Thread 84" [_thread_blocked, id=27872, stack(0x000000f1c4d00000,0x000000f1c4e00000)]
  0x00000266b9450740 JavaThread "Unconstrained build operations Thread 85" [_thread_blocked, id=7552, stack(0x000000f1c4e00000,0x000000f1c4f00000)]
  0x00000266b9451670 JavaThread "Unconstrained build operations Thread 86" [_thread_blocked, id=9196, stack(0x000000f1c4f00000,0x000000f1c5000000)]
  0x00000266b944e3d0 JavaThread "Unconstrained build operations Thread 87" [_thread_blocked, id=11196, stack(0x000000f1c5000000,0x000000f1c5100000)]
  0x00000266b944c570 JavaThread "Unconstrained build operations Thread 88" [_thread_blocked, id=24420, stack(0x000000f1c5100000,0x000000f1c5200000)]
  0x00000266b944f810 JavaThread "Unconstrained build operations Thread 89" [_thread_blocked, id=24504, stack(0x000000f1c5200000,0x000000f1c5300000)]
  0x00000266b9451b80 JavaThread "Unconstrained build operations Thread 90" [_thread_blocked, id=10424, stack(0x000000f1c5300000,0x000000f1c5400000)]
  0x00000266b944bb50 JavaThread "Unconstrained build operations Thread 91" [_thread_blocked, id=24264, stack(0x000000f1c5400000,0x000000f1c5500000)]
  0x00000266b944ca80 JavaThread "Unconstrained build operations Thread 92" [_thread_blocked, id=7272, stack(0x000000f1c5500000,0x000000f1c5600000)]
  0x00000266b944a710 JavaThread "Unconstrained build operations Thread 93" [_thread_blocked, id=11596, stack(0x000000f1c5600000,0x000000f1c5700000)]
  0x00000266b944dec0 JavaThread "Unconstrained build operations Thread 94" [_thread_blocked, id=356, stack(0x000000f1c5700000,0x000000f1c5800000)]
  0x00000266b9450230 JavaThread "Unconstrained build operations Thread 95" [_thread_blocked, id=5484, stack(0x000000f1c5800000,0x000000f1c5900000)]
  0x00000266b2fa4ad0 JavaThread "C2 CompilerThread4" daemon [_thread_in_native, id=12884, stack(0x000000f1c5900000,0x000000f1c5a00000)]
  0x00000266b2fa25a0 JavaThread "C2 CompilerThread5" daemon [_thread_in_native, id=13484, stack(0x000000f1c5a00000,0x000000f1c5b00000)]
  0x00000266b2fa2af0 JavaThread "C2 CompilerThread6" daemon [_thread_in_native, id=19892, stack(0x000000f1c5b00000,0x000000f1c5c00000)]
  0x00000266b2fa7aa0 JavaThread "C2 CompilerThread7" daemon [_thread_in_native, id=16308, stack(0x000000f1c5c00000,0x000000f1c5d00000)]
  0x00000266b944d9b0 JavaThread "Unconstrained build operations Thread 96" [_thread_blocked, id=16904, stack(0x000000f1c5d00000,0x000000f1c5e00000)]
  0x00000266b944e8e0 JavaThread "Unconstrained build operations Thread 97" [_thread_blocked, id=22832, stack(0x000000f1c5e00000,0x000000f1c5f00000)]
  0x00000266b411bff0 JavaThread "Unconstrained build operations Thread 98" [_thread_blocked, id=15880, stack(0x000000f1c5f00000,0x000000f1c6000000)]
  0x00000266b9adf420 JavaThread "Unconstrained build operations Thread 99" [_thread_blocked, id=1728, stack(0x000000f1c6000000,0x000000f1c6100000)]
  0x00000266b9ae26c0 JavaThread "Unconstrained build operations Thread 100" [_thread_blocked, id=11008, stack(0x000000f1c6100000,0x000000f1c6200000)]
  0x00000266b9ae5450 JavaThread "Unconstrained build operations Thread 101" [_thread_blocked, id=9828, stack(0x000000f1c6200000,0x000000f1c6300000)]
  0x00000266b9ae5960 JavaThread "Unconstrained build operations Thread 102" [_thread_blocked, id=16012, stack(0x000000f1c6300000,0x000000f1c6400000)]
  0x00000266b9ae4010 JavaThread "Unconstrained build operations Thread 103" [_thread_blocked, id=13744, stack(0x000000f1c6400000,0x000000f1c6500000)]
  0x00000266b9ae4520 JavaThread "Unconstrained build operations Thread 104" [_thread_blocked, id=25112, stack(0x000000f1c6500000,0x000000f1c6600000)]
  0x00000266b9ae5e70 JavaThread "Unconstrained build operations Thread 105" [_thread_blocked, id=24672, stack(0x000000f1c6600000,0x000000f1c6700000)]
  0x00000266b9ae0350 JavaThread "Unconstrained build operations Thread 106" [_thread_blocked, id=28508, stack(0x000000f1c6700000,0x000000f1c6800000)]
  0x00000266b9ae30e0 JavaThread "Unconstrained build operations Thread 107" [_thread_blocked, id=16196, stack(0x000000f1c6800000,0x000000f1c6900000)]
  0x00000266b9ae4f40 JavaThread "Unconstrained build operations Thread 108" [_thread_blocked, id=27800, stack(0x000000f1c6900000,0x000000f1c6a00000)]
  0x00000266b9ae21b0 JavaThread "Unconstrained build operations Thread 109" [_thread_blocked, id=13736, stack(0x000000f1c6a00000,0x000000f1c6b00000)]
  0x00000266b9ae2bd0 JavaThread "Unconstrained build operations Thread 110" [_thread_blocked, id=21716, stack(0x000000f1c6b00000,0x000000f1c6c00000)]
  0x00000266b9ae35f0 JavaThread "Unconstrained build operations Thread 111" [_thread_blocked, id=22368, stack(0x000000f1c6c00000,0x000000f1c6d00000)]
  0x00000266b9ae3b00 JavaThread "Unconstrained build operations Thread 112" [_thread_blocked, id=27524, stack(0x000000f1c6d00000,0x000000f1c6e00000)]
  0x00000266b9ae4a30 JavaThread "Unconstrained build operations Thread 113" [_thread_blocked, id=6904, stack(0x000000f1c6e00000,0x000000f1c6f00000)]
  0x00000266b9ae6380 JavaThread "Unconstrained build operations Thread 114" [_thread_blocked, id=7908, stack(0x000000f1c6f00000,0x000000f1c7000000)]
  0x00000266b9adef10 JavaThread "Unconstrained build operations Thread 115" [_thread_blocked, id=10176, stack(0x000000f1c7000000,0x000000f1c7100000)]
  0x00000266b9ae6890 JavaThread "Unconstrained build operations Thread 116" [_thread_blocked, id=21872, stack(0x000000f1c7100000,0x000000f1c7200000)]
  0x00000266b9adf930 JavaThread "Unconstrained build operations Thread 117" [_thread_blocked, id=13316, stack(0x000000f1c7200000,0x000000f1c7300000)]
  0x00000266b9adfe40 JavaThread "Unconstrained build operations Thread 118" [_thread_blocked, id=12332, stack(0x000000f1c7300000,0x000000f1c7400000)]
  0x00000266b9ae0860 JavaThread "Unconstrained build operations Thread 119" [_thread_blocked, id=19460, stack(0x000000f1c7400000,0x000000f1c7500000)]
  0x00000266b9ae0d70 JavaThread "Unconstrained build operations Thread 120" [_thread_blocked, id=17376, stack(0x000000f1c7500000,0x000000f1c7600000)]
  0x00000266b9ae1280 JavaThread "Unconstrained build operations Thread 121" [_thread_blocked, id=14016, stack(0x000000f1c7600000,0x000000f1c7700000)]
  0x00000266b9ae1790 JavaThread "Unconstrained build operations Thread 122" [_thread_blocked, id=16116, stack(0x000000f1c7700000,0x000000f1c7800000)]
  0x00000266b9ae1ca0 JavaThread "Unconstrained build operations Thread 123" [_thread_blocked, id=21356, stack(0x000000f1c7800000,0x000000f1c7900000)]
  0x00000266bf99adf0 JavaThread "Unconstrained build operations Thread 124" [_thread_blocked, id=17800, stack(0x000000f1c7900000,0x000000f1c7a00000)]
  0x00000266bf99e090 JavaThread "Unconstrained build operations Thread 125" [_thread_blocked, id=21216, stack(0x000000f1c7a00000,0x000000f1c7b00000)]
  0x00000266bf999ec0 JavaThread "Unconstrained build operations Thread 126" [_thread_blocked, id=26160, stack(0x000000f1c7b00000,0x000000f1c7c00000)]
  0x00000266bf99bd20 JavaThread "Unconstrained build operations Thread 127" [_thread_blocked, id=13772, stack(0x000000f1c7c00000,0x000000f1c7d00000)]
  0x00000266bf9a0e20 JavaThread "Unconstrained build operations Thread 128" [_thread_blocked, id=10856, stack(0x000000f1c7d00000,0x000000f1c7e00000)]
  0x00000266bf9a1330 JavaThread "Unconstrained build operations Thread 129" [_thread_blocked, id=22748, stack(0x000000f1c7e00000,0x000000f1c7f00000)]
  0x00000266bf9a0910 JavaThread "Unconstrained build operations Thread 130" [_thread_blocked, id=12620, stack(0x000000f1c7f00000,0x000000f1c8000000)]
  0x00000266bf99d160 JavaThread "Unconstrained build operations Thread 131" [_thread_blocked, id=19768, stack(0x000000f1c8200000,0x000000f1c8300000)]
  0x00000266bf9a1840 JavaThread "Unconstrained build operations Thread 132" [_thread_blocked, id=17240, stack(0x000000f1c8300000,0x000000f1c8400000)]
  0x00000266bf99e5a0 JavaThread "Unconstrained build operations Thread 133" [_thread_blocked, id=4708, stack(0x000000f1c8400000,0x000000f1c8500000)]
  0x00000266bf99c230 JavaThread "Unconstrained build operations Thread 134" [_thread_blocked, id=15040, stack(0x000000f1c8500000,0x000000f1c8600000)]
  0x00000266bf99b300 JavaThread "Unconstrained build operations Thread 135" [_thread_blocked, id=18880, stack(0x000000f1c8600000,0x000000f1c8700000)]
  0x00000266bf99eab0 JavaThread "Unconstrained build operations Thread 136" [_thread_blocked, id=15648, stack(0x000000f1c8700000,0x000000f1c8800000)]
  0x00000266bf99b810 JavaThread "Unconstrained build operations Thread 137" [_thread_blocked, id=20148, stack(0x000000f1c8800000,0x000000f1c8900000)]
  0x00000266bf9a0400 JavaThread "Unconstrained build operations Thread 138" [_thread_blocked, id=20508, stack(0x000000f1c8900000,0x000000f1c8a00000)]
  0x00000266bf99a3d0 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\Github\KhelSportsPlayerApp\android\.gradle\8.3\executionHistory)" [_thread_blocked, id=1640, stack(0x000000f1c8a00000,0x000000f1c8b00000)]
  0x00000266bf99db80 JavaThread "Unconstrained build operations Thread 139" [_thread_blocked, id=20992, stack(0x000000f1c8b00000,0x000000f1c8c00000)]
  0x00000266bf99efc0 JavaThread "Unconstrained build operations Thread 140" [_thread_in_native, id=8280, stack(0x000000f1c8c00000,0x000000f1c8d00000)]
  0x00000266bf99c740 JavaThread "Unconstrained build operations Thread 141" [_thread_blocked, id=10976, stack(0x000000f1c8d00000,0x000000f1c8e00000)]
  0x00000266bf99a8e0 JavaThread "Unconstrained build operations Thread 142" [_thread_blocked, id=20308, stack(0x000000f1c8e00000,0x000000f1c8f00000)]
  0x00000266bf9a3190 JavaThread "Unconstrained build operations Thread 143" [_thread_blocked, id=4536, stack(0x000000f1c8f00000,0x000000f1c9000000)]
  0x00000266bf99f9e0 JavaThread "Unconstrained build operations Thread 144" [_thread_blocked, id=18624, stack(0x000000f1c9000000,0x000000f1c9100000)]
  0x00000266bf99f4d0 JavaThread "Unconstrained build operations Thread 145" [_thread_blocked, id=5600, stack(0x000000f1c9100000,0x000000f1c9200000)]
  0x00000266bf9a2260 JavaThread "Unconstrained build operations Thread 146" [_thread_blocked, id=27360, stack(0x000000f1c9200000,0x000000f1c9300000)]
  0x00000266bf99fef0 JavaThread "Unconstrained build operations Thread 147" [_thread_blocked, id=2608, stack(0x000000f1c9300000,0x000000f1c9400000)]
  0x00000266bf9a36a0 JavaThread "Unconstrained build operations Thread 148" [_thread_blocked, id=28424, stack(0x000000f1c9400000,0x000000f1c9500000)]
  0x00000266bf9a1d50 JavaThread "Unconstrained build operations Thread 149" [_thread_blocked, id=21388, stack(0x000000f1c9500000,0x000000f1c9600000)]
  0x00000266bf9a2770 JavaThread "Unconstrained build operations Thread 150" [_thread_blocked, id=28096, stack(0x000000f1c9600000,0x000000f1c9700000)]
  0x00000266bf9a2c80 JavaThread "Unconstrained build operations Thread 151" [_thread_blocked, id=7656, stack(0x000000f1c9700000,0x000000f1c9800000)]
  0x00000266bf99cc50 JavaThread "Unconstrained build operations Thread 152" [_thread_blocked, id=26220, stack(0x000000f1c9800000,0x000000f1c9900000)]
  0x00000266bf9a3bb0 JavaThread "Unconstrained build operations Thread 153" [_thread_blocked, id=26024, stack(0x000000f1c9900000,0x000000f1c9a00000)]

Other Threads:
  0x00000266ff618c70 VMThread "VM Thread" [stack: 0x000000f1b8a00000,0x000000f1b8b00000] [id=12608]
  0x00000266f36c3150 WatcherThread [stack: 0x000000f1b9900000,0x000000f1b9a00000] [id=24112]
  0x00000266f371e270 GCTaskThread "GC Thread#0" [stack: 0x000000f1b8500000,0x000000f1b8600000] [id=26524]
  0x00000266b3e63570 GCTaskThread "GC Thread#1" [stack: 0x000000f1b9e00000,0x000000f1b9f00000] [id=16900]
  0x00000266b3e63830 GCTaskThread "GC Thread#2" [stack: 0x000000f1b9f00000,0x000000f1ba000000] [id=8100]
  0x00000266b333af10 GCTaskThread "GC Thread#3" [stack: 0x000000f1ba000000,0x000000f1ba100000] [id=18664]
  0x00000266b3e0a010 GCTaskThread "GC Thread#4" [stack: 0x000000f1ba100000,0x000000f1ba200000] [id=16508]
  0x00000266b3e0a2d0 GCTaskThread "GC Thread#5" [stack: 0x000000f1ba200000,0x000000f1ba300000] [id=10860]
  0x00000266b5714220 GCTaskThread "GC Thread#6" [stack: 0x000000f1bb600000,0x000000f1bb700000] [id=15436]
  0x00000266b6cc7cd0 GCTaskThread "GC Thread#7" [stack: 0x000000f1bb700000,0x000000f1bb800000] [id=18888]
  0x00000266b68236f0 GCTaskThread "GC Thread#8" [stack: 0x000000f1bb800000,0x000000f1bb900000] [id=26468]
  0x00000266b605a6f0 GCTaskThread "GC Thread#9" [stack: 0x000000f1bb900000,0x000000f1bba00000] [id=18132]
  0x00000266b6434dc0 GCTaskThread "GC Thread#10" [stack: 0x000000f1bba00000,0x000000f1bbb00000] [id=24804]
  0x00000266b50ce930 GCTaskThread "GC Thread#11" [stack: 0x000000f1bbb00000,0x000000f1bbc00000] [id=4712]
  0x00000266b50cd8b0 GCTaskThread "GC Thread#12" [stack: 0x000000f1bbc00000,0x000000f1bbd00000] [id=17180]
  0x00000266f372f0d0 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000f1b8600000,0x000000f1b8700000] [id=17912]
  0x00000266f372f9f0 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000f1b8700000,0x000000f1b8800000] [id=27516]
  0x00000266b50cebf0 ConcurrentGCThread "G1 Conc#1" [stack: 0x000000f1bbd00000,0x000000f1bbe00000] [id=18236]
  0x00000266b50cd070 ConcurrentGCThread "G1 Conc#2" [stack: 0x000000f1bbe00000,0x000000f1bbf00000] [id=25396]
  0x00000266ff4d3bd0 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000f1b8800000,0x000000f1b8900000] [id=13088]
  0x00000266b536ce60 ConcurrentGCThread "G1 Refine#1" [stack: 0x000000f1bcf00000,0x000000f1bd000000] [id=12488]
  0x00000266b9452730 ConcurrentGCThread "G1 Refine#2" [stack: 0x000000f1c3300000,0x000000f1c3400000] [id=21572]
  0x00000266b9453eb0 ConcurrentGCThread "G1 Refine#3" [stack: 0x000000f1c3400000,0x000000f1c3500000] [id=17336]
  0x00000266b94561f0 ConcurrentGCThread "G1 Refine#4" [stack: 0x000000f1c3500000,0x000000f1c3600000] [id=17436]
  0x00000266b94541a0 ConcurrentGCThread "G1 Refine#5" [stack: 0x000000f1c3600000,0x000000f1c3700000] [id=22852]
  0x00000266b94538d0 ConcurrentGCThread "G1 Refine#6" [stack: 0x000000f1c3700000,0x000000f1c3800000] [id=15432]
  0x00000266b9454d60 ConcurrentGCThread "G1 Refine#7" [stack: 0x000000f1c3800000,0x000000f1c3900000] [id=12408]
  0x00000266b9452a20 ConcurrentGCThread "G1 Refine#8" [stack: 0x000000f1c3900000,0x000000f1c3a00000] [id=17888]
  0x00000266ff4d4510 ConcurrentGCThread "G1 Service" [stack: 0x000000f1b8900000,0x000000f1b8a00000] [id=8508]

Threads with active compile tasks:
C2 CompilerThread0    24259 16225   !   4       java.util.concurrent.locks.AbstractQueuedSynchronizer::acquire (407 bytes)
C2 CompilerThread1    24259 16248       4       java.util.ArrayDeque::addFirst (51 bytes)
C2 CompilerThread2    24259 16231       4       org.gradle.api.internal.changedetection.state.CachingFileHasher::snapshot (122 bytes)
C2 CompilerThread3    24260 16179   !   4       org.gradle.cache.internal.DefaultMultiProcessSafeIndexedCache::getIfPresent (25 bytes)
C2 CompilerThread4    24260 16227       4       org.gradle.cache.internal.ExclusiveCacheAccessingWorker$1::call (10 bytes)
C2 CompilerThread5    24260 16226       4       org.gradle.cache.internal.AsyncCacheAccessDecoratedCache$$Lambda$512/0x000002669447d8d0::create (12 bytes)
C2 CompilerThread6    24260 16230       4       org.gradle.api.internal.changedetection.state.CachingFileHasher$FileInfoSerializer::read (36 bytes)
C2 CompilerThread7    24260 16229       4       org.gradle.api.internal.changedetection.state.CachingFileHasher$FileInfoSerializer::read (6 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000026693000000-0x0000026693bd0000-0x0000026693bd0000), size 12386304, SharedBaseAddress: 0x0000026693000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000026694000000-0x00000266ae000000, reserved size: 436207616
Narrow klass base: 0x0000026693000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 16 total, 16 available
 Memory: 16070M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 252M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 247808K, used 198766K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 36 young (36864K), 3 survivors (3072K)
 Metaspace       used 104117K, committed 105088K, reserved 557056K
  class space    used 14571K, committed 15040K, reserved 425984K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%|HS|  |TAMS 0x0000000080100000, 0x0000000080000000| Complete 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HC|  |TAMS 0x0000000080200000, 0x0000000080100000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080300000, 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HC|  |TAMS 0x0000000080400000, 0x0000000080300000| Complete 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%| O|  |TAMS 0x0000000080500000, 0x0000000080400000| Untracked 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080600000, 0x0000000080500000| Untracked 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080700000, 0x0000000080600000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%| O|  |TAMS 0x0000000080800000, 0x0000000080700000| Untracked 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080900000, 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080a00000, 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%|HS|  |TAMS 0x0000000080a00000, 0x0000000080a00000| Complete 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080c00000, 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080d00000, 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080e00000, 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080f00000, 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000081000000, 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081100000, 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081200000, 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081300000, 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081400000, 0x0000000081300000| Untracked 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081500000, 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081600000, 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%| O|  |TAMS 0x0000000081700000, 0x0000000081600000| Untracked 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%| O|  |TAMS 0x0000000081800000, 0x0000000081700000| Untracked 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%| O|  |TAMS 0x0000000081900000, 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%| O|  |TAMS 0x0000000081a00000, 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%| O|  |TAMS 0x0000000081b00000, 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%| O|  |TAMS 0x0000000081c00000, 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%| O|  |TAMS 0x0000000081d00000, 0x0000000081c00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%| O|  |TAMS 0x0000000081e00000, 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%| O|  |TAMS 0x0000000081f00000, 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%| O|  |TAMS 0x0000000082000000, 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%| O|  |TAMS 0x0000000082100000, 0x0000000082000000| Untracked 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%| O|  |TAMS 0x0000000082200000, 0x0000000082100000| Untracked 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%| O|  |TAMS 0x0000000082300000, 0x0000000082200000| Untracked 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%| O|  |TAMS 0x0000000082400000, 0x0000000082300000| Untracked 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%| O|  |TAMS 0x0000000082500000, 0x0000000082400000| Untracked 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%| O|  |TAMS 0x0000000082600000, 0x0000000082500000| Untracked 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%| O|  |TAMS 0x0000000082700000, 0x0000000082600000| Untracked 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%|HS|  |TAMS 0x0000000082800000, 0x0000000082700000| Complete 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%|HC|  |TAMS 0x0000000082900000, 0x0000000082800000| Complete 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%|HS|  |TAMS 0x0000000082a00000, 0x0000000082900000| Complete 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%|HC|  |TAMS 0x0000000082b00000, 0x0000000082a00000| Complete 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%| O|  |TAMS 0x0000000082c00000, 0x0000000082b00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%| O|  |TAMS 0x0000000082d00000, 0x0000000082c00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%|HS|  |TAMS 0x0000000082e00000, 0x0000000082d00000| Complete 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%|HC|  |TAMS 0x0000000082f00000, 0x0000000082e00000| Complete 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%|HC|  |TAMS 0x0000000083000000, 0x0000000082f00000| Complete 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%|HC|  |TAMS 0x0000000083100000, 0x0000000083000000| Complete 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%| O|  |TAMS 0x0000000083200000, 0x0000000083100000| Untracked 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%| O|  |TAMS 0x0000000083300000, 0x0000000083200000| Untracked 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%| O|  |TAMS 0x0000000083400000, 0x0000000083300000| Untracked 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%| O|  |TAMS 0x0000000083500000, 0x0000000083400000| Untracked 
|  53|0x0000000083500000, 0x0000000083600000, 0x0000000083600000|100%| O|  |TAMS 0x0000000083600000, 0x0000000083500000| Untracked 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%|HS|  |TAMS 0x0000000083700000, 0x0000000083600000| Complete 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%|HC|  |TAMS 0x0000000083800000, 0x0000000083700000| Complete 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%|HC|  |TAMS 0x0000000083900000, 0x0000000083800000| Complete 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%|HC|  |TAMS 0x0000000083a00000, 0x0000000083900000| Complete 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%| O|  |TAMS 0x0000000083b00000, 0x0000000083a00000| Untracked 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%| O|  |TAMS 0x0000000083c00000, 0x0000000083b00000| Untracked 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%| O|  |TAMS 0x0000000083d00000, 0x0000000083c00000| Untracked 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%| O|  |TAMS 0x0000000083e00000, 0x0000000083d00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%| O|  |TAMS 0x0000000083f00000, 0x0000000083e00000| Untracked 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%| O|  |TAMS 0x0000000084000000, 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%| O|  |TAMS 0x0000000084100000, 0x0000000084000000| Untracked 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%| O|  |TAMS 0x0000000084200000, 0x0000000084100000| Untracked 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%| O|  |TAMS 0x0000000084300000, 0x0000000084200000| Untracked 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%| O|  |TAMS 0x0000000084300000, 0x0000000084300000| Untracked 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%|HS|  |TAMS 0x0000000084500000, 0x0000000084400000| Complete 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%|HS|  |TAMS 0x0000000084600000, 0x0000000084500000| Complete 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%|HS|  |TAMS 0x0000000084700000, 0x0000000084600000| Complete 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%| O|  |TAMS 0x0000000084800000, 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%|HS|  |TAMS 0x0000000084900000, 0x0000000084800000| Complete 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%| O|  |TAMS 0x0000000084a00000, 0x0000000084900000| Untracked 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%| O|  |TAMS 0x0000000084b00000, 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%| O|  |TAMS 0x0000000084c00000, 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%| O|  |TAMS 0x0000000084d00000, 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%| O|  |TAMS 0x0000000084e00000, 0x0000000084d00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%| O|  |TAMS 0x0000000084f00000, 0x0000000084e00000| Untracked 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%| O|  |TAMS 0x0000000085000000, 0x0000000084f00000| Untracked 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%| O|  |TAMS 0x0000000085100000, 0x0000000085000000| Untracked 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%| O|  |TAMS 0x0000000085200000, 0x0000000085100000| Untracked 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%| O|  |TAMS 0x0000000085300000, 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%| O|  |TAMS 0x0000000085400000, 0x0000000085300000| Untracked 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%| O|  |TAMS 0x0000000085500000, 0x0000000085400000| Untracked 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| O|  |TAMS 0x000000008556c600, 0x0000000085500000| Untracked 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085600000, 0x0000000085600000| Untracked 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085700000, 0x0000000085700000| Untracked 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085800000, 0x0000000085800000| Untracked 
|  89|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%|HS|  |TAMS 0x0000000085900000, 0x0000000085900000| Complete 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%|HS|  |TAMS 0x0000000085a00000, 0x0000000085a00000| Complete 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%|HC|  |TAMS 0x0000000085b00000, 0x0000000085b00000| Complete 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%| O|  |TAMS 0x0000000085c00000, 0x0000000085c00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085e00000, 0x0000000085e00000|100%| O|  |TAMS 0x0000000085d00000, 0x0000000085d00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%| O|  |TAMS 0x0000000085e00000, 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%| O|  |TAMS 0x0000000085f00000, 0x0000000085f00000| Untracked 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%| O|  |TAMS 0x0000000086000000, 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x00000000861ffff8, 0x0000000086200000| 99%| O|  |TAMS 0x0000000086100000, 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%| O|  |TAMS 0x0000000086200000, 0x0000000086200000| Untracked 
|  99|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%| O|  |TAMS 0x0000000086300000, 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%| O|  |TAMS 0x0000000086400000, 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%| O|  |TAMS 0x0000000086500000, 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x0000000086700000, 0x0000000086700000|100%| O|  |TAMS 0x0000000086600000, 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%| O|  |TAMS 0x0000000086700000, 0x0000000086700000| Untracked 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%| O|  |TAMS 0x0000000086800000, 0x0000000086800000| Untracked 
| 105|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%|HS|  |TAMS 0x0000000086900000, 0x0000000086900000| Complete 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%|HS|  |TAMS 0x0000000086a00000, 0x0000000086a00000| Complete 
| 107|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%| O|  |TAMS 0x0000000086b00000, 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%|HS|  |TAMS 0x0000000086c00000, 0x0000000086c00000| Complete 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%| O|  |TAMS 0x0000000086d00000, 0x0000000086d00000| Untracked 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%| O|  |TAMS 0x0000000086e00000, 0x0000000086e00000| Untracked 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%| O|  |TAMS 0x0000000086f00000, 0x0000000086f00000| Untracked 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%| O|  |TAMS 0x0000000087000000, 0x0000000087000000| Untracked 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%| O|  |TAMS 0x0000000087100000, 0x0000000087100000| Untracked 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%| O|  |TAMS 0x0000000087200000, 0x0000000087200000| Untracked 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%| O|  |TAMS 0x0000000087300000, 0x0000000087300000| Untracked 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%| O|  |TAMS 0x0000000087400000, 0x0000000087400000| Untracked 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%| O|  |TAMS 0x0000000087500000, 0x0000000087500000| Untracked 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%| O|  |TAMS 0x0000000087600000, 0x0000000087600000| Untracked 
| 119|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%| O|  |TAMS 0x0000000087700000, 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x0000000087900000, 0x0000000087900000|100%| O|  |TAMS 0x0000000087800000, 0x0000000087800000| Untracked 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%| O|  |TAMS 0x0000000087900000, 0x0000000087900000| Untracked 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%| O|  |TAMS 0x0000000087a00000, 0x0000000087a00000| Untracked 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%| O|  |TAMS 0x0000000087b00000, 0x0000000087b00000| Untracked 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%| O|  |TAMS 0x0000000087c00000, 0x0000000087c00000| Untracked 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%| O|  |TAMS 0x0000000087d00000, 0x0000000087d00000| Untracked 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%| O|  |TAMS 0x0000000087e00000, 0x0000000087e00000| Untracked 
| 127|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%| O|  |TAMS 0x0000000087f00000, 0x0000000087f00000| Untracked 
| 128|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%| O|  |TAMS 0x0000000088000000, 0x0000000088000000| Untracked 
| 129|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%| O|  |TAMS 0x0000000088100000, 0x0000000088100000| Untracked 
| 130|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%| O|  |TAMS 0x0000000088200000, 0x0000000088200000| Untracked 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%| O|  |TAMS 0x0000000088300000, 0x0000000088300000| Untracked 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%| O|  |TAMS 0x0000000088400000, 0x0000000088400000| Untracked 
| 133|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%| O|  |TAMS 0x0000000088500000, 0x0000000088500000| Untracked 
| 134|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%| O|  |TAMS 0x0000000088600000, 0x0000000088600000| Untracked 
| 135|0x0000000088700000, 0x0000000088800000, 0x0000000088800000|100%| O|  |TAMS 0x0000000088700000, 0x0000000088700000| Untracked 
| 136|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%| O|  |TAMS 0x0000000088800000, 0x0000000088800000| Untracked 
| 137|0x0000000088900000, 0x0000000088a00000, 0x0000000088a00000|100%| O|  |TAMS 0x0000000088900000, 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%| O|  |TAMS 0x0000000088a00000, 0x0000000088a00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%| O|  |TAMS 0x0000000088b00000, 0x0000000088b00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%| O|  |TAMS 0x0000000088c00000, 0x0000000088c00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088e00000, 0x0000000088e00000|100%| O|  |TAMS 0x0000000088d00000, 0x0000000088d00000| Untracked 
| 142|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%| O|  |TAMS 0x0000000088e00000, 0x0000000088e00000| Untracked 
| 143|0x0000000088f00000, 0x0000000089000000, 0x0000000089000000|100%| O|  |TAMS 0x0000000088f00000, 0x0000000088f00000| Untracked 
| 144|0x0000000089000000, 0x0000000089100000, 0x0000000089100000|100%| O|  |TAMS 0x0000000089000000, 0x0000000089000000| Untracked 
| 145|0x0000000089100000, 0x0000000089200000, 0x0000000089200000|100%| O|  |TAMS 0x0000000089100000, 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x0000000089300000, 0x0000000089300000|100%| O|  |TAMS 0x0000000089200000, 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089400000, 0x0000000089400000|100%| O|  |TAMS 0x0000000089300000, 0x0000000089300000| Untracked 
| 148|0x0000000089400000, 0x0000000089500000, 0x0000000089500000|100%| O|  |TAMS 0x0000000089400000, 0x0000000089400000| Untracked 
| 149|0x0000000089500000, 0x0000000089600000, 0x0000000089600000|100%| O|  |TAMS 0x0000000089500000, 0x0000000089500000| Untracked 
| 150|0x0000000089600000, 0x0000000089700000, 0x0000000089700000|100%| O|  |TAMS 0x0000000089600000, 0x0000000089600000| Untracked 
| 151|0x0000000089700000, 0x0000000089800000, 0x0000000089800000|100%| O|  |TAMS 0x0000000089700000, 0x0000000089700000| Untracked 
| 152|0x0000000089800000, 0x0000000089900000, 0x0000000089900000|100%| O|  |TAMS 0x0000000089800000, 0x0000000089800000| Untracked 
| 153|0x0000000089900000, 0x0000000089a00000, 0x0000000089a00000|100%| O|  |TAMS 0x0000000089900000, 0x0000000089900000| Untracked 
| 154|0x0000000089a00000, 0x0000000089b00000, 0x0000000089b00000|100%| O|  |TAMS 0x0000000089a00000, 0x0000000089a00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089c00000, 0x0000000089c00000|100%| O|  |TAMS 0x0000000089b00000, 0x0000000089b00000| Untracked 
| 156|0x0000000089c00000, 0x0000000089d00000, 0x0000000089d00000|100%| O|  |TAMS 0x0000000089c00000, 0x0000000089c00000| Untracked 
| 157|0x0000000089d00000, 0x0000000089e00000, 0x0000000089e00000|100%| O|  |TAMS 0x0000000089d00000, 0x0000000089d00000| Untracked 
| 158|0x0000000089e00000, 0x0000000089f00000, 0x0000000089f00000|100%| O|  |TAMS 0x0000000089e00000, 0x0000000089e00000| Untracked 
| 159|0x0000000089f00000, 0x0000000089f4b200, 0x000000008a000000| 29%| O|  |TAMS 0x0000000089f00000, 0x0000000089f00000| Untracked 
| 160|0x000000008a000000, 0x000000008a000000, 0x000000008a100000|  0%| F|  |TAMS 0x000000008a000000, 0x000000008a000000| Untracked 
| 161|0x000000008a100000, 0x000000008a100000, 0x000000008a200000|  0%| F|  |TAMS 0x000000008a100000, 0x000000008a100000| Untracked 
| 162|0x000000008a200000, 0x000000008a200000, 0x000000008a300000|  0%| F|  |TAMS 0x000000008a200000, 0x000000008a200000| Untracked 
| 163|0x000000008a300000, 0x000000008a300000, 0x000000008a400000|  0%| F|  |TAMS 0x000000008a300000, 0x000000008a300000| Untracked 
| 164|0x000000008a400000, 0x000000008a400000, 0x000000008a500000|  0%| F|  |TAMS 0x000000008a400000, 0x000000008a400000| Untracked 
| 165|0x000000008a500000, 0x000000008a500000, 0x000000008a600000|  0%| F|  |TAMS 0x000000008a500000, 0x000000008a500000| Untracked 
| 166|0x000000008a600000, 0x000000008a600000, 0x000000008a700000|  0%| F|  |TAMS 0x000000008a600000, 0x000000008a600000| Untracked 
| 167|0x000000008a700000, 0x000000008a700000, 0x000000008a800000|  0%| F|  |TAMS 0x000000008a700000, 0x000000008a700000| Untracked 
| 168|0x000000008a800000, 0x000000008a800000, 0x000000008a900000|  0%| F|  |TAMS 0x000000008a800000, 0x000000008a800000| Untracked 
| 169|0x000000008a900000, 0x000000008a900000, 0x000000008aa00000|  0%| F|  |TAMS 0x000000008a900000, 0x000000008a900000| Untracked 
| 170|0x000000008aa00000, 0x000000008aa00000, 0x000000008ab00000|  0%| F|  |TAMS 0x000000008aa00000, 0x000000008aa00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ab00000, 0x000000008ac00000|  0%| F|  |TAMS 0x000000008ab00000, 0x000000008ab00000| Untracked 
| 172|0x000000008ac00000, 0x000000008ac00000, 0x000000008ad00000|  0%| F|  |TAMS 0x000000008ac00000, 0x000000008ac00000| Untracked 
| 173|0x000000008ad00000, 0x000000008ad00000, 0x000000008ae00000|  0%| F|  |TAMS 0x000000008ad00000, 0x000000008ad00000| Untracked 
| 174|0x000000008ae00000, 0x000000008ae00000, 0x000000008af00000|  0%| F|  |TAMS 0x000000008ae00000, 0x000000008ae00000| Untracked 
| 175|0x000000008af00000, 0x000000008af00000, 0x000000008b000000|  0%| F|  |TAMS 0x000000008af00000, 0x000000008af00000| Untracked 
| 176|0x000000008b000000, 0x000000008b000000, 0x000000008b100000|  0%| F|  |TAMS 0x000000008b000000, 0x000000008b000000| Untracked 
| 177|0x000000008b100000, 0x000000008b100000, 0x000000008b200000|  0%| F|  |TAMS 0x000000008b100000, 0x000000008b100000| Untracked 
| 178|0x000000008b200000, 0x000000008b200000, 0x000000008b300000|  0%| F|  |TAMS 0x000000008b200000, 0x000000008b200000| Untracked 
| 179|0x000000008b300000, 0x000000008b300000, 0x000000008b400000|  0%| F|  |TAMS 0x000000008b300000, 0x000000008b300000| Untracked 
| 180|0x000000008b400000, 0x000000008b400000, 0x000000008b500000|  0%| F|  |TAMS 0x000000008b400000, 0x000000008b400000| Untracked 
| 181|0x000000008b500000, 0x000000008b500000, 0x000000008b600000|  0%| F|  |TAMS 0x000000008b500000, 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b600000, 0x000000008b700000|  0%| F|  |TAMS 0x000000008b600000, 0x000000008b600000| Untracked 
| 183|0x000000008b700000, 0x000000008b700000, 0x000000008b800000|  0%| F|  |TAMS 0x000000008b700000, 0x000000008b700000| Untracked 
| 184|0x000000008b800000, 0x000000008b800000, 0x000000008b900000|  0%| F|  |TAMS 0x000000008b800000, 0x000000008b800000| Untracked 
| 185|0x000000008b900000, 0x000000008b900000, 0x000000008ba00000|  0%| F|  |TAMS 0x000000008b900000, 0x000000008b900000| Untracked 
| 186|0x000000008ba00000, 0x000000008ba00000, 0x000000008bb00000|  0%| F|  |TAMS 0x000000008ba00000, 0x000000008ba00000| Untracked 
| 187|0x000000008bb00000, 0x000000008bb00000, 0x000000008bc00000|  0%| F|  |TAMS 0x000000008bb00000, 0x000000008bb00000| Untracked 
| 188|0x000000008bc00000, 0x000000008bc00000, 0x000000008bd00000|  0%| F|  |TAMS 0x000000008bc00000, 0x000000008bc00000| Untracked 
| 189|0x000000008bd00000, 0x000000008bd00000, 0x000000008be00000|  0%| F|  |TAMS 0x000000008bd00000, 0x000000008bd00000| Untracked 
| 190|0x000000008be00000, 0x000000008be00000, 0x000000008bf00000|  0%| F|  |TAMS 0x000000008be00000, 0x000000008be00000| Untracked 
| 191|0x000000008bf00000, 0x000000008bf00000, 0x000000008c000000|  0%| F|  |TAMS 0x000000008bf00000, 0x000000008bf00000| Untracked 
| 192|0x000000008c000000, 0x000000008c000000, 0x000000008c100000|  0%| F|  |TAMS 0x000000008c000000, 0x000000008c000000| Untracked 
| 193|0x000000008c100000, 0x000000008c100000, 0x000000008c200000|  0%| F|  |TAMS 0x000000008c100000, 0x000000008c100000| Untracked 
| 194|0x000000008c200000, 0x000000008c200000, 0x000000008c300000|  0%| F|  |TAMS 0x000000008c200000, 0x000000008c200000| Untracked 
| 195|0x000000008c300000, 0x000000008c300000, 0x000000008c400000|  0%| F|  |TAMS 0x000000008c300000, 0x000000008c300000| Untracked 
| 196|0x000000008c400000, 0x000000008c400000, 0x000000008c500000|  0%| F|  |TAMS 0x000000008c400000, 0x000000008c400000| Untracked 
| 197|0x000000008c500000, 0x000000008c5d0980, 0x000000008c600000| 81%| S|CS|TAMS 0x000000008c500000, 0x000000008c500000| Complete 
| 198|0x000000008c600000, 0x000000008c700000, 0x000000008c700000|100%| S|CS|TAMS 0x000000008c600000, 0x000000008c600000| Complete 
| 199|0x000000008c700000, 0x000000008c800000, 0x000000008c800000|100%| S|CS|TAMS 0x000000008c700000, 0x000000008c700000| Complete 
| 200|0x000000008c800000, 0x000000008c800000, 0x000000008c900000|  0%| F|  |TAMS 0x000000008c800000, 0x000000008c800000| Untracked 
| 201|0x000000008c900000, 0x000000008c900000, 0x000000008ca00000|  0%| F|  |TAMS 0x000000008c900000, 0x000000008c900000| Untracked 
| 202|0x000000008ca00000, 0x000000008ca00000, 0x000000008cb00000|  0%| F|  |TAMS 0x000000008ca00000, 0x000000008ca00000| Untracked 
| 203|0x000000008cb00000, 0x000000008cb00000, 0x000000008cc00000|  0%| F|  |TAMS 0x000000008cb00000, 0x000000008cb00000| Untracked 
| 204|0x000000008cc00000, 0x000000008cc00000, 0x000000008cd00000|  0%| F|  |TAMS 0x000000008cc00000, 0x000000008cc00000| Untracked 
| 205|0x000000008cd00000, 0x000000008cd00000, 0x000000008ce00000|  0%| F|  |TAMS 0x000000008cd00000, 0x000000008cd00000| Untracked 
| 206|0x000000008ce00000, 0x000000008ce00000, 0x000000008cf00000|  0%| F|  |TAMS 0x000000008ce00000, 0x000000008ce00000| Untracked 
| 207|0x000000008cf00000, 0x000000008cf00000, 0x000000008d000000|  0%| F|  |TAMS 0x000000008cf00000, 0x000000008cf00000| Untracked 
| 208|0x000000008d000000, 0x000000008d068230, 0x000000008d100000| 40%| E|  |TAMS 0x000000008d000000, 0x000000008d000000| Complete 
| 209|0x000000008d100000, 0x000000008d200000, 0x000000008d200000|100%| E|CS|TAMS 0x000000008d100000, 0x000000008d100000| Complete 
| 210|0x000000008d200000, 0x000000008d300000, 0x000000008d300000|100%| E|CS|TAMS 0x000000008d200000, 0x000000008d200000| Complete 
| 211|0x000000008d300000, 0x000000008d400000, 0x000000008d400000|100%| E|CS|TAMS 0x000000008d300000, 0x000000008d300000| Complete 
| 212|0x000000008d400000, 0x000000008d500000, 0x000000008d500000|100%| E|CS|TAMS 0x000000008d400000, 0x000000008d400000| Complete 
| 213|0x000000008d500000, 0x000000008d600000, 0x000000008d600000|100%| E|CS|TAMS 0x000000008d500000, 0x000000008d500000| Complete 
| 214|0x000000008d600000, 0x000000008d700000, 0x000000008d700000|100%| E|CS|TAMS 0x000000008d600000, 0x000000008d600000| Complete 
| 215|0x000000008d700000, 0x000000008d800000, 0x000000008d800000|100%| E|CS|TAMS 0x000000008d700000, 0x000000008d700000| Complete 
| 216|0x000000008d800000, 0x000000008d900000, 0x000000008d900000|100%| E|CS|TAMS 0x000000008d800000, 0x000000008d800000| Complete 
| 217|0x000000008d900000, 0x000000008da00000, 0x000000008da00000|100%| E|CS|TAMS 0x000000008d900000, 0x000000008d900000| Complete 
| 218|0x000000008da00000, 0x000000008db00000, 0x000000008db00000|100%| E|CS|TAMS 0x000000008da00000, 0x000000008da00000| Complete 
| 219|0x000000008db00000, 0x000000008dc00000, 0x000000008dc00000|100%| E|CS|TAMS 0x000000008db00000, 0x000000008db00000| Complete 
| 220|0x000000008dc00000, 0x000000008dd00000, 0x000000008dd00000|100%| E|CS|TAMS 0x000000008dc00000, 0x000000008dc00000| Complete 
| 221|0x000000008dd00000, 0x000000008de00000, 0x000000008de00000|100%| E|CS|TAMS 0x000000008dd00000, 0x000000008dd00000| Complete 
| 222|0x000000008de00000, 0x000000008df00000, 0x000000008df00000|100%| E|CS|TAMS 0x000000008de00000, 0x000000008de00000| Complete 
| 223|0x000000008df00000, 0x000000008e000000, 0x000000008e000000|100%| E|CS|TAMS 0x000000008df00000, 0x000000008df00000| Complete 
| 224|0x000000008e000000, 0x000000008e100000, 0x000000008e100000|100%| E|CS|TAMS 0x000000008e000000, 0x000000008e000000| Complete 
| 225|0x000000008e100000, 0x000000008e200000, 0x000000008e200000|100%| E|CS|TAMS 0x000000008e100000, 0x000000008e100000| Complete 
| 226|0x000000008e200000, 0x000000008e300000, 0x000000008e300000|100%| E|CS|TAMS 0x000000008e200000, 0x000000008e200000| Complete 
| 227|0x000000008e300000, 0x000000008e400000, 0x000000008e400000|100%| E|CS|TAMS 0x000000008e300000, 0x000000008e300000| Complete 
| 228|0x000000008e400000, 0x000000008e500000, 0x000000008e500000|100%| E|CS|TAMS 0x000000008e400000, 0x000000008e400000| Complete 
| 229|0x000000008e500000, 0x000000008e600000, 0x000000008e600000|100%| E|CS|TAMS 0x000000008e500000, 0x000000008e500000| Complete 
| 230|0x000000008e600000, 0x000000008e700000, 0x000000008e700000|100%| E|CS|TAMS 0x000000008e600000, 0x000000008e600000| Complete 
| 231|0x000000008e700000, 0x000000008e800000, 0x000000008e800000|100%| E|CS|TAMS 0x000000008e700000, 0x000000008e700000| Complete 
| 232|0x000000008e800000, 0x000000008e900000, 0x000000008e900000|100%| E|CS|TAMS 0x000000008e800000, 0x000000008e800000| Complete 
| 233|0x000000008e900000, 0x000000008ea00000, 0x000000008ea00000|100%| E|CS|TAMS 0x000000008e900000, 0x000000008e900000| Complete 
| 234|0x000000008ea00000, 0x000000008eb00000, 0x000000008eb00000|100%| E|CS|TAMS 0x000000008ea00000, 0x000000008ea00000| Complete 
| 235|0x000000008eb00000, 0x000000008ec00000, 0x000000008ec00000|100%| E|CS|TAMS 0x000000008eb00000, 0x000000008eb00000| Complete 
| 236|0x000000008ec00000, 0x000000008ed00000, 0x000000008ed00000|100%| E|CS|TAMS 0x000000008ec00000, 0x000000008ec00000| Complete 
| 237|0x000000008ed00000, 0x000000008ee00000, 0x000000008ee00000|100%| E|CS|TAMS 0x000000008ed00000, 0x000000008ed00000| Complete 
| 238|0x000000008ee00000, 0x000000008ef00000, 0x000000008ef00000|100%| E|CS|TAMS 0x000000008ee00000, 0x000000008ee00000| Complete 
| 239|0x000000008ef00000, 0x000000008f000000, 0x000000008f000000|100%| E|CS|TAMS 0x000000008ef00000, 0x000000008ef00000| Complete 
| 240|0x000000008f000000, 0x000000008f100000, 0x000000008f100000|100%| E|CS|TAMS 0x000000008f000000, 0x000000008f000000| Complete 
| 251|0x000000008fb00000, 0x000000008fc00000, 0x000000008fc00000|100%| E|CS|TAMS 0x000000008fb00000, 0x000000008fb00000| Complete 

Card table byte_map: [0x00000266fbe30000,0x00000266fc230000] _byte_map_base: 0x00000266fba30000

Marking Bits (Prev, Next): (CMBitMap*) 0x00000266f371e7a0, (CMBitMap*) 0x00000266f371e7e0
 Prev Bits: [0x00000266fc630000, 0x00000266fe630000)
 Next Bits: [0x000002668f000000, 0x0000026691000000)

Polling page: 0x00000266f1600000

Metaspace:

Usage:
  Non-class:     87.46 MB used.
      Class:     14.23 MB used.
       Both:    101.69 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      87.94 MB ( 69%) committed,  2 nodes.
      Class space:      416.00 MB reserved,      14.69 MB (  4%) committed,  1 nodes.
             Both:      544.00 MB reserved,     102.62 MB ( 19%) committed. 

Chunk freelists:
   Non-Class:  7.19 MB
       Class:  1.32 MB
        Both:  8.51 MB

MaxMetaspaceSize: 512.00 MB
CompressedClassSpaceSize: 416.00 MB
Initial GC threshold: 21.00 MB
Current GC threshold: 131.81 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 1276.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1642.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 5728.
num_chunk_merges: 6.
num_chunk_splits: 3691.
num_chunks_enlarged: 2349.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=9473Kb max_used=9473Kb free=109694Kb
 bounds [0x0000026687ba0000, 0x00000266884f0000, 0x000002668f000000]
CodeHeap 'profiled nmethods': size=119104Kb used=27797Kb max_used=27797Kb free=91307Kb
 bounds [0x0000026680000000, 0x0000026681b30000, 0x0000026687450000]
CodeHeap 'non-nmethods': size=7488Kb used=4129Kb max_used=4241Kb free=3358Kb
 bounds [0x0000026687450000, 0x0000026687880000, 0x0000026687ba0000]
 total_blobs=15050 nmethods=14072 adapters=886
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 24.173 Thread 0x00000266ff685390 16187       3       jdk.internal.misc.Unsafe::freeMemory (18 bytes)
Event: 24.173 Thread 0x00000266b2fa6010 16188       3       jdk.internal.misc.Unsafe::freeMemoryChecks (7 bytes)
Event: 24.173 Thread 0x00000266b2fa2af0 16186       4       org.gradle.cache.internal.btree.BTreePersistentIndexedCache$DataBlock::getValue (47 bytes)
Event: 24.174 Thread 0x00000266b2fa6010 nmethod 16188 0x00000266810df310 code [0x00000266810df4c0, 0x00000266810df6b8]
Event: 24.174 Thread 0x00000266ff685390 nmethod 16187 0x0000026680465490 code [0x0000026680465660, 0x0000026680465938]
Event: 24.174 Thread 0x00000266ff6812c0 16141       4       com.google.common.cache.LocalCache$LoadingValueReference::<init> (34 bytes)
Event: 24.177 Thread 0x00000266b2fa4ad0 nmethod 16173 0x00000266884bdf90 code [0x00000266884be180, 0x00000266884bee48]
Event: 24.178 Thread 0x00000266b2fa15b0 16191       3       sun.nio.fs.NativeBuffer$Deallocator::run (11 bytes)
Event: 24.178 Thread 0x00000266b2fa15b0 nmethod 16191 0x0000026680f4e490 code [0x0000026680f4e660, 0x0000026680f4e998]
Event: 24.179 Thread 0x00000266b2fa4ad0 16190   !   4       java.util.concurrent.FutureTask::run (123 bytes)
Event: 24.184 Thread 0x00000266ff6812c0 nmethod 16141 0x00000266884bf990 code [0x00000266884bfb20, 0x00000266884bfef8]
Event: 24.184 Thread 0x00000266ff6812c0 16175   !   4       com.google.common.util.concurrent.Uninterruptibles::getUninterruptibly (40 bytes)
Event: 24.188 Thread 0x00000266ff6812c0 nmethod 16175 0x00000266884c0010 code [0x00000266884c01a0, 0x00000266884c02d8]
Event: 24.189 Thread 0x00000266ff6812c0 16176       4       com.google.common.base.Stopwatch::elapsedNanos (32 bytes)
Event: 24.191 Thread 0x00000266b2fa2af0 nmethod 16186 0x00000266884c0510 code [0x00000266884c06c0, 0x00000266884c0f58]
Event: 24.191 Thread 0x00000266ff6812c0 nmethod 16176 0x00000266884c1290 code [0x00000266884c1420, 0x00000266884c14f8]
Event: 24.191 Thread 0x00000266ff6812c0 16142   !   4       com.google.common.cache.LocalCache$Segment::getAndRecordStats (153 bytes)
Event: 24.192 Thread 0x00000266b2fa2af0 16143       4       com.google.common.cache.LocalCache$Segment::loadSync (19 bytes)
Event: 24.193 Thread 0x00000266b2fa4ad0 nmethod 16190 0x00000266884c1610 code [0x00000266884c17c0, 0x00000266884c1d48]
Event: 24.193 Thread 0x00000266b2fa4ad0 16144   !   4       com.google.common.cache.LocalCache$LoadingValueReference::loadFuture (125 bytes)

GC Heap History (20 events):
Event: 20.019 GC heap before
{Heap before GC invocations=43 (full 0):
 garbage-first heap   total 247808K, used 174527K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 83 young (84992K), 1 survivors (1024K)
 Metaspace       used 85303K, committed 86144K, reserved 557056K
  class space    used 11761K, committed 12160K, reserved 425984K
}
Event: 20.022 GC heap after
{Heap after GC invocations=44 (full 0):
 garbage-first heap   total 247808K, used 95414K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 85303K, committed 86144K, reserved 557056K
  class space    used 11761K, committed 12160K, reserved 425984K
}
Event: 20.629 GC heap before
{Heap before GC invocations=44 (full 0):
 garbage-first heap   total 247808K, used 181430K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 90 young (92160K), 6 survivors (6144K)
 Metaspace       used 90902K, committed 91712K, reserved 557056K
  class space    used 12710K, committed 13120K, reserved 425984K
}
Event: 20.631 GC heap after
{Heap after GC invocations=45 (full 0):
 garbage-first heap   total 247808K, used 98410K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 90902K, committed 91712K, reserved 557056K
  class space    used 12710K, committed 13120K, reserved 425984K
}
Event: 21.315 GC heap before
{Heap before GC invocations=45 (full 0):
 garbage-first heap   total 247808K, used 189546K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 95 young (97280K), 9 survivors (9216K)
 Metaspace       used 93185K, committed 94080K, reserved 557056K
  class space    used 13043K, committed 13440K, reserved 425984K
}
Event: 21.321 GC heap after
{Heap after GC invocations=46 (full 0):
 garbage-first heap   total 247808K, used 118905K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 93185K, committed 94080K, reserved 557056K
  class space    used 13043K, committed 13440K, reserved 425984K
}
Event: 21.858 GC heap before
{Heap before GC invocations=46 (full 0):
 garbage-first heap   total 247808K, used 191609K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 80 young (81920K), 12 survivors (12288K)
 Metaspace       used 94114K, committed 95040K, reserved 557056K
  class space    used 13143K, committed 13568K, reserved 425984K
}
Event: 21.864 GC heap after
{Heap after GC invocations=47 (full 0):
 garbage-first heap   total 247808K, used 138216K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 94114K, committed 95040K, reserved 557056K
  class space    used 13143K, committed 13568K, reserved 425984K
}
Event: 22.326 GC heap before
{Heap before GC invocations=47 (full 0):
 garbage-first heap   total 247808K, used 192488K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 63 young (64512K), 10 survivors (10240K)
 Metaspace       used 98991K, committed 99968K, reserved 557056K
  class space    used 13821K, committed 14272K, reserved 425984K
}
Event: 22.330 GC heap after
{Heap after GC invocations=48 (full 0):
 garbage-first heap   total 247808K, used 143083K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 98991K, committed 99968K, reserved 557056K
  class space    used 13821K, committed 14272K, reserved 425984K
}
Event: 22.786 GC heap before
{Heap before GC invocations=48 (full 0):
 garbage-first heap   total 247808K, used 196331K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 56 young (57344K), 4 survivors (4096K)
 Metaspace       used 101436K, committed 102400K, reserved 557056K
  class space    used 14135K, committed 14592K, reserved 425984K
}
Event: 22.790 GC heap after
{Heap after GC invocations=49 (full 0):
 garbage-first heap   total 247808K, used 148206K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 7 young (7168K), 7 survivors (7168K)
 Metaspace       used 101436K, committed 102400K, reserved 557056K
  class space    used 14135K, committed 14592K, reserved 425984K
}
Event: 23.166 GC heap before
{Heap before GC invocations=49 (full 0):
 garbage-first heap   total 247808K, used 200430K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 58 young (59392K), 7 survivors (7168K)
 Metaspace       used 102312K, committed 103360K, reserved 557056K
  class space    used 14226K, committed 14720K, reserved 425984K
}
Event: 23.171 GC heap after
{Heap after GC invocations=50 (full 0):
 garbage-first heap   total 247808K, used 154388K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 102312K, committed 103360K, reserved 557056K
  class space    used 14226K, committed 14720K, reserved 425984K
}
Event: 23.476 GC heap before
{Heap before GC invocations=50 (full 0):
 garbage-first heap   total 247808K, used 202516K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 54 young (55296K), 6 survivors (6144K)
 Metaspace       used 103596K, committed 104640K, reserved 557056K
  class space    used 14522K, committed 15040K, reserved 425984K
}
Event: 23.480 GC heap after
{Heap after GC invocations=51 (full 0):
 garbage-first heap   total 247808K, used 158214K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 103596K, committed 104640K, reserved 557056K
  class space    used 14522K, committed 15040K, reserved 425984K
}
Event: 23.769 GC heap before
{Heap before GC invocations=51 (full 0):
 garbage-first heap   total 247808K, used 203270K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 50 young (51200K), 5 survivors (5120K)
 Metaspace       used 104071K, committed 105088K, reserved 557056K
  class space    used 14571K, committed 15040K, reserved 425984K
}
Event: 23.772 GC heap after
{Heap after GC invocations=52 (full 0):
 garbage-first heap   total 247808K, used 163213K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 104071K, committed 105088K, reserved 557056K
  class space    used 14571K, committed 15040K, reserved 425984K
}
Event: 23.918 GC heap before
{Heap before GC invocations=52 (full 0):
 garbage-first heap   total 247808K, used 205197K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 47 young (48128K), 5 survivors (5120K)
 Metaspace       used 104098K, committed 105088K, reserved 557056K
  class space    used 14571K, committed 15040K, reserved 425984K
}
Event: 23.921 GC heap after
{Heap after GC invocations=53 (full 0):
 garbage-first heap   total 247808K, used 165998K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 104098K, committed 105088K, reserved 557056K
  class space    used 14571K, committed 15040K, reserved 425984K
}

Dll operation events (14 events):
Event: 0.006 Loaded shared library C:\Program Files\Microsoft\jdk-********-hotspot\bin\java.dll
Event: 0.019 Loaded shared library C:\Program Files\Microsoft\jdk-********-hotspot\bin\jsvml.dll
Event: 0.043 Loaded shared library C:\Program Files\Microsoft\jdk-********-hotspot\bin\zip.dll
Event: 0.045 Loaded shared library C:\Program Files\Microsoft\jdk-********-hotspot\bin\instrument.dll
Event: 0.048 Loaded shared library C:\Program Files\Microsoft\jdk-********-hotspot\bin\net.dll
Event: 0.048 Loaded shared library C:\Program Files\Microsoft\jdk-********-hotspot\bin\nio.dll
Event: 0.050 Loaded shared library C:\Program Files\Microsoft\jdk-********-hotspot\bin\zip.dll
Event: 0.151 Loaded shared library C:\Program Files\Microsoft\jdk-********-hotspot\bin\jimage.dll
Event: 0.195 Loaded shared library C:\Program Files\Microsoft\jdk-********-hotspot\bin\verify.dll
Event: 0.297 Loaded shared library C:\Users\<USER>\.gradle\native\68d5fa5c4cc2d200863cafc0d521ce42e7d3e7ee720ec0a83991735586a16f82\windows-amd64\native-platform.dll
Event: 0.303 Loaded shared library C:\Users\<USER>\.gradle\native\e376f236ea51e6404a007f0833ffe2c6e607c4080706a723a18a27aeea778392\windows-amd64\native-platform-file-events.dll
Event: 0.816 Loaded shared library C:\Program Files\Microsoft\jdk-********-hotspot\bin\management.dll
Event: 0.817 Loaded shared library C:\Program Files\Microsoft\jdk-********-hotspot\bin\management_ext.dll
Event: 1.048 Loaded shared library C:\Program Files\Microsoft\jdk-********-hotspot\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 23.709 Thread 0x00000266b766cdc0 Uncommon trap: trap_request=0xffffffbe fr.pc=0x000002668841cfa4 relative=0x0000000000001564
Event: 23.709 Thread 0x00000266b766cdc0 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000002668841cfa4 method=org.gradle.internal.file.FileHierarchySet$Node.contains(Ljava/lang/String;I)Z @ 69 c2
Event: 23.709 Thread 0x00000266b766cdc0 DEOPT PACKING pc=0x000002668841cfa4 sp=0x000000f1bfcfb5b0
Event: 23.709 Thread 0x00000266b766cdc0 DEOPT UNPACKING pc=0x00000266874a69a3 sp=0x000000f1bfcfb580 mode 2
Event: 23.709 Thread 0x00000266b766cdc0 Uncommon trap: trap_request=0xffffffbe fr.pc=0x000002668841cfa4 relative=0x0000000000001564
Event: 23.709 Thread 0x00000266b766cdc0 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000002668841cfa4 method=org.gradle.internal.file.FileHierarchySet$Node.contains(Ljava/lang/String;I)Z @ 69 c2
Event: 23.710 Thread 0x00000266b766cdc0 DEOPT PACKING pc=0x000002668841cfa4 sp=0x000000f1bfcfb5b0
Event: 23.710 Thread 0x00000266b766cdc0 DEOPT UNPACKING pc=0x00000266874a69a3 sp=0x000000f1bfcfb580 mode 2
Event: 23.710 Thread 0x00000266b766cdc0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x00000266883d3dc0 relative=0x00000000000000a0
Event: 23.710 Thread 0x00000266b766cdc0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x00000266883d3dc0 method=org.gradle.execution.plan.ActionNode.hasPendingPreExecutionNodes()Z @ 11 c2
Event: 23.711 Thread 0x00000266b766cdc0 DEOPT PACKING pc=0x00000266883d3dc0 sp=0x000000f1bfcfe9d0
Event: 23.711 Thread 0x00000266b766cdc0 DEOPT UNPACKING pc=0x00000266874a69a3 sp=0x000000f1bfcfe958 mode 2
Event: 23.713 Thread 0x00000266b766c8b0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x00000266883d3dc0 relative=0x00000000000000a0
Event: 23.713 Thread 0x00000266b766c8b0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x00000266883d3dc0 method=org.gradle.execution.plan.ActionNode.hasPendingPreExecutionNodes()Z @ 11 c2
Event: 23.713 Thread 0x00000266b766c8b0 DEOPT PACKING pc=0x00000266883d3dc0 sp=0x000000f1bfbfeb60
Event: 23.713 Thread 0x00000266b766c8b0 DEOPT UNPACKING pc=0x00000266874a69a3 sp=0x000000f1bfbfeae8 mode 2
Event: 23.716 Thread 0x00000266b766c8b0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x00000266883d3dc0 relative=0x00000000000000a0
Event: 23.716 Thread 0x00000266b766c8b0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x00000266883d3dc0 method=org.gradle.execution.plan.ActionNode.hasPendingPreExecutionNodes()Z @ 11 c2
Event: 23.716 Thread 0x00000266b766c8b0 DEOPT PACKING pc=0x00000266883d3dc0 sp=0x000000f1bfbfeb60
Event: 23.716 Thread 0x00000266b766c8b0 DEOPT UNPACKING pc=0x00000266874a69a3 sp=0x000000f1bfbfeae8 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 23.342 Thread 0x00000266b9ae4520 Exception <a 'java/lang/NoSuchMethodError'{0x000000008dc2dd58}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008dc2dd58) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 23.342 Thread 0x00000266b944e8e0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008dccccb0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008dccccb0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 23.342 Thread 0x00000266b9ae5e70 Exception <a 'java/lang/NoSuchMethodError'{0x000000008dc961f8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008dc961f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 23.342 Thread 0x00000266b9ae0350 Exception <a 'java/lang/NoSuchMethodError'{0x000000008dc7c5a8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008dc7c5a8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 23.342 Thread 0x00000266b9ae30e0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008dcafe38}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008dcafe38) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 23.342 Thread 0x00000266b7673300 Exception <a 'java/lang/NoSuchMethodError'{0x000000008dcd60f8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008dcd60f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 23.342 Thread 0x00000266b944d9b0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008dc89de8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008dc89de8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 23.342 Thread 0x00000266b9ae4f40 Exception <a 'java/lang/NoSuchMethodError'{0x000000008dcbc8d8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008dcbc8d8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 23.342 Thread 0x00000266b9ae21b0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008dcd9578}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008dcd9578) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 23.342 Thread 0x00000266b411bff0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008dc6eb08}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008dc6eb08) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 23.342 Thread 0x00000266b9ae2bd0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008dca3e60}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008dca3e60) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 23.402 Thread 0x00000266b9ae30e0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008d59e640}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x000000008d59e640) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 23.459 Thread 0x00000266b7672df0 Implicit null exception at 0x00000266883de3d4 to 0x00000266883de434
Event: 23.667 Thread 0x00000266b766e200 Exception <a 'java/lang/ClassNotFoundException'{0x000000008db1e2c0}: groovy/util/FileTreeBuilderBeanInfo> (0x000000008db1e2c0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 23.669 Thread 0x00000266b766e200 Exception <a 'java/lang/ClassNotFoundException'{0x000000008db911e8}: groovy/util/FileTreeBuilderCustomizer> (0x000000008db911e8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 23.675 Thread 0x00000266b766e200 Exception <a 'java/lang/ClassNotFoundException'{0x000000008dbcc870}: java/io/BufferedWriterBeanInfo> (0x000000008dbcc870) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 23.675 Thread 0x00000266b766e200 Exception <a 'java/lang/ClassNotFoundException'{0x000000008dbd2628}: java/io/WriterBeanInfo> (0x000000008dbd2628) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 23.676 Thread 0x00000266b766e200 Exception <a 'java/lang/ClassNotFoundException'{0x000000008dbd8338}: java/io/WriterCustomizer> (0x000000008dbd8338) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 23.676 Thread 0x00000266b766e200 Exception <a 'java/lang/ClassNotFoundException'{0x000000008dbe7df8}: java/io/BufferedWriterCustomizer> (0x000000008dbe7df8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 23.699 Thread 0x00000266b7673300 Exception <a 'java/lang/NoSuchMethodError'{0x000000008da7f490}: static Lcom/android/build/api/variant/BuildConfigField;.<clinit>()V> (0x000000008da7f490) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1107]

VM Operations (20 events):
Event: 23.296 Executing VM operation: ICBufferFull
Event: 23.296 Executing VM operation: ICBufferFull done
Event: 23.303 Executing VM operation: HandshakeAllThreads
Event: 23.311 Executing VM operation: HandshakeAllThreads done
Event: 23.362 Executing VM operation: HandshakeAllThreads
Event: 23.363 Executing VM operation: HandshakeAllThreads done
Event: 23.459 Executing VM operation: ICBufferFull
Event: 23.459 Executing VM operation: ICBufferFull done
Event: 23.476 Executing VM operation: G1CollectForAllocation
Event: 23.481 Executing VM operation: G1CollectForAllocation done
Event: 23.692 Executing VM operation: ICBufferFull
Event: 23.692 Executing VM operation: ICBufferFull done
Event: 23.767 Executing VM operation: G1CollectForAllocation
Event: 23.772 Executing VM operation: G1CollectForAllocation done
Event: 23.819 Executing VM operation: HandshakeAllThreads
Event: 23.819 Executing VM operation: HandshakeAllThreads done
Event: 23.853 Executing VM operation: ICBufferFull
Event: 23.853 Executing VM operation: ICBufferFull done
Event: 23.917 Executing VM operation: G1CollectForAllocation
Event: 23.921 Executing VM operation: G1CollectForAllocation done

Events (20 events):
Event: 23.825 Thread 0x00000266ff68ac10 flushing nmethod 0x00000266882d2810
Event: 23.827 Thread 0x00000266ff68ac10 flushing nmethod 0x0000026680058b90
Event: 23.830 Thread 0x00000266ff68ac10 flushing nmethod 0x00000266802b7090
Event: 23.830 Thread 0x00000266ff68ac10 flushing nmethod 0x00000266802c3d90
Event: 23.831 Thread 0x00000266ff68ac10 flushing nmethod 0x00000266803cc690
Event: 23.832 Thread 0x00000266ff68ac10 flushing nmethod 0x0000026680464f90
Event: 23.835 Thread 0x00000266ff68ac10 flushing nmethod 0x00000266806b5c90
Event: 23.837 Thread 0x00000266ff68ac10 flushing nmethod 0x00000266808fee90
Event: 23.838 Thread 0x00000266ff68ac10 flushing nmethod 0x00000266809f5790
Event: 23.839 Thread 0x00000266ff68ac10 flushing nmethod 0x0000026680af2a10
Event: 23.840 Thread 0x00000266ff68ac10 flushing nmethod 0x0000026680c71310
Event: 23.843 Thread 0x00000266ff68ac10 flushing nmethod 0x0000026680ea2110
Event: 23.843 Thread 0x00000266ff68ac10 flushing nmethod 0x0000026680eb3210
Event: 23.843 Thread 0x00000266ff68ac10 flushing nmethod 0x0000026680f4df90
Event: 23.843 Thread 0x00000266ff68ac10 flushing nmethod 0x0000026680f4e310
Event: 23.843 Thread 0x00000266ff68ac10 flushing nmethod 0x0000026680f4ea10
Event: 23.843 Thread 0x00000266ff68ac10 flushing nmethod 0x0000026680f4f690
Event: 23.845 Thread 0x00000266ff68ac10 flushing nmethod 0x00000266810df310
Event: 23.845 Thread 0x00000266ff68ac10 flushing nmethod 0x0000026681101a90
Event: 23.853 Thread 0x00000266ff68ac10 flushing nmethod 0x0000026681759490


Dynamic libraries:
0x00007ff74c5e0000 - 0x00007ff74c5ee000 	C:\Program Files\Microsoft\jdk-********-hotspot\bin\java.exe
0x00007ffd3d170000 - 0x00007ffd3d387000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffd3c340000 - 0x00007ffd3c404000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffd3a8a0000 - 0x00007ffd3ac46000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffd3ac50000 - 0x00007ffd3ad61000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffd2eba0000 - 0x00007ffd2ebb7000 	C:\Program Files\Microsoft\jdk-********-hotspot\bin\jli.dll
0x00007ffd2eb80000 - 0x00007ffd2eb9b000 	C:\Program Files\Microsoft\jdk-********-hotspot\bin\VCRUNTIME140.dll
0x00007ffd3c470000 - 0x00007ffd3c61e000 	C:\WINDOWS\System32\USER32.dll
0x00007ffd3ae90000 - 0x00007ffd3aeb6000 	C:\WINDOWS\System32\win32u.dll
0x00007ffd27df0000 - 0x00007ffd28083000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.2506_none_270c5ae97388e100\COMCTL32.dll
0x00007ffd3b3a0000 - 0x00007ffd3b3c9000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffd3cef0000 - 0x00007ffd3cf97000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffd3ad70000 - 0x00007ffd3ae88000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffd3a7d0000 - 0x00007ffd3a86a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffd3cfa0000 - 0x00007ffd3cfd1000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffd334c0000 - 0x00007ffd334cc000 	C:\Program Files\Microsoft\jdk-********-hotspot\bin\vcruntime140_1.dll
0x00007ffd29940000 - 0x00007ffd299cd000 	C:\Program Files\Microsoft\jdk-********-hotspot\bin\msvcp140.dll
0x00007ffcb7e10000 - 0x00007ffcb8a75000 	C:\Program Files\Microsoft\jdk-********-hotspot\bin\server\jvm.dll
0x00007ffd3c8f0000 - 0x00007ffd3c9a3000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffd3c280000 - 0x00007ffd3c328000 	C:\WINDOWS\System32\sechost.dll
0x00007ffd3a870000 - 0x00007ffd3a898000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffd3bcd0000 - 0x00007ffd3bde7000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffd39300000 - 0x00007ffd3934d000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffd22270000 - 0x00007ffd22279000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffd32b50000 - 0x00007ffd32b5a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffd31360000 - 0x00007ffd31394000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffd3c9b0000 - 0x00007ffd3ca21000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffd392e0000 - 0x00007ffd392f3000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffd39500000 - 0x00007ffd39518000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffd330f0000 - 0x00007ffd330fa000 	C:\Program Files\Microsoft\jdk-********-hotspot\bin\jimage.dll
0x00007ffd37880000 - 0x00007ffd37ab3000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffd3b010000 - 0x00007ffd3b399000 	C:\WINDOWS\System32\combase.dll
0x00007ffd3ce10000 - 0x00007ffd3cee7000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffd0ea10000 - 0x00007ffd0ea42000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffd3a750000 - 0x00007ffd3a7ca000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffd308c0000 - 0x00007ffd308ce000 	C:\Program Files\Microsoft\jdk-********-hotspot\bin\instrument.dll
0x00007ffd2cf20000 - 0x00007ffd2cf45000 	C:\Program Files\Microsoft\jdk-********-hotspot\bin\java.dll
0x00007ffd1e420000 - 0x00007ffd1e4f7000 	C:\Program Files\Microsoft\jdk-********-hotspot\bin\jsvml.dll
0x00007ffd3b450000 - 0x00007ffd3bcab000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffd383d0000 - 0x00007ffd38cc6000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffd38290000 - 0x00007ffd383ce000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007ffd3c7f0000 - 0x00007ffd3c8e3000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffd3cc10000 - 0x00007ffd3cc6e000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffd3a3e0000 - 0x00007ffd3a406000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffd275a0000 - 0x00007ffd275b8000 	C:\Program Files\Microsoft\jdk-********-hotspot\bin\zip.dll
0x00007ffd2ce20000 - 0x00007ffd2ce39000 	C:\Program Files\Microsoft\jdk-********-hotspot\bin\net.dll
0x00007ffd33e20000 - 0x00007ffd33f57000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffd39a10000 - 0x00007ffd39a79000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffd283d0000 - 0x00007ffd283e6000 	C:\Program Files\Microsoft\jdk-********-hotspot\bin\nio.dll
0x00007ffd32b70000 - 0x00007ffd32b80000 	C:\Program Files\Microsoft\jdk-********-hotspot\bin\verify.dll
0x00007ffd22300000 - 0x00007ffd22327000 	C:\Users\<USER>\.gradle\native\68d5fa5c4cc2d200863cafc0d521ce42e7d3e7ee720ec0a83991735586a16f82\windows-amd64\native-platform.dll
0x00007ffcf7190000 - 0x00007ffcf72d4000 	C:\Users\<USER>\.gradle\native\e376f236ea51e6404a007f0833ffe2c6e607c4080706a723a18a27aeea778392\windows-amd64\native-platform-file-events.dll
0x00007ffd30b30000 - 0x00007ffd30b39000 	C:\Program Files\Microsoft\jdk-********-hotspot\bin\management.dll
0x00007ffd30a50000 - 0x00007ffd30a5b000 	C:\Program Files\Microsoft\jdk-********-hotspot\bin\management_ext.dll
0x00007ffd3c680000 - 0x00007ffd3c688000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffd39c20000 - 0x00007ffd39c3b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffd39520000 - 0x00007ffd39555000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffd39ac0000 - 0x00007ffd39aec000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffd39c60000 - 0x00007ffd39c6c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffd38fa0000 - 0x00007ffd38fcd000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffd3bdf0000 - 0x00007ffd3bdf9000 	C:\WINDOWS\System32\NSI.dll
0x00007ffd33fe0000 - 0x00007ffd33ff9000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffd33fb0000 - 0x00007ffd33fcf000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffd39010000 - 0x00007ffd39109000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffd2ee30000 - 0x00007ffd2ee3e000 	C:\Program Files\Microsoft\jdk-********-hotspot\bin\sunmscapi.dll
0x00007ffd3a5e0000 - 0x00007ffd3a746000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffd39e50000 - 0x00007ffd39e7e000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffd39e10000 - 0x00007ffd39e47000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffd027e0000 - 0x00007ffd027e8000 	C:\WINDOWS\system32\wshunix.dll
0x00007ffd271c0000 - 0x00007ffd271ca000 	C:\Windows\System32\rasadhlp.dll
0x00007ffd31690000 - 0x00007ffd31713000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffd10170000 - 0x00007ffd10187000 	C:\WINDOWS\system32\napinsp.dll
0x00007ffd0fae0000 - 0x00007ffd0fafb000 	C:\WINDOWS\system32\pnrpnsp.dll
0x00007ffd0f7d0000 - 0x00007ffd0f7e1000 	C:\WINDOWS\System32\winrnr.dll
0x00007ffd31f60000 - 0x00007ffd31f75000 	C:\WINDOWS\system32\wshbth.dll
0x00007ffd0f450000 - 0x00007ffd0f471000 	C:\WINDOWS\system32\nlansp_c.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Microsoft\jdk-********-hotspot\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.2506_none_270c5ae97388e100;C:\Program Files\Microsoft\jdk-********-hotspot\bin\server;C:\Users\<USER>\.gradle\native\68d5fa5c4cc2d200863cafc0d521ce42e7d3e7ee720ec0a83991735586a16f82\windows-amd64;C:\Users\<USER>\.gradle\native\e376f236ea51e6404a007f0833ffe2c6e607c4080706a723a18a27aeea778392\windows-amd64

VM Arguments:
jvm_args: -XX:MaxMetaspaceSize=512m --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx2048m -Dfile.encoding=windows-1252 -Duser.country=IN -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.3-all\6en3ugtfdg5xnpx44z4qbwgas\gradle-8.3\lib\agents\gradle-instrumentation-agent-8.3.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.3
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.3-all\6en3ugtfdg5xnpx44z4qbwgas\gradle-8.3\lib\gradle-launcher-8.3.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 436207616                                 {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 264241152                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxMetaspaceSize                         = 536870912                                 {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Microsoft\jdk-********-hotspot\
CLASSPATH=C:\Users\<USER>\Github\KhelSportsPlayerApp\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Users\<USER>\Github\KhelSportsPlayerApp\node_modules\.bin;C:\Users\<USER>\Github\KhelSportsPlayerApp\node_modules\.bin;C:\Users\<USER>\Github\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\nvm\v20.11.0\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Users\<USER>\Github\KhelSportsPlayerApp\node_modules\.bin;C:\Users\<USER>\Github\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\nvm\v20.11.0\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Program Files\Microsoft\jdk-********-hotspot\bin;C:\Python312\Scripts\;C:\Python312\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\ProgramData\chocolatey\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Ruby33-x64\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Android\Sdk;
USERNAME=anupr
LANG=en_US.UTF-8
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 154 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.3085)
OS uptime: 3 days 18:07 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 154 stepping 3 microcode 0x423, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, hv
Processor Information for processor 0
  Max Mhz: 2700, Current Mhz: 2700, Mhz Limit: 2700
Processor Information for processor 1
  Max Mhz: 2700, Current Mhz: 1489, Mhz Limit: 2700
Processor Information for processor 2
  Max Mhz: 2700, Current Mhz: 2700, Mhz Limit: 2700
Processor Information for processor 3
  Max Mhz: 2700, Current Mhz: 2700, Mhz Limit: 2700
Processor Information for processor 4
  Max Mhz: 2700, Current Mhz: 2700, Mhz Limit: 2700
Processor Information for processor 5
  Max Mhz: 2700, Current Mhz: 2700, Mhz Limit: 2700
Processor Information for processor 6
  Max Mhz: 2700, Current Mhz: 2700, Mhz Limit: 2700
Processor Information for processor 7
  Max Mhz: 2700, Current Mhz: 2700, Mhz Limit: 2700
Processor Information for processor 8
  Max Mhz: 2700, Current Mhz: 2700, Mhz Limit: 2700
Processor Information for processor 9
  Max Mhz: 2700, Current Mhz: 2700, Mhz Limit: 2700
Processor Information for processor 10
  Max Mhz: 2700, Current Mhz: 2700, Mhz Limit: 2700
Processor Information for processor 11
  Max Mhz: 2700, Current Mhz: 2700, Mhz Limit: 2700
Processor Information for processor 12
  Max Mhz: 2700, Current Mhz: 1995, Mhz Limit: 1971
Processor Information for processor 13
  Max Mhz: 2700, Current Mhz: 1995, Mhz Limit: 1971
Processor Information for processor 14
  Max Mhz: 2700, Current Mhz: 1995, Mhz Limit: 1971
Processor Information for processor 15
  Max Mhz: 2700, Current Mhz: 1995, Mhz Limit: 1971

Memory: 4k page, system-wide physical 16070M (3494M free)
TotalPageFile size 20934M (AvailPageFile size 3653M)
current process WorkingSet (physical memory assigned to process): 609M, peak: 626M
current process commit charge ("private bytes"): 675M, peak: 692M

vm_info: OpenJDK 64-Bit Server VM (17.0.8+7-LTS) for windows-amd64 JRE (17.0.8+7-LTS), built on Jul  7 2023 17:21:55 by "MicrosoftCorporation" with MS VC++ 16.10 / 16.11 (VS2019)

END.
