import React, {createContext, useContext, useState, useEffect} from 'react';
import { Text, View, Alert, Platform } from 'react-native';
import {NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {createDrawerNavigator} from '@react-navigation/drawer';
import HomePage from './src/screens/HomePage/HomePage.jsx';
import SignIn from './src/screens/SignIn';
import SignUp from './src/screens/SignUp';
import Courses from './src/screens/Courses';
import ThankYou from './src/screens/ThankYou';
import CoachProfile from './src/screens/CoachProfile';
import ListingCards from './src/screens/ListingCards';
import {AuthProvider} from './src/Context/AuthContext';
import Filter from './src/components/Filters/Filter';
import Collection from './src/components/Filters/Collection';
import FAQ from './src/screens/FAQ';
import PrivacyPolicy from './src/screens/PrivacyPolicy';
import CustomerGrievance from './src/screens/CustomerGrievance';
import Header from './src/components/header';
import BookingDetails from './src/screens/BookingDetails';
import RegisterAsCoach from './src/screens/RegisterAsCoach';
import ReadMore from './src/screens/ReadMore';
import Transactions from './src/screens/Transactions';
import {PaperProvider} from 'react-native-paper';
import TimePicker from './src/screens/TimePicker';
import QrScanner from './src/screens/QrScanner';
import AboutUs from './src/screens/AboutUs';
import Support from './src/screens/Support';
import TermsOfService from './src/screens/TermsOfService';
import Invoice from './src/screens/Invoice';
import CoachInvoice from './src/screens/CoachInvoice';
import OrderSummary from './src/screens/OrderSummary';
import ForgetPassword from './src/screens/ForgetPassword';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useAuth} from './src/Context/AuthContext';
import {NEXT_PUBLIC_BASE_URL, JWT_SECRET} from '@env';
import Contact from './src/screens/Contact';
// Import SearchScreen
import SearchScreen from './src/screens/Search/SearchScreen';
import Orientation from 'react-native-orientation-locker';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  requestLocationPermissionWithUX,
  LocationPermissionStatus
} from './src/helpers/locationHelper';
import AcademyProfile from './src/screens/AcademyProfile/AcademyProfile.tsx';
import PlayerProfile from './src/screens/PlayerProfile/PlayerProfile.jsx';
// import PlayerProfile from './src/screens/PlayerProfile.jsx';

const Stack = createNativeStackNavigator();

// Storage keys for location alert preferences
const STORAGE_KEYS = {
  LOCATION_SERVICES_ALERT_DISMISSED: 'location_services_alert_dismissed',
  LOCATION_BLOCKED_ALERT_DISMISSED: 'location_blocked_alert_dismissed'
};

// Helper functions for AsyncStorage
const checkAlertDismissed = async (storageKey: string): Promise<boolean> => {
  try {
    const dismissed = await AsyncStorage.getItem(storageKey);
    return dismissed === 'true';
  } catch (error) {
    console.warn('Error checking alert dismissed status:', error);
    return false; // Default to showing alert if storage fails
  }
};

const setAlertDismissed = async (storageKey: string): Promise<void> => {
  try {
    await AsyncStorage.setItem(storageKey, 'true');
    console.log('Location alert dismissed preference saved');
  } catch (error) {
    console.warn('Error saving alert dismissed status:', error);
  }
};

// Optional: Function to reset alert preferences (useful for testing)
const resetAlertPreferences = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(STORAGE_KEYS.LOCATION_SERVICES_ALERT_DISMISSED);
    await AsyncStorage.removeItem(STORAGE_KEYS.LOCATION_BLOCKED_ALERT_DISMISSED);
    console.log('Location alert preferences reset');
  } catch (error) {
    console.warn('Error resetting alert preferences:', error);
  }
};

function App(): React.JSX.Element {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    Orientation.lockToPortrait();
  }, []);

  // Centralized location permission request - runs only once on app initialization
  useEffect(() => {
    const requestLocationPermissionOnce = async () => {
      try {
        console.log('Requesting location permission at app level...');

        const result = await requestLocationPermissionWithUX();

        if (result.granted) {
          console.log('Location permission granted');
        } else {
          console.log('Location permission denied or blocked:', result.status);
          if (result.message) {
            console.log('Permission message:', result.message);

            // Show alert for location services disabled or blocked permissions
            // Check AsyncStorage to see if user has dismissed this type of alert
            if ((Platform.OS === 'ios' &&
                (result.status === LocationPermissionStatus.LOCATION_SERVICES_DISABLED ||
                 result.status === LocationPermissionStatus.BLOCKED)) ||
                (Platform.OS === 'android' &&
                 result.status === LocationPermissionStatus.BLOCKED)) {

              // Determine storage key and alert title based on status
              const isLocationServicesDisabled = result.status === LocationPermissionStatus.LOCATION_SERVICES_DISABLED;
              const storageKey = isLocationServicesDisabled
                ? STORAGE_KEYS.LOCATION_SERVICES_ALERT_DISMISSED
                : STORAGE_KEYS.LOCATION_BLOCKED_ALERT_DISMISSED;

              const alertTitle = isLocationServicesDisabled
                ? 'Turn On Location Services'
                : 'Location Access Required';

              // Check if user has previously dismissed this type of alert
              const alertDismissed = await checkAlertDismissed(storageKey);

              if (!alertDismissed) {
                Alert.alert(
                  alertTitle,
                  result.message,
                  [
                    {
                      text: 'OK',
                      style: 'default'
                    },
                    {
                      text: "Don't Show Again",
                      style: 'cancel',
                      onPress: () => setAlertDismissed(storageKey)
                    }
                  ]
                );
              }
            }
          }
        }
      } catch (error) {
        console.warn('Error requesting location permission at app level:', error);
      } finally {
        // Set loading to false after permission request completes (success or failure)
        setLoading(false);
      }
    };

    requestLocationPermissionOnce();
  }, []); // Empty dependency array ensures this runs only once

  // Show loading screen while permission request is in progress
  if (loading) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'white'}}/>
    );
  }
  return (
    <SafeAreaView style={{ flex: 1 }}>
      <PaperProvider>
        <NavigationContainer>
        <AuthProvider>
          <Stack.Navigator initialRouteName="Home">
            <Stack.Screen
              name="Home"
              component={HomePage}
              options={{
                headerShown: false,
              }}
            />
            <Stack.Screen name="SignIn" component={SignIn} />
            <Stack.Screen name="SignUp" component={SignUp} />
            <Stack.Screen
              name="Courses"
              component={Courses}
              initialParams={{courseId: 123}}
            />
            <Stack.Screen name="ThankYou" component={ThankYou} />
            <Stack.Screen name="PlayerProfile" component={PlayerProfile} />
            <Stack.Screen name="CoachProfile" component={CoachProfile} />
            <Stack.Screen name="ListingCards" component={ListingCards} />
            <Stack.Screen name="Collection" component={Collection} />
            <Stack.Screen name="FAQ" component={FAQ} />
            <Stack.Screen name="PrivacyPolicy" component={PrivacyPolicy} />
            <Stack.Screen name="CustomerGrievance" component={CustomerGrievance} />
            <Stack.Screen name="BookingDetails" component={BookingDetails} />
            <Stack.Screen name="RegisterAsCoach" component={RegisterAsCoach} />
            <Stack.Screen name="ReadMore" component={ReadMore} />
            <Stack.Screen name="Transactions" component={Transactions} />
            <Stack.Screen name="TimePicker" component={TimePicker} />
            <Stack.Screen name="QrScanner" component={QrScanner} />
            <Stack.Screen name="AcademyProfile" component={AcademyProfile} />
            <Stack.Screen name="Support" component={Support} />
            <Stack.Screen name="AboutUs" component={AboutUs} />
            <Stack.Screen name="Invoice" component={Invoice} />
            <Stack.Screen name="CoachInvoice" component={CoachInvoice} />
            <Stack.Screen name="OrderSummary" component={OrderSummary} />
            <Stack.Screen name="TermsOfService" component={TermsOfService} />
            <Stack.Screen name='ForgetPassword' component={ForgetPassword} />
            <Stack.Screen name='Contact' component={Contact} />
            <Stack.Screen
              name="SearchScreen"
              component={SearchScreen}
              options={{
                title: "Search",
                headerShown: true
              }}
            />
          </Stack.Navigator>
          </AuthProvider>
        </NavigationContainer>
      </PaperProvider>
      </SafeAreaView>

  );
}

export default App;