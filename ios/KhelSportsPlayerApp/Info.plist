<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>K<PERSON> Coach (Athlete)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.150321700613-r0aj7ctj9tpeav1eqglm6taedbd8gaei</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>googlechrome</string>
		<string>googlechrome-x-callback</string>
		<string>googlechrome-x-callback://</string>
		<string>com.googleusercontent.apps.150321700613-r0aj7ctj9tpeav1eqglm6taedbd8gaei</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>We use the camera to scan QR codes for marking attendance at your booked sports classes and courses. For example, when you arrive at a tennis lesson, you'll scan the coach's QR code to confirm your attendance.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>We use your location to find nearby sports facilities, coaches, and courses in your area. For example, when you search for 'tennis lessons', we'll show you the closest tennis coaches and courts based on your current location.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We use your location to find nearby sports facilities, coaches, and courses in your area. For example, when you search for 'tennis lessons', we'll show you the closest tennis coaches and courts based on your current location.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>KhelSportsPlayerApp needs access to your Photo Library.</string>
	<key>UIAppFonts</key>
	<array>
		<string>Lato-Regular.ttf</string>
		<string>Lato-Bold.ttf</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
