PODS:
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - Base64 (1.1.2)
  - BEMCheckBox (1.4.1)
  - boost (1.83.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.73.3)
  - FBReactNativeSpec (0.73.3):
    - RCT-Folly (= 2022.05.16.00)
    - RCTRequired (= 0.73.3)
    - RCTTypeSafety (= 0.73.3)
    - React-Core (= 0.73.3)
    - React-jsi (= 0.73.3)
    - ReactCommon/turbomodule/core (= 0.73.3)
  - fmt (6.2.1)
  - glog (0.3.5)
  - GoogleSignIn (7.0.0):
    - AppAuth (~> 1.5)
    - GTMAppAuth (< 3.0, >= 1.3)
    - GTMSessionFetcher/Core (< 4.0, >= 1.1)
  - GTMAppAuth (2.0.0):
    - AppAuth/Core (~> 1.6)
    - GTMSessionFetcher/Core (< 4.0, >= 1.5)
  - GTMSessionFetcher/Core (3.5.0)
  - hermes-engine (0.73.3):
    - hermes-engine/Pre-built (= 0.73.3)
  - hermes-engine/Pre-built (0.73.3)
  - JWT (3.0.0-beta.12):
    - Base64 (~> 1.1.2)
  - libavif/core (0.11.1)
  - libavif/libdav1d (0.11.1):
    - libavif/core
    - libdav1d (>= 0.6.0)
  - libdav1d (1.2.0)
  - libevent (2.1.12)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - razorpay-pod (1.4.2)
  - RCT-Folly (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2022.05.16.00)
  - RCT-Folly/Default (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Fabric (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.73.3)
  - RCTTypeSafety (0.73.3):
    - FBLazyVector (= 0.73.3)
    - RCTRequired (= 0.73.3)
    - React-Core (= 0.73.3)
  - React (0.73.3):
    - React-Core (= 0.73.3)
    - React-Core/DevSupport (= 0.73.3)
    - React-Core/RCTWebSocket (= 0.73.3)
    - React-RCTActionSheet (= 0.73.3)
    - React-RCTAnimation (= 0.73.3)
    - React-RCTBlob (= 0.73.3)
    - React-RCTImage (= 0.73.3)
    - React-RCTLinking (= 0.73.3)
    - React-RCTNetwork (= 0.73.3)
    - React-RCTSettings (= 0.73.3)
    - React-RCTText (= 0.73.3)
    - React-RCTVibration (= 0.73.3)
  - React-callinvoker (0.73.3)
  - React-Codegen (0.73.3):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.73.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.3)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.73.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.73.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.73.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.3)
    - React-Core/RCTWebSocket (= 0.73.3)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.73.3)
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.73.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.73.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.73.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.73.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.73.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.73.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.73.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.73.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.73.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.73.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.3)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.73.3):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety (= 0.73.3)
    - React-Codegen
    - React-Core/CoreModulesHeaders (= 0.73.3)
    - React-jsi (= 0.73.3)
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage (= 0.73.3)
    - ReactCommon
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.73.3):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.3)
    - React-debug (= 0.73.3)
    - React-jsi (= 0.73.3)
    - React-jsinspector (= 0.73.3)
    - React-logger (= 0.73.3)
    - React-perflogger (= 0.73.3)
    - React-runtimeexecutor (= 0.73.3)
  - React-debug (0.73.3)
  - React-Fabric (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.73.3)
    - React-Fabric/attributedstring (= 0.73.3)
    - React-Fabric/componentregistry (= 0.73.3)
    - React-Fabric/componentregistrynative (= 0.73.3)
    - React-Fabric/components (= 0.73.3)
    - React-Fabric/core (= 0.73.3)
    - React-Fabric/imagemanager (= 0.73.3)
    - React-Fabric/leakchecker (= 0.73.3)
    - React-Fabric/mounting (= 0.73.3)
    - React-Fabric/scheduler (= 0.73.3)
    - React-Fabric/telemetry (= 0.73.3)
    - React-Fabric/templateprocessor (= 0.73.3)
    - React-Fabric/textlayoutmanager (= 0.73.3)
    - React-Fabric/uimanager (= 0.73.3)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/inputaccessory (= 0.73.3)
    - React-Fabric/components/legacyviewmanagerinterop (= 0.73.3)
    - React-Fabric/components/modal (= 0.73.3)
    - React-Fabric/components/rncore (= 0.73.3)
    - React-Fabric/components/root (= 0.73.3)
    - React-Fabric/components/safeareaview (= 0.73.3)
    - React-Fabric/components/scrollview (= 0.73.3)
    - React-Fabric/components/text (= 0.73.3)
    - React-Fabric/components/textinput (= 0.73.3)
    - React-Fabric/components/unimplementedview (= 0.73.3)
    - React-Fabric/components/view (= 0.73.3)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/inputaccessory (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/modal (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/rncore (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/safeareaview (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/scrollview (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/text (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/textinput (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/unimplementedview (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/textlayoutmanager (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricImage (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired (= 0.73.3)
    - RCTTypeSafety (= 0.73.3)
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.73.3)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-graphics (0.73.3):
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core/Default (= 0.73.3)
    - React-utils
  - React-hermes (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - RCT-Folly/Futures (= 2022.05.16.00)
    - React-cxxreact (= 0.73.3)
    - React-jsi
    - React-jsiexecutor (= 0.73.3)
    - React-jsinspector (= 0.73.3)
    - React-perflogger (= 0.73.3)
  - React-ImageManager (0.73.3):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.73.3):
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-debug
    - React-jsi
    - React-Mapbuffer
  - React-jsi (0.73.3):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
  - React-jsiexecutor (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact (= 0.73.3)
    - React-jsi (= 0.73.3)
    - React-perflogger (= 0.73.3)
  - React-jsinspector (0.73.3)
  - React-logger (0.73.3):
    - glog
  - React-Mapbuffer (0.73.3):
    - glog
    - React-debug
  - react-native-date-picker (4.4.2):
    - React-Core
  - react-native-geolocation (3.2.1):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - react-native-html-to-pdf (0.12.0):
    - React-Core
  - react-native-orientation-locker (1.7.0):
    - React-Core
  - react-native-pdf (6.7.5):
    - React-Core
  - react-native-pure-jwt (3.0.2):
    - JWT (= 3.0.0-beta.12)
    - React
  - react-native-razorpay (2.3.0):
    - razorpay-pod
    - React
  - react-native-render-html (6.3.4):
    - React-Core
  - react-native-safe-area-context (4.9.0):
    - React-Core
  - react-native-slider (4.3.1):
    - React-Core
  - react-native-webview (13.8.1):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - React-nativeconfig (0.73.3)
  - React-NativeModulesApple (0.73.3):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.73.3)
  - React-RCTActionSheet (0.73.3):
    - React-Core/RCTActionSheetHeaders (= 0.73.3)
  - React-RCTAnimation (0.73.3):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTAppDelegate (0.73.3):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon
  - React-RCTBlob (0.73.3):
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTFabric (0.73.3):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-nativeconfig
    - React-RCTImage
    - React-RCTText
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.73.3):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTLinking (0.73.3):
    - React-Codegen
    - React-Core/RCTLinkingHeaders (= 0.73.3)
    - React-jsi (= 0.73.3)
    - React-NativeModulesApple
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.73.3)
  - React-RCTNetwork (0.73.3):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTSettings (0.73.3):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTText (0.73.3):
    - React-Core/RCTTextHeaders (= 0.73.3)
    - Yoga
  - React-RCTVibration (0.73.3):
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-rendererdebug (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - React-rncore (0.73.3)
  - React-runtimeexecutor (0.73.3):
    - React-jsi (= 0.73.3)
  - React-runtimescheduler (0.73.3):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-jsi
    - React-rendererdebug
    - React-runtimeexecutor
    - React-utils
  - React-utils (0.73.3):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - ReactCommon (0.73.3):
    - React-logger (= 0.73.3)
    - ReactCommon/turbomodule (= 0.73.3)
  - ReactCommon/turbomodule (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.3)
    - React-cxxreact (= 0.73.3)
    - React-jsi (= 0.73.3)
    - React-logger (= 0.73.3)
    - React-perflogger (= 0.73.3)
    - ReactCommon/turbomodule/bridging (= 0.73.3)
    - ReactCommon/turbomodule/core (= 0.73.3)
  - ReactCommon/turbomodule/bridging (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.3)
    - React-cxxreact (= 0.73.3)
    - React-jsi (= 0.73.3)
    - React-logger (= 0.73.3)
    - React-perflogger (= 0.73.3)
  - ReactCommon/turbomodule/core (0.73.3):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.3)
    - React-cxxreact (= 0.73.3)
    - React-jsi (= 0.73.3)
    - React-logger (= 0.73.3)
    - React-perflogger (= 0.73.3)
  - rn-fetch-blob (0.12.0):
    - React-Core
  - RNCAsyncStorage (1.22.3):
    - React-Core
  - RNCCheckbox (0.5.17):
    - BEMCheckBox (~> 1.4)
    - React-Core
  - RNCPicker (2.7.2):
    - React-Core
  - RNDateTimePicker (7.7.0):
    - React-Core
  - RNFastImage (8.10.0):
    - libavif/core (~> 0.11.1)
    - libavif/libdav1d (~> 0.11.1)
    - React-Core
    - SDWebImage (>= 5.19.1)
    - SDWebImageAVIFCoder (~> 0.11.0)
    - SDWebImageWebPCoder (~> 0.14)
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.15.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNGoogleSignin (11.0.1):
    - GoogleSignIn (~> 7.0.0)
    - React-Core
  - RNPermissions (4.1.5):
    - React-Core
  - RNScreens (3.29.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - RNShare (10.2.1):
    - React-Core
  - RNVectorIcons (10.1.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - SDWebImageAVIFCoder (0.11.0):
    - libavif/core (>= 0.11.0)
    - SDWebImage (~> 5.10)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - SocketRocket (0.6.1)
  - VisionCamera (4.0.1):
    - VisionCamera/Core (= 4.0.1)
    - VisionCamera/React (= 4.0.1)
  - VisionCamera/Core (4.0.1)
  - VisionCamera/React (4.0.1):
    - React-Core
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - react-native-date-picker (from `../node_modules/react-native-date-picker`)
  - "react-native-geolocation (from `../node_modules/@react-native-community/geolocation`)"
  - react-native-html-to-pdf (from `../node_modules/react-native-html-to-pdf`)
  - react-native-orientation-locker (from `../node_modules/react-native-orientation-locker`)
  - react-native-pdf (from `../node_modules/react-native-pdf`)
  - react-native-pure-jwt (from `../node_modules/react-native-pure-jwt`)
  - react-native-razorpay (from `../node_modules/react-native-razorpay`)
  - react-native-render-html (from `../node_modules/react-native-render-html`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - "react-native-slider (from `../node_modules/@react-native-community/slider`)"
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-nativeconfig (from `../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - rn-fetch-blob (from `../node_modules/rn-fetch-blob`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCCheckbox (from `../node_modules/@react-native-community/checkbox`)"
  - "RNCPicker (from `../node_modules/@react-native-picker/picker`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - "RNFastImage (from `../node_modules/@d11/react-native-fast-image`)"
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - "RNGoogleSignin (from `../node_modules/@react-native-google-signin/google-signin`)"
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNShare (from `../node_modules/react-native-share`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - VisionCamera (from `../node_modules/react-native-vision-camera`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - AppAuth
    - Base64
    - BEMCheckBox
    - fmt
    - GoogleSignIn
    - GTMAppAuth
    - GTMSessionFetcher
    - JWT
    - libavif
    - libdav1d
    - libevent
    - libwebp
    - razorpay-pod
    - SDWebImage
    - SDWebImageAVIFCoder
    - SDWebImageWebPCoder
    - SocketRocket

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2023-11-17-RNv0.73.0-21043a3fc062be445e56a2c10ecd8be028dd9cc5
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  react-native-date-picker:
    :path: "../node_modules/react-native-date-picker"
  react-native-geolocation:
    :path: "../node_modules/@react-native-community/geolocation"
  react-native-html-to-pdf:
    :path: "../node_modules/react-native-html-to-pdf"
  react-native-orientation-locker:
    :path: "../node_modules/react-native-orientation-locker"
  react-native-pdf:
    :path: "../node_modules/react-native-pdf"
  react-native-pure-jwt:
    :path: "../node_modules/react-native-pure-jwt"
  react-native-razorpay:
    :path: "../node_modules/react-native-razorpay"
  react-native-render-html:
    :path: "../node_modules/react-native-render-html"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-slider:
    :path: "../node_modules/@react-native-community/slider"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-nativeconfig:
    :path: "../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  rn-fetch-blob:
    :path: "../node_modules/rn-fetch-blob"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCCheckbox:
    :path: "../node_modules/@react-native-community/checkbox"
  RNCPicker:
    :path: "../node_modules/@react-native-picker/picker"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNFastImage:
    :path: "../node_modules/@d11/react-native-fast-image"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNGoogleSignin:
    :path: "../node_modules/@react-native-google-signin/google-signin"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  VisionCamera:
    :path: "../node_modules/react-native-vision-camera"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  Base64: cecfb41a004124895a7bcee567a89bae5a89d49b
  BEMCheckBox: 5ba6e37ade3d3657b36caecc35c8b75c6c2b1a4e
  boost: d3f49c53809116a5d38da093a8aa78bf551aed09
  BVLinearGradient: cb006ba232a1f3e4f341bb62c42d1098c284da70
  DoubleConversion: fea03f2699887d960129cc54bba7e52542b6f953
  FBLazyVector: 70590b4f9e8ae9b0ce076efacea3abd7bc585ace
  FBReactNativeSpec: e47ea8c8f044c25e41a4fa5e8b7ff3923d3e0a94
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: c5d68082e772fa1c511173d6b30a9de2c05a69a2
  GoogleSignIn: b232380cf495a429b8095d3178a8d5855b42e842
  GTMAppAuth: 99fb010047ba3973b7026e45393f51f27ab965ae
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  hermes-engine: 5420539d016f368cd27e008f65f777abd6098c56
  JWT: 9b5c05abbcc1a0e69c3c91e1655b3387fc7e581d
  libavif: 84bbb62fb232c3018d6f1bab79beea87e35de7b7
  libdav1d: 23581a4d8ec811ff171ed5e2e05cd27bad64c39f
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  razorpay-pod: 77bdd9733b6ea81c9fbbd6b75fea6a09feab284e
  RCT-Folly: cd21f1661364f975ae76b3308167ad66b09f53f5
  RCTRequired: 9b898847f76977a6dfed2a08f4c5ed37add75ec0
  RCTTypeSafety: 0debdc4ba38c8138016d8d8ada4bdf9ec1b8aa82
  React: f8afb04431634ac7e9b876dc96d30af9871f5946
  React-callinvoker: 5ea86c3f93326867aa5114989d229db54d4759d0
  React-Codegen: 008c682f9969f79fecf0fc121d638c35f8cc6c74
  React-Core: d51781503984a2277f9fc9f3dddf85ee97738f69
  React-CoreModules: b52f9d5ed92e784d1d9e97ec7d1bdf05426aca11
  React-cxxreact: 5c982b03d5741b86f27a52a31bbad1601e5916f5
  React-debug: 23ea1f904cd98ae3b04b2b4982584641d3c7bcb5
  React-Fabric: 59f4b074be6930d12010fc3eaff14aae327aead0
  React-FabricImage: 3a54f3d5f319d2cf5379426ecb900953bce0a7bd
  React-graphics: e0f9ef6ed361eb1f8b4200d9ca0519ac33d6fd1a
  React-hermes: 247bd138d8f7d375f39185be6bd0d4372c8b9481
  React-ImageManager: ba8e12ac6b73ae7f0aa2294c3144ebc9bbed6a7a
  React-jserrorhandler: 371b399f8cc4f6b793ca318c10ec3e298e3e7d4a
  React-jsi: 24392fe58e0e811c3705226f747f62597d3cd063
  React-jsiexecutor: 0072f9e6b44f4a4a6435c5f1933c8c83995f8103
  React-jsinspector: 6fad0fe14882fb6b1c32e5cc8a4bd3d33a8b6790
  React-logger: 026c4699c71b179283959f13583507a06099fbde
  React-Mapbuffer: 62a470fa4b06d7ca94d7d49c3e9f3a0aeb4cebef
  react-native-date-picker: 5ddb34db03afc527bc07777952c2ac72fa384411
  react-native-geolocation: 461aa445b5a9d871b62467a3b998b5a43528fd20
  react-native-html-to-pdf: 7a49e6c58ac5221bcc093027b195f4b214f27a9d
  react-native-orientation-locker: cc6f357b289a2e0dd2210fea0c52cb8e0727fdaa
  react-native-pdf: 58e0b0d938c63bd08fb9c2d852a9dfc1b79af2e2
  react-native-pure-jwt: aadf68bd2da2fb8548744b6a0b7c52fadc5d90f7
  react-native-razorpay: ab51e7be3d906f49b26198d978fc6f163e846d30
  react-native-render-html: 5afc4751f1a98621b3009432ef84c47019dcb2bd
  react-native-safe-area-context: 435f4c13ac75ceed6135382ee77d57d1a5b5b2d6
  react-native-slider: 26d674f6cb59ecb825a23636a8fd63483f397d13
  react-native-webview: c4a84268d3960c32dc877dade6af70a03732f36c
  React-nativeconfig: 4d3076dc3dc498ec49819e4e4225b55d3507f902
  React-NativeModulesApple: e335068b1e202c9f3190e84c158c7f29db9236c6
  React-perflogger: 27ccacf853ba725524ef2b4e444f14e34d0837b0
  React-RCTActionSheet: 77dd6a2a5cfab9e85b7f1add0f7e2e9cd697d936
  React-RCTAnimation: ba12c9c61312942267e6bed99d8dcc09f7df83cd
  React-RCTAppDelegate: dfd229fc646769c2d10008098e878e4599380469
  React-RCTBlob: dabdea9782a7a14ca74953e4caf49921ae394869
  React-RCTFabric: 29273e19350fb7385db20f224f1d6ae984963787
  React-RCTImage: 97127bf61b00f36fa67ba1a8c175151c1761465f
  React-RCTLinking: 6d65395b1b070d02a045c829ca6d903b1582319b
  React-RCTNetwork: 7a36665387f0fd385c25238cc9d2c03636bbae75
  React-RCTSettings: 951ab88b1a16bd44d50b9bd298b48a42fb26d4f5
  React-RCTText: ff819f4216c2874d68938022976b0d61897f3539
  React-RCTVibration: b8ec1565a7e57619adec38b018de1e3e6054b04a
  React-rendererdebug: 7c3c37ed16dfd5fef44824bf080baff1e4134c93
  React-rncore: bfb1b25c3e6ce9535708a7ac109c91fed3c8085b
  React-runtimeexecutor: 7e71a40def8262ef7d82a1145d873ea61b1a27b5
  React-runtimescheduler: f49563abf7590f60501f0ac5c0ef7d730d62894a
  React-utils: f2d847abe621c4dfa0f5a2968c080cbc50660d95
  ReactCommon: 55b0ff6881f30c25713c0ba6bc4e30af3d4aa261
  rn-fetch-blob: 25612b6d6f6e980c6f17ed98ba2f58f5696a51ca
  RNCAsyncStorage: 9350c2956f996b3ff1ac7cfdb50901c113a27640
  RNCCheckbox: 450ce156f3e29e25efa0315c96cfbabe5a39ded1
  RNCPicker: fb82ba6cfba077a80a32412bc7b5391130a4e25f
  RNDateTimePicker: 590f2000e4272050b98689cee6c8abc66c25bb22
  RNFastImage: b508cbf84894005f533b980b1bf578e654827f7c
  RNFS: 89de7d7f4c0f6bafa05343c578f61118c8282ed8
  RNGestureHandler: 2a76de24abfbdfdc7a7a5fcb4b5e57563ada065e
  RNGoogleSignin: b8f09e3ec56e09497e1e53b0ff66d5a45916c6b1
  RNPermissions: 308909220f5f1486f19e2978f185151f6aaee4b5
  RNScreens: 8d1521c6e375a79dbd4525efaf21704cc64ac0cf
  RNShare: 694e19d7f74ac4c04de3a8af0649e9ccc03bd8b1
  RNVectorIcons: 58c1437bf9e3b57578b5fa04d713f8e7780403f2
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  SDWebImageAVIFCoder: 00310d246aab3232ce77f1d8f0076f8c4b021d90
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  VisionCamera: a7ae9105df094fd54b156247424d570e261d54d2
  Yoga: ff0382b894475dba0b4d2a5fda860bfee5a9afad

PODFILE CHECKSUM: d11d7a4279815a01190d9bc0ec49d10fcf3becc3

COCOAPODS: 1.16.2
