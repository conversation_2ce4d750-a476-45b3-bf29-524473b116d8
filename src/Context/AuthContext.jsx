import React, { createContext, useContext, useState, useEffect } from 'react';
import { Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { NEXT_PUBLIC_BASE_URL } from '@env';
import { useNavigation } from '@react-navigation/native';
const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userId, setUserId] = useState(null);
  const [userToken, setUserToken] = useState(null);
  const [selectedFilters, setSelectedFilters]  = useState(null);
  const [playerData, setPlayerData] = useState({});
  const navigation = useNavigation();

  useEffect(() => {
    const checkLoginState = async () => {
      try {
        const token = await AsyncStorage.getItem('userToken');
        const id = await AsyncStorage.getItem('userId');
        if (token && id) {
          setIsLoggedIn(true);
          setUserToken(token);
          setUserId(id);
          // Optionally, fetchUserDetails(token, id);
        }
      } catch (error) {
        console.error("Error loading auth state:", error);
      }
    };
    checkLoginState();
  }, []);

  const applyFilter = filters => {
    setSelectedFilters(filters);
    setFilterVisible(false);
  };

  const login = async(id, token) => {
    setIsLoggedIn(true);
    setUserId(id);
    setUserToken(token);
    AsyncStorage.setItem('userToken', token);
    AsyncStorage.setItem('userId', id);
    
  };

  const clearStorage = async () => {
    await AsyncStorage.clear();
  };

  const logout = async () => {
    setIsLoggedIn(false);
    setUserId(null);
    setUserToken(null);
    setPlayerData({});
    await clearStorage();
  };

  const setUser = (id, token) => {
    setUserId(id);
    setUserToken(token);
    setIsLoggedIn(true);
    // fetchUserDetails(token, id)
  };
  const fetchUserDetails = async (token, id) => {
    try {
      const response = await fetch(`${NEXT_PUBLIC_BASE_URL}/api/player/${userId}`, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${token}`,
        },
      });
      if(response.status === 401)
      {
        logout();
        Alert.alert("Status", "Please Sign Again", [
          {
            text: "OK", onPress: () => {
              navigation.navigate("SignIn")
            }
          }
        ]);
       
      }else{
        const result = await response.json();
        setPlayerData(result);
        setIsLoggedIn(true);
      }
    } catch (error) {
      console.error("Error fetching user details:", error);
      // Alert.alert("Error", "Failed to fetch user details. Please try again later.");
    }
  };
  useEffect(() => {
    if (isLoggedIn && userToken && userId) {
      fetchUserDetails(userToken, userId);
    }
  }, [isLoggedIn, userToken, userId]);

  return (
    <AuthContext.Provider value={{ isLoggedIn, login, logout, userId, setUser, userToken , userToken, applyFilter, selectedFilters, setSelectedFilters, playerData , fetchUserDetails}}>
      {children}
    </AuthContext.Provider>
  );
};