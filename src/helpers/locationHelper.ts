import { Platform, Alert, PermissionsAndroid } from 'react-native';
import Geolocation from '@react-native-community/geolocation';
import { GOOGLE_PLACES_API_KEY } from '@env';
import { check, request, PERMISSIONS, RESULTS } from 'react-native-permissions';

export interface LocationCoordinates {
  lat: number;
  lng: number;
}

export interface LocationResult {
  coordinates: LocationCoordinates;
  cityName: string;
}

export enum LocationPermissionStatus {
  GRANTED = 'granted',
  DENIED = 'denied',
  BLOCKED = 'blocked',
  NOT_DETERMINED = 'not_determined',
  UNAVAILABLE = 'unavailable',
  LOCATION_SERVICES_DISABLED = 'location_services_disabled'
}

/**
 * Check if location services are enabled at the device level
 * This is different from app-level permissions
 */
export const checkLocationServicesEnabled = async (): Promise<boolean> => {
  try {
    if (Platform.OS === 'android') {
      const status = await check(PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION);
      return status !== RESULTS.UNAVAILABLE;
    } else {
      // iOS
      const status = await check(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);
      return status !== RESULTS.UNAVAILABLE;
    }
  } catch (error) {
    console.warn('Error checking location services status:', error);
    return false;
  }
};

/**
 * Check current location permission status
 * Returns the current permission status without requesting
 */
export const checkLocationPermissionStatus = async (): Promise<LocationPermissionStatus> => {
  try {
    if (Platform.OS === 'android') {
      const status = await check(PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION);
      switch (status) {
        case RESULTS.GRANTED:
          return LocationPermissionStatus.GRANTED;
        case RESULTS.DENIED:
          return LocationPermissionStatus.DENIED;
        case RESULTS.BLOCKED:
          return LocationPermissionStatus.BLOCKED;
        case RESULTS.UNAVAILABLE:
          // On Android, UNAVAILABLE typically means location services are disabled
          return LocationPermissionStatus.LOCATION_SERVICES_DISABLED;
        default:
          return LocationPermissionStatus.NOT_DETERMINED;
      }
    } else {
      // iOS
      const status = await check(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);
      console.log('Location permission status:', status);
      switch (status) {
        case RESULTS.GRANTED:
          return LocationPermissionStatus.GRANTED;
        case RESULTS.DENIED:
          return LocationPermissionStatus.DENIED;
        case RESULTS.BLOCKED:
          return LocationPermissionStatus.BLOCKED;
        case RESULTS.UNAVAILABLE:
          // On iOS, UNAVAILABLE typically means location services are disabled
          return LocationPermissionStatus.LOCATION_SERVICES_DISABLED;
        default:
          return LocationPermissionStatus.NOT_DETERMINED;
      }
    }
  } catch (error) {
    console.warn('Error checking location permission status:', error);
    return LocationPermissionStatus.UNAVAILABLE;
  }
};

/**
 * Request location permission for both Android and iOS devices
 * Handles different permission states and provides appropriate user messaging
 * On Android, this will trigger native location services prompt if services are disabled
 */
export const requestLocationPermission = async (): Promise<boolean> => {
  try {
    if (Platform.OS === 'android') {
      // On Android, attempt permission request even if location services appear disabled
      // This allows the system to show native "Turn on location services?" dialog
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        {
          title: "Location Access Required",
          message: "This app needs to access your location to find nearby sports facilities, coaches, and courses in your area.",
          buttonPositive: "Allow",
          buttonNegative: "Deny"
        }
      );
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    } else {
      // iOS
      const currentStatus = await checkLocationPermissionStatus();

      if (currentStatus === LocationPermissionStatus.GRANTED) {
        return true;
      }

      if (currentStatus === LocationPermissionStatus.BLOCKED) {
        // Don't show alert here - let the calling code handle messaging
        return false;
      }

      // For iOS, don't attempt if location services are disabled
      if (currentStatus === LocationPermissionStatus.LOCATION_SERVICES_DISABLED) {
        return false;
      }

      const result = await request(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);
      return result === RESULTS.GRANTED;
    }
  } catch (error) {
    console.warn('Location permission request error:', error);
    return false;
  }
};

/**
 * Request location permission with enhanced user experience
 * Provides detailed messaging based on permission state
 */
export const requestLocationPermissionWithUX = async (): Promise<{
  granted: boolean;
  status: LocationPermissionStatus;
  message?: string;
}> => {
  try {
    const currentStatus = await checkLocationPermissionStatus();
    console.log('Current location permission status:', currentStatus);
    if (currentStatus === LocationPermissionStatus.GRANTED) {
      return {
        granted: true,
        status: currentStatus
      };
    }

    if (currentStatus === LocationPermissionStatus.LOCATION_SERVICES_DISABLED) {
      if (Platform.OS === 'ios') {
        // iOS: Show manual instruction since iOS doesn't have native location services prompt
        const message = 'Please enable location services manually in Settings, then restart the app to use location-based features.';
        return {
          granted: false,
          status: currentStatus,
          message
        };
      } else {
        // Android: Attempt permission request to trigger native location services prompt
        console.log('Android location services disabled, attempting permission request to trigger native prompt...');
        const granted = await requestLocationPermission();
        const newStatus = await checkLocationPermissionStatus();

        if (granted) {
          // User enabled location services and granted permission
          return {
            granted: true,
            status: newStatus
          };
        } else {
          // User declined to enable location services or denied permission
          const message = 'Please enable location services manually in Settings, then restart the app to use location-based features.';
          return {
            granted: false,
            status: newStatus,
            message
          };
        }
      }
    }

    if (currentStatus === LocationPermissionStatus.BLOCKED) {
      const message = 'Please enable location permission manually in Settings, then restart the app to use location-based features.';

      return {
        granted: false,
        status: currentStatus,
        message
      };
    }

    // Request permission for normal cases
    const granted = await requestLocationPermission();
    const newStatus = await checkLocationPermissionStatus();

    let message;
    if (!granted) {
      message = 'Please enable location permission in Settings to use location-based features.';
    }

    return {
      granted,
      status: newStatus,
      message
    };
  } catch (error) {
    console.warn('Error in requestLocationPermissionWithUX:', error);
    return {
      granted: false,
      status: LocationPermissionStatus.UNAVAILABLE,
      message: 'An error occurred while requesting location permission.'
    };
  }
};

/**
 * Extract city name from Google Places address components
 * Prioritizes locality > administrative_area_level_2 > administrative_area_level_1
 */
export const extractCityNameFromAddressComponents = (addressComponents: any[]): string => {
  let cityName = '';

  for (const component of addressComponents) {
    if (component.types.includes('locality') ||
        component.types.includes('administrative_area_level_2') ||
        component.types.includes('administrative_area_level_1')) {
      cityName = component.long_name;
      break;
    }
  }

  return cityName;
};

/**
 * Get city name from coordinates using Google Geocoding API
 */
export const getCityNameFromCoordinates = async (
  latitude: number,
  longitude: number
): Promise<string> => {
  const apiUrl = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${GOOGLE_PLACES_API_KEY}`;

  try {
    const response = await fetch(apiUrl);
    const data = await response.json();

    if (data.results && data.results.length > 0) {
      const addressComponents = data.results[0].address_components;
      return extractCityNameFromAddressComponents(addressComponents);
    }

    return '';
  } catch (error) {
    console.error('Error fetching city name:', error);
    return '';
  }
};

/**
 * Get current location with enhanced permission handling
 * Returns coordinates and city name with detailed permission status
 * @deprecated Use getCurrentLocationSilently() for component-level calls
 */
export const getCurrentLocationWithPermission = async (): Promise<LocationResult | null> => {
  const permissionResult = await requestLocationPermissionWithUX();

  if (!permissionResult.granted) {
    if (permissionResult.message) {
      Alert.alert('Location Permission Required', permissionResult.message);
    }
    return null;
  }

  return new Promise((resolve, reject) => {
    Geolocation.getCurrentPosition(
      async (info) => {
        const { latitude, longitude } = info.coords;
        const cityName = await getCityNameFromCoordinates(latitude, longitude);

        resolve({
          coordinates: {
            lat: latitude,
            lng: longitude
          },
          cityName
        });
      },
      (error) => {
        console.error('Error getting current location:', error);

        // Provide more specific error messages
        let errorMessage = 'Unable to get current location';
        if (error.code === 1) {
          errorMessage = 'Please enable location permission in Settings to use location-based features.';
        } else if (error.code === 2) {
          errorMessage = 'Please enable location services in Settings to use location-based features.';
        } else if (error.code === 3) {
          errorMessage = 'Location request timed out. Please try again.';
        }

        Alert.alert('Location Error', errorMessage);
        reject(error);
      },
      {
        enableHighAccuracy: false,
        timeout: 15000,
        maximumAge: 10000
      }
    );
  });
};

/**
 * Get current location with Android native location services prompt support
 * On Android: Attempts location access even if services appear disabled to trigger native prompt
 * On iOS: Only attempts if permission is granted
 * For use during app initialization - may show native system dialogs
 */
export const getCurrentLocationWithNativePrompt = async (): Promise<LocationResult | null> => {
  try {
    const status = await checkLocationPermissionStatus();

    if (Platform.OS === 'android') {
      // On Android, attempt location access even if services appear disabled
      // This allows the system to show native "Turn on location services?" dialog
      if (status === LocationPermissionStatus.GRANTED ||
          status === LocationPermissionStatus.LOCATION_SERVICES_DISABLED) {

        return new Promise((resolve) => {
          Geolocation.getCurrentPosition(
            async (info) => {
              try {
                const { latitude, longitude } = info.coords;
                const cityName = await getCityNameFromCoordinates(latitude, longitude);

                resolve({
                  coordinates: {
                    lat: latitude,
                    lng: longitude
                  },
                  cityName
                });
              } catch (error) {
                console.warn('Error getting city name:', error);
                resolve({
                  coordinates: {
                    lat: info.coords.latitude,
                    lng: info.coords.longitude
                  },
                  cityName: ''
                });
              }
            },
            (error) => {
              console.warn('Android location access failed (may have triggered native prompt):', error);
              // Silently fail - user may have declined native location services prompt
              resolve(null);
            },
            {
              enableHighAccuracy: false,
              timeout: 15000,
              maximumAge: 10000
            }
          );
        });
      }
    } else {
      // iOS: Only attempt if permission is granted (no native location services prompt)
      if (status === LocationPermissionStatus.GRANTED) {
        return new Promise((resolve) => {
          Geolocation.getCurrentPosition(
            async (info) => {
              try {
                const { latitude, longitude } = info.coords;
                const cityName = await getCityNameFromCoordinates(latitude, longitude);

                resolve({
                  coordinates: {
                    lat: latitude,
                    lng: longitude
                  },
                  cityName
                });
              } catch (error) {
                console.warn('Error getting city name:', error);
                resolve({
                  coordinates: {
                    lat: info.coords.latitude,
                    lng: info.coords.longitude
                  },
                  cityName: ''
                });
              }
            },
            (error) => {
              console.warn('iOS location access failed:', error);
              resolve(null);
            },
            {
              enableHighAccuracy: false,
              timeout: 15000,
              maximumAge: 10000
            }
          );
        });
      }
    }

    // Return null if conditions not met
    return null;
  } catch (error) {
    console.warn('Error in getCurrentLocationWithNativePrompt:', error);
    return null;
  }
};

/**
 * Get current location silently without permission requests or alerts
 * Only attempts to get location if permission is already granted
 * For use in components - does not show any user-facing messages
 */
export const getCurrentLocationSilently = async (): Promise<LocationResult | null> => {
  try {
    const status = await checkLocationPermissionStatus();

    if (status !== LocationPermissionStatus.GRANTED) {
      // Silently return null if permission not granted or location services disabled
      return null;
    }

    return new Promise((resolve) => {
      Geolocation.getCurrentPosition(
        async (info) => {
          try {
            const { latitude, longitude } = info.coords;
            const cityName = await getCityNameFromCoordinates(latitude, longitude);

            resolve({
              coordinates: {
                lat: latitude,
                lng: longitude
              },
              cityName
            });
          } catch (error) {
            console.warn('Error getting city name:', error);
            // Still resolve with coordinates even if city name fails
            resolve({
              coordinates: {
                lat: info.coords.latitude,
                lng: info.coords.longitude
              },
              cityName: ''
            });
          }
        },
        (error) => {
          console.warn('Error getting current location silently:', error);
          // Silently fail - no alerts
          resolve(null);
        },
        {
          enableHighAccuracy: false,
          timeout: 15000,
          maximumAge: 10000
        }
      );
    });
  } catch (error) {
    console.warn('Error in getCurrentLocationSilently:', error);
    return null;
  }
};

/**
 * Get current location coordinates only (without city name)
 * Useful when you only need coordinates
 */
export const getCurrentLocation = (): Promise<LocationCoordinates> => {
  return new Promise((resolve, reject) => {
    Geolocation.getCurrentPosition(
      (info) => {
        const { latitude, longitude } = info.coords;
        resolve({
          lat: latitude,
          lng: longitude
        });
      },
      (error) => {
        console.error('Error getting current location:', error);
        reject(error);
      },
      {
        enableHighAccuracy: false,
        timeout: 15000,
        maximumAge: 10000
      }
    );
  });
};

/**
 * Extract city name from Google Places details object
 * Used when user selects a location from GooglePlacesAutocomplete
 */
export const extractCityFromPlaceDetails = (details: any): string => {
  if (details && details.address_components) {
    return extractCityNameFromAddressComponents(details.address_components);
  }
  return '';
};