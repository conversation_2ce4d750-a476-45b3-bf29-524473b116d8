import axios from 'axios';
import { NEXT_PUBLIC_BASE_URL } from '@env';

export interface LocationCoordinates {
  lat: number | null;
  lan: number | null;
}

export interface SearchSuggestion {
  _id: string;
  courseName: string;
  course: string | boolean;
  coach_id: string;
  coachName: string;
  [key: string]: any;
}

export interface SearchResult {
  suggestions: SearchSuggestion[];
  courses: SearchSuggestion[];
  coaches: SearchSuggestion[];
  uniqueCoaches: SearchSuggestion[];
  academies: any[]; // Added academies field
}

export interface SearchParams {
  query: string;
  location?: LocationCoordinates;
  minLength?: number;
}

/**
 * Fetch search suggestions from the API
 * @param params - Search parameters including query, location, and minimum length
 * @returns Promise with search results containing suggestions, courses, and coaches
 */
export const fetchSearchSuggestions = async (params: SearchParams): Promise<SearchResult> => {
  const { query, location, minLength = 3 } = params;

  // Return empty results if query is too short
  if (query.length < minLength) {
    return {
      suggestions: [],
      courses: [],
      coaches: [],
      uniqueCoaches: [],
      academies: []
    };
  }

  // Build API URL with location if available
  const apiUrl = (location?.lat && location?.lan) 
    ? `${NEXT_PUBLIC_BASE_URL}/api/course/filter?lat=${location.lat}&long=${location.lan}&q=${query}`
    : `${NEXT_PUBLIC_BASE_URL}/api/course/filter?q=${query}`;

  try {
    const response = await axios.get(apiUrl);
    const { data } = response;

    const academies = Array.isArray(data.academyData) ? data.academyData : [];
    const suggestions = Array.isArray(data.coursesAndCoaches) ? data.coursesAndCoaches : [];

    if (!Array.isArray(suggestions)) {
      console.error('API response coursesAndCoaches is not an array:');
      return {
        suggestions: [],
        courses: [],
        coaches: [],
        uniqueCoaches: [],
        academies: []
      };
    }

    // Filter courses and coaches based on object structure
    const filteredCourses: SearchSuggestion[] = [];
    const filteredCoaches: SearchSuggestion[] = [];

    suggestions.forEach((item: SearchSuggestion) => {
      // Check if it's a course/session by looking for course-specific keys
      const isCourse = (
        item.classType ||
        item.sessionType ||
        item.facility ||
        item.fees ||
        item.dates ||
        (item.camp !== undefined) ||
        (item.courseName && item.courseName !== "No course")
      );

      // Check if it's a coach by looking for coach-specific keys
      const isCoach = (
        item.sportsCategories ||
        item.lastName ||
        (item.course === "false") ||
        (item.courseName === "No course") ||
        (item.email && !item.coachEmail) // coaches have 'email', courses have 'coachEmail'
      );

      if (isCourse && !isCoach) {
        filteredCourses.push(item);
      } else if (isCoach && !isCourse) {
        filteredCoaches.push(item);
      }
    });

    // Get unique coaches by coach_id
    const coachesById = suggestions.reduce((acc: Record<string, SearchSuggestion>, curr: SearchSuggestion) => {
      if (curr.coach_id) {
        acc[curr.coach_id] = curr;
      }
      return acc;
    }, {});

    const uniqueCoaches = Object.values(coachesById) as SearchSuggestion[];

    return {
      suggestions,
      courses: filteredCourses,
      coaches: filteredCoaches,
      uniqueCoaches,
      academies
    };

  } catch (error) {
    console.error('Error fetching search suggestions:', error);
    return {
      suggestions: [],
      courses: [],
      coaches: [],
      uniqueCoaches: [],
      academies: []
    };
  }
};

/**
 * Build search query string for navigation
 * @param query - Search query string
 * @param location - Optional location coordinates
 * @returns Formatted query string for navigation
 */
export const buildSearchQuery = (query: string, location?: LocationCoordinates): string => {
  if (location?.lat && location?.lan) {
    return `&q=${query}&lat=${location.lat}&long=${location.lan}`;
  }
  return `&q=${query}`;
};

/**
 * Filter suggestions to get only courses (excluding coaches)
 * @param suggestions - Array of search suggestions
 * @returns Array of course suggestions only
 */
export const filterCourses = (suggestions: SearchSuggestion[]): SearchSuggestion[] => {
  return suggestions.filter(item => {
    const isCourse = (
      item.classType ||
      item.sessionType ||
      item.facility ||
      item.fees ||
      item.dates ||
      (item.camp !== undefined) ||
      (item.courseName && item.courseName !== "No course")
    );

    const isCoach = (
      item.sportsCategories ||
      item.lastName ||
      (item.course === "false") ||
      (item.courseName === "No course") ||
      (item.email && !item.coachEmail)
    );

    return isCourse && !isCoach;
  });
};

/**
 * Filter suggestions to get only coaches
 * @param suggestions - Array of search suggestions
 * @returns Array of coach suggestions only
 */
export const filterCoaches = (suggestions: SearchSuggestion[]): SearchSuggestion[] => {
  return suggestions.filter(item => {
    const isCourse = (
      item.classType ||
      item.sessionType ||
      item.facility ||
      item.fees ||
      item.dates ||
      (item.camp !== undefined) ||
      (item.courseName && item.courseName !== "No course")
    );

    const isCoach = (
      item.sportsCategories ||
      item.lastName ||
      (item.course === "false") ||
      (item.courseName === "No course") ||
      (item.email && !item.coachEmail)
    );

    return isCoach && !isCourse;
  });
};

/**
 * Get unique coaches from suggestions array
 * @param suggestions - Array of search suggestions
 * @returns Array of unique coaches
 */
export const getUniqueCoaches = (suggestions: SearchSuggestion[]): SearchSuggestion[] => {
  const coachesById = suggestions.reduce((acc: Record<string, SearchSuggestion>, curr: SearchSuggestion) => {
    if (curr.coach_id) {
      acc[curr.coach_id] = curr;
    }
    return acc;
  }, {});

  return Object.values(coachesById) as SearchSuggestion[];
};

/**
 * Debounce function for search input
 * @param func - Function to debounce
 * @param delay - Delay in milliseconds
 * @returns Debounced function
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) & { cancel: () => void } => {
  let timeoutId: NodeJS.Timeout;

  const debouncedFunction = (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };

  debouncedFunction.cancel = () => {
    clearTimeout(timeoutId);
  };

  return debouncedFunction;
};