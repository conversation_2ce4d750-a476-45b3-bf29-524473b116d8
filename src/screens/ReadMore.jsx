import { View, Text, TouchableOpacity, StyleSheet, Image, Linking, SafeAreaView, ScrollView } from "react-native";
import React, {useState, useEffect} from 'react';
import { useNavigation } from '@react-navigation/native';
import HTML from 'react-native-render-html';
import Search from '../components/Search'
import Footer from '../components/footer'
import {NEXT_PUBLIC_BASE_URL} from '@env';

const ReadMore = () => {
  const navigation = useNavigation();
  const [isLoading, setIsLoading] = useState(true);
  const [readMore, setReadMore] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch(`${NEXT_PUBLIC_BASE_URL}/api/cms/cms-registration-details`);
        if (!response.ok) {
          throw new Error('Failed to fetch data');
        }
        const data = await response.json();
        setReadMore(data[0]); 
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);
  const handleRegisterNow = () => {
    Linking.openURL('https://3pd82u71m8.execute-api.ap-south-1.amazonaws.com/profile/basic_details#signup');
  };

  const handleReadMore = () => {
    navigation.navigate("ReadMore");
  }
  return (
 <SafeAreaView>
  <ScrollView>
  <View style={styles.container}>
      <View style={styles.textContainer}>
        <Text style={styles.title}>BECOME AN INSTRUCTOR</Text>
        <HTML tagsStyles={{
          p: { margin: 0, padding: 0, color: "#000", },
          h2: { margin: 0, padding: 0, color: "#000", fontSize: 15 },
          br: { display: 'none' },
          li: { margin: 0, paddingHorizontal: "2%", color: "#000", alignItems: 'center' },
          ul: { marginHorizontal: "5%", alignItems: 'center', padding: 0 },
        }} source={{ html:  readMore?.registrationData }} />
  
      </View>
    </View>
  </ScrollView>
 </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "center",
    maxWidth: "100%",
    paddingHorizontal: "5%",
    alignItems: "center",
    marginTop: "5%"
  },
  textContainer: {
    width: "100%",
    marginBottom: 16,
    paddingHorizontal:"2%"
  },
  title: {
    fontSize: 18,
    // fontWeight: "bold",
    marginBottom: 16,
    fontFamily: 'Lato-Bold',
    color: "#000",
  },
  description: {
    fontSize: 16,
    marginBottom: 16,
    fontFamily: 'Lato-Regular',
    color: "#000",
    lineHeight: 22,
  },
  listContainer: {
    width: "90%",
    margin: "auto",
  },
  listItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  bullet: {
    fontSize: 20,
    marginRight: 5,
  },
  listItemText: {
    fontSize: 15,
    fontFamily: 'Lato-Regular',
    color: "#000",
    lineHeight: 22,
  },
  buttonContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 20,
  },
  button: {
    backgroundColor: "#E31F26",
    paddingVertical: "3%",
    paddingHorizontal: 10,
    borderRadius: 5,
  },
  buttonText: {
    color: "white",
    fontSize: 14,
    fontFamily: 'Lato-Bold'
  },
  readMoreContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginLeft: 20
  },
  readMoreText: {
    textDecorationLine: "underline",
    fontSize: 14,
    marginRight: 1,
    fontFamily: 'Lato-Bold',
    color: "#000",
    textDecorationColor: "#000",
    fontWeight: "600"
  },
  instructorImg: {
    width: "100%",
    marginTop: "8%",
  },
});

export default ReadMore
