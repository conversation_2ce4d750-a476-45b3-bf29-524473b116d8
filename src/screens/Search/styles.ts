import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
  safeAreaContainer: {
    flex: 1,
  },
  mainContainer: {
    backgroundColor: '#fff',
    minHeight: '100%',
  },
  // Location and Search Container - matching search.jsx
  locationAndSearchContainer: {
    paddingHorizontal: "5%",
    paddingVertical: "5%",
    backgroundColor: "#0EA5E9",
    gap: 20,
  },

  // GooglePlaces wrapper to prevent overlap
  googlePlacesWrapper: {
    position: 'relative',
    zIndex: 1000,
  },

  // Search input container - matching search.jsx structure
  searchInputContainer: {
    flexDirection: 'row',
    borderColor: '#ccc',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderRadius: 5,
    color: '#000',
    position: 'relative',
    zIndex: 1,
  },

  // Search input field - matching search.jsx
  searchInput: {
    flex: 1,
    padding: 10,
    color: '#000',
    fontSize: 16,
    fontFamily: 'Lato-Regular'
  },

  // Search button - matching search.jsx
  searchButton: {
    backgroundColor: "#000",
    color: "white",
    display: "flex",
    flexDirection: "row",
    borderRadius: 5,
    padding: 12,
    justifyContent: "center",
    marginTop: "3%",
    alignItems: "center"
  },

  // Search button icon
  searchButtonIcon: {
    width: 20,
    height: 20,
  },

  // Search button text
  searchButtonText: {
    color: "#fff",
    fontSize: 13,
    marginLeft: 5,
    fontFamily: 'Lato-Regular'
  },
  // Results container
  resultsContainer: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    paddingTop: 16,
  },

  // Results title
  resultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    paddingHorizontal: 16,
    paddingBottom: 8,
    fontFamily: 'Lato-Bold',
  },

  // FlatList content
  flatListContent: {
    paddingBottom: 20,
  },

  // Loading container
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },

  // Loading text
  loadingText: {
    fontSize: 16,
    color: '#666',
    fontFamily: 'Lato-Regular',
  },

  // No results container
  noResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },

  // No results text
  noResultsText: {
    fontSize: 16,
    color: '#666',
    fontFamily: 'Lato-Regular',
    textAlign: 'center',
  },

  // Section header
  sectionHeader: {
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },

  // Section title
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    fontFamily: 'Lato-Bold',
  },

  // Not found container
  notFoundContainer: {
    paddingHorizontal: 16,
    paddingVertical: 20,
    alignItems: 'center',
  },

  // Not found text
  notFoundText: {
    fontSize: 14,
    color: '#999',
    fontFamily: 'Lato-Regular',
    fontStyle: 'italic',
  },

  // Search prompt container
  searchPromptContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 60,
  },

  // Search prompt text
  searchPromptText: {
    fontSize: 18,
    color: '#666',
    fontFamily: 'Lato-Regular',
    textAlign: 'center',
    lineHeight: 24,
  },

  // Search instruction container
  searchInstructionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 40,
  },

  // Search instruction text
  searchInstructionText: {
    fontSize: 16,
    color: '#888',
    fontFamily: 'Lato-Regular',
    textAlign: 'center',
  },

  // No results main container
  noResultsMainContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 60,
  },

  // No results main text
  noResultsMainText: {
    fontSize: 18,
    color: '#333',
    fontFamily: 'Lato-Bold',
    textAlign: 'center',
    marginBottom: 8,
    lineHeight: 24,
  },

  // No results sub text
  noResultsSubText: {
    fontSize: 14,
    color: '#666',
    fontFamily: 'Lato-Regular',
    textAlign: 'center',
  },
});


export const googlePlacesStyles = {
  container: {
    flex: 0,
    position: 'relative',
    zIndex: 1000,
  },
  textInputContainer: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
    borderTopWidth: 0,
    borderRadius: 5,
  },
  textInput: {
    marginLeft: 0,
    marginRight: 0,
    height: 48,
    color: '#000',
    borderColor: '#ccc',
    fontSize: 16,
    borderRadius: 5,
    borderWidth: 1,
    padding: 10,
    fontFamily: 'Lato-Regular'
  },
  listView: {
    backgroundColor: '#fff',
    position: 'absolute',
    top: 48,
    left: 0,
    right: 0,
    zIndex: 1001,
    borderColor: '#ccc',
    borderWidth: 1,
    borderTopWidth: 0,
    borderRadius: 5,
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    maxHeight: 200,
  },
  row: {
    backgroundColor: '#fff',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  description: {
    color: '#000',
    fontSize: 16,
    fontFamily: 'Lato-Regular',
  },
  separator: {
    height: 1,
    backgroundColor: '#eee',
  },
};