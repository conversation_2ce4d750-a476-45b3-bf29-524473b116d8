import React from "react";
import { View, Text, TouchableOpacity, Image } from "react-native";
import { Rating } from 'react-native-ratings';
import HTML from 'react-native-render-html';

const placeholder = require("../../assets/placeholder.png");
const location = require("../../assets/locatiothree.png");
const redirect = require("../../assets/redirect.png");

export default function CourseCard({ course, index, navigation, handleMapNavigation, styles }) {
  return (
    <View key={index} style={styles.container}>
      <View style={styles.sectionOne}>
        <TouchableOpacity
          style={styles.imageContainer}
          onPress={() => {
            const courseId = course?._id;
            navigation.navigate("Courses", { courseId });
          }}
        >
          <Image
            source={course?.images?.[0]?.url ? { uri: course.images[0].url } : placeholder}
            style={styles.image}
          />
        </TouchableOpacity>
        <View style={styles.sectionTwo}>
          <View style={styles.rowReview}>
            <Rating
              type='star'
              ratingCount={5}
              startingValue={course?.ratings?.stars || 0}
              imageSize={15}
              readonly
            />
            <Text style={styles.labelReview}>
              {course?.ratings?.noOfRatings || 0} Reviews
            </Text>
          </View>

          {course?.maxGroupSize && (
            <View style={{ flexDirection: "row" }}>
              <Text style={styles.labelReview}>Group Size:</Text>
              <Text style={styles.facility}>{course.maxGroupSize}</Text>
            </View>
          )}

          {course?.fees?.feesCourse == null ? (
            <TouchableOpacity
              style={styles.buttonContainer}
              onPress={() => {
                navigation.navigate("Courses", { courseId: course?._id });
              }}
              activeOpacity={0.8}
            >
              <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 16, textAlign: 'center', lineHeight: 35 }}>
                Book Now
              </Text>
            </TouchableOpacity>
          ) : (
            <View style={styles.priceContainer}>
              <Text style={styles.priceText}>{`₹ ${Math.round(course.fees.feesCourse)}`}</Text>
            </View>
          )}

          <TouchableOpacity style={styles.rowView} onPress={() => {
            navigation.navigate("Courses", { courseId: course?._id });
          }}>
            <Text style={styles.labelView}>View More</Text>
            <Image source={redirect} style={{ width: 15, height: 15 }} />
          </TouchableOpacity>
        </View>
      </View>

      {course?.maxGroupSize === 1 && course?.classType?.toLowerCase() === "class" && (
        <View style={styles.classTypeContainer}>
          <Text style={styles.classType}>Individual Session</Text>
        </View>
      )}

      <View style={styles.horizontalLine} />
      <View style={styles.detailsContainer}>
        <Text style={styles.title}>
          {course?.courseName} <Text style={{ color: "grey" }}>({course?.classType})</Text>
        </Text>
        <View style={styles.row}>
          <Text style={styles.label}>Coach Name:</Text>
          <Text style={styles.value}>{course?.coachName || 'N/A'}</Text>
        </View>
        <View style={styles.row}>
          <Text style={[styles.label, {paddingTop: 0 }]}>Description:</Text>
          <View style={{ flex: 1 }}>
            {course?.description ? (
              <HTML
                contentWidth={0}
                tagsStyles={{
                  p: { margin: 0, paddingHorizontal: '2%', color: '#444', fontFamily: 'Lato-Regular', fontSize: 14 },
                  li: { margin: 0, paddingHorizontal: '2%', color: '#444', alignItems: 'center', fontFamily: 'Lato-Regular', fontSize: 14 },
                  ul: { marginHorizontal: '5%', alignItems: 'center', padding: 0 },
                  h2: { margin: 0, padding: 0, color: '#000', fontSize: 15 },
                }}
                source={{ html: course.description.length > 150 ? course.description.slice(0, 150) + '...' : course.description }}
              />
            ) : null}
          </View>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Proficiency:</Text>
          <View style={styles.proficiencyContainer}>
            {course?.proficiency?.map((p, idx) => (
              <Text key={idx} style={styles.proficiency}>{p}</Text>
            ))}
          </View>
        </View>
        {course?.facility?.name && (
          <View style={styles.row}>
            <Text style={styles.label}>Facility:</Text>
            <Text style={styles.value}>{course?.facility?.name}</Text>
          </View>
        )}
        {course?.facility?.addressLine1 && (
          <View style={styles.row}>
            <Image source={location} />
            <Text style={styles.value}>
              {course?.facility?.addressLine1}, {course?.facility?.addressLine2}, {course?.facility?.city}, {course?.facility?.state}
            </Text>
            {course?.facility?.location?.coordinates?.[0] && course?.facility?.location?.coordinates?.[1] && (
              <TouchableOpacity onPress={() => handleMapNavigation(course.facility.location.coordinates[0], course.facility.location.coordinates[1])}>
                <Text style={styles.showOnMap}>Show on map</Text>
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>
    </View>
  );
} 