import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
    container: {
      backgroundColor: "#fff",
      borderRadius: 5,
      marginBottom: 20,
      marginHorizontal: 10,
      borderWidth: 0.5,
      flex: 1,
      minWidth: 0,
      maxWidth: '100%',
    },
    titleText: {
      fontSize: 16,
      color: "#000",
      marginLeft: "5%",
      marginBottom: "2%",
      flexWrap: 'wrap',
      maxWidth: '95%',
    },
    notifyButton: {
      backgroundColor: "#0EA5E9",
      padding: 10,
      borderRadius: 5,
      marginTop: 10,
    },
    input: {
      borderWidth: 1,
      borderColor: '#ccc',
      padding: 8,
      borderRadius: 5,
      width: '100%',
      marginVertical: 10,
    },
    noCoursesContainer: {
      // padding: 20,
    },
    sectionOne: {
      flexDirection: "row",
      flexWrap: 'wrap',
      minWidth: 0,
      maxWidth: '100%',
    },
    sectionTwo: {
      padding: 10,
      flex: 1,
      minWidth: 0,
      maxWidth: '100%',
    },
    rowReview: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      flexWrap: 'wrap',
      minWidth: 0,
      maxWidth: '100%',
    },
    labelReview: {
      color: "gray",
      marginLeft: 5,
      flexShrink: 1,
      minWidth: 0,
      maxWidth: '100%',
    },
    rowView: {
      flexDirection: "row",
      alignItems: "center",
      marginTop: 10,
      flexWrap: 'wrap',
      minWidth: 0,
      maxWidth: '100%',
    },
    labelView: {
      color: '#0EA5E9',
      fontWeight: '600',
      fontSize: 15,
      marginRight: 5,
    },
    classTypeContainer: {
      position: "absolute",
      top: 10,
      left: 10,
      backgroundColor: "#0EA5E9",
      paddingHorizontal: 10,
      paddingVertical: 4,
      borderRadius: 5,
    },
    classType: {
      color: "#fff",
      fontSize: 12,
    },
    imageContainer: {
      width: "50%",
      height: 150,
      borderRadius: 5,
      margin: 10,
    },
    image: {
      width: "100%", height: "100%",
      borderRadius: 5,
      maxWidth: '100%',
      resizeMode: 'cover',
    },
    horizontalLine: {
      height: 1,
      backgroundColor: "#ccc",
      marginHorizontal: 10,
    },
    detailsContainer: {
      padding: 10,
      minWidth: 0,
      maxWidth: '100%',
    },
    title: {
      fontWeight: "bold",
      fontSize: 16,
      color: '#000',
      flexWrap: 'wrap',
      minWidth: 0,
      maxWidth: '100%',
    },
    row: {
      flexDirection: "row",
      marginVertical: 4,
      flexWrap: 'wrap',
      minWidth: 0,
      maxWidth: '100%',
    },
    label: {
      fontWeight: "600",
      marginRight: 5,
      width: 100,
      flexShrink: 0,
      minWidth: 0,
      maxWidth: 100,
      color: 'gray',
    },
    value: {
      flex: 1,
      color: "black",
      flexWrap: 'wrap',
      minWidth: 0,
      maxWidth: '100%',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
    },
    proficiencyContainer: {
      flexDirection: "row",
      flexWrap: "wrap",
      minWidth: 0,
      maxWidth: '100%',
    },
    proficiency: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      marginRight: 6,
      marginTop: 4,
      backgroundColor: "#E0F7FA",
      borderRadius: 4,
      color: "#00796B",
      fontSize: 12,
      flexWrap: 'wrap',
      minWidth: 0,
      maxWidth: '100%',
    },
    buttonContainer: {
      backgroundColor: "rgb(220,38,38)",
      borderRadius: 4,
      overflow: "hidden",
      marginVertical: 5,
      height: 35,
      width: '100%',
      justifyContent: 'center',
      alignItems: 'center',
    },
    priceContainer: {
      backgroundColor: "#E31F26",
      padding: 8,
      borderRadius: 4,
      alignItems: "center",
    },
    priceText: {
      color: "#fff",
      fontWeight: "bold",
      fontSize: 16,
    },
    showOnMap: {
      color: "#0EA5E9",
      marginLeft: 8,
      fontSize: 13,
    }
  });