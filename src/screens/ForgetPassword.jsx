import React, { useState } from 'react';
import {
    View,
    Text,
    TextInput,
    StyleSheet,
    TouchableOpacity,
    KeyboardAvoidingView,
    Platform,
    TouchableWithoutFeedback,
    Keyboard,
    Alert,
    Image,
    ScrollView
} from 'react-native';
import { NEXT_PUBLIC_BASE_URL } from '@env';
import {useNavigation} from '@react-navigation/native';

const ForgetPassword = () => {
    const [email, setEmail] = useState('');
    const [errorMessage, setErrorMessage] = useState('');
    const navigation = useNavigation();

    const emailRegExp = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;

    const handleSubmit = async () => {
        setErrorMessage('');
        if (!emailRegExp.test(email)) {
            setErrorMessage('Please enter a valid email address.');
            return;
        }

        try {
            const response = await fetch(`${NEXT_PUBLIC_BASE_URL}/api/player/requestResetPassword/${email}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            const data = await response.json();
            if (data.message) {
                Alert.alert('Success', 'Password reset link has been sent to your email.');
                navigation.navigate('SignIn');
                
            } else {
                Alert.alert('Error', data.error || 'Something went wrong. Please try again.');
            }
        } catch (error) {
            Alert.alert('Error', 'Unable to send password reset link. Please try again later.');
        }
    };

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={styles.container}
        >
            <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
                <ScrollView contentContainerStyle={styles.scrollContainer}>
                    <View style={styles.inner}>
                        <View style={styles.title}>
                            <Image
                                source={require('../assets/MainKhelCoach.png')}
                                style={{ width: 150, height: 50 }} // Adjust width and height as needed
                            />
                        </View>
                        <TextInput
                            style={styles.input}
                            placeholder="Enter your email"
                            keyboardType="email-address"
                            autoCapitalize="none"
                            value={email}
                            onChangeText={setEmail}
                        />
                        {errorMessage ? <Text style={styles.errorText}>{errorMessage}</Text> : null}
                        <TouchableOpacity style={styles.button} onPress={handleSubmit}>
                            <Text style={styles.buttonText}>Submit</Text>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </TouchableWithoutFeedback>
        </KeyboardAvoidingView>
    );
};

export default ForgetPassword;

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    scrollContainer: {
        justifyContent: 'center',
    },
    inner: {
        padding: 24,
        justifyContent: 'center',
    },
    title: {
        marginBottom: "5%",
        display: "flex",
        textAlign: 'center',
        justifyContent: "center",
    },
    input: {
        height: 40,
        borderColor: '#ccc',
        borderWidth: 1,
        paddingLeft: 8,
        borderRadius: 4,
        color: "#000",
        fontFamily: "Lato-Regular"
    },
    errorText: {
        color: 'red',
        marginBottom: 12,
        textAlign: 'center',
        fontFamily: "Lato-Regular"

    },
    button: {
        backgroundColor: '#0EA5E9',
        padding: '3%',
        borderRadius: 4,
        alignItems: 'center',
        marginVertical: '3%',
        color: "#000",
    },
    buttonText: {
        color: '#fff',
        fontSize: 16,
        fontFamily: "Lato-Regular"
    },
});