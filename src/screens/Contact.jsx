import React, { useState } from 'react';
import { StyleSheet, Text, View, TextInput, TouchableOpacity, Modal, Alert, SafeAreaView, ScrollView } from 'react-native';
import axios from 'axios';
import { NEXT_PUBLIC_BASE_URL } from '@env';

const Contact = () => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    mobile: '',
    message: '',
    userType: 'coach',
  });
  const [successModal, setSuccessModal] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [mobileError, setMobileError] = useState('');
  const [emailError, setEmailError] = useState('');

  const handleChange = (name, value) => {
    setFormData({
      ...formData,
      [name]: value,
    });

    if (name === 'mobile') {
      validateMobile(value);
    }

    if (name === 'email') {
      validateEmail(value);
    }
  };

  const validateMobile = (value) => {
    const mobileRegex = /^\d{10}$/;
    if (!mobileRegex.test(value)) {
      setMobileError('Phone number must be exactly 10 digits and contain only numbers');
    } else {
      setMobileError('');
    }
  };

  const validateEmail = (value) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      setEmailError('Invalid email address');
    } else {
      setEmailError('');
    }
  };

  const handleSubmit = async () => {
    if (mobileError || emailError) {
      Alert.alert('Validation Error', 'Please correct the errors before submitting.');
      return;
    }

    const myHeaders = new Headers();
    myHeaders.append('Content-Type', 'application/json');

    const raw = JSON.stringify({
      firstName: formData.firstName,
      lastName: formData.lastName,
      mobile: formData.mobile,
      email: formData.email,
      message: formData.message,
      userType: 'coach',
    });

    const requestOptions = {
      method: 'POST',
      headers: myHeaders,
      body: raw,
    };

    try {
      const response = await fetch(`${NEXT_PUBLIC_BASE_URL}/api/contactUs`, requestOptions);
      const result = await response.json();
      if (response.ok) {
        setSuccessModal(true);
        Alert.alert('Data sent successfully');
      } else {
        setSuccessModal(true);
        Alert.alert(result.error);
      }
    } catch (error) {
      console.error('Error:', error);
    }
  };

  const isFormValid = !mobileError && !emailError && formData.mobile && formData.email;

  return (
    <SafeAreaView>
      <ScrollView>
        <View style={styles.container}>
          <Text style={styles.header}>Get in touch</Text>
          <Text style={styles.description}>
            We value your feedback and are committed to providing you with the best possible experience. Get in touch today and take the first step towards reaching your goals!
          </Text>
          <TextInput
            style={styles.input}
            placeholderTextColor="#000"
            placeholder="First name"
            value={formData.firstName}
            onChangeText={(value) => handleChange('firstName', value)}
          />
          <TextInput
            style={styles.input}
            placeholderTextColor="#000"
            placeholder="Last name"
            value={formData.lastName}
            onChangeText={(value) => handleChange('lastName', value)}
          />
          <TextInput
            style={styles.input}
            placeholderTextColor="#000"
            placeholder="Email"
            value={formData.email}
            onChangeText={(value) => handleChange('email', value)}
          />
          {emailError ? <Text style={styles.errorText}>{emailError}</Text> : null}
          <TextInput
            style={styles.input}
            placeholderTextColor="#000"
            placeholder="Phone number"
            value={formData.mobile}
            onChangeText={(value) => handleChange('mobile', value)}
            keyboardType="numeric"
          />
          {mobileError ? <Text style={styles.errorText}>{mobileError}</Text> : null}
          <TextInput
            style={styles.textArea}
            placeholderTextColor="#000"
            placeholder="Message"
            value={formData.message}
            onChangeText={(value) => handleChange('message', value)}
            multiline
          />
          <TouchableOpacity
            style={[styles.button, !isFormValid && styles.buttonDisabled]}
            onPress={handleSubmit}
            disabled={!isFormValid}
          >
            <Text style={styles.buttonText}>Send message</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default Contact;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: 'white',
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    color: "#000"
  },
  description: {
    fontSize: 16,
    marginBottom: 20,
    color: "#000"
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    padding: 10,
    borderRadius: 5,
    marginBottom: 15,
    color: "#000"
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#ccc',
    padding: 10,
    borderRadius: 5,
    height: 100,
    textAlignVertical: 'top',
    marginBottom: 15,
    color: "#000"
  },
  button: {
    backgroundColor: '#1E90FF',
    padding: 15,
    borderRadius: 5,
    alignItems: 'center',
  },
  buttonDisabled: {
    backgroundColor: '#7EC8E3',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  errorText: {
    color: 'red',
    marginBottom: 15,
  }
});