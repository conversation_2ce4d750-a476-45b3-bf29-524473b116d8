import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  Image,
  TouchableOpacity,
  Linking,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { useRoute } from '@react-navigation/native';
import { useNavigation } from '@react-navigation/native';
import axios from 'axios';
import { NEXT_PUBLIC_BASE_URL, COACH_ID_TOKEN } from '@env';
import { useAuth } from '../Context/AuthContext';
import Footer from '../components/footer';
import CoachCourseCards from '../components/CoachCourseCards';
import AsyncStorage from '@react-native-async-storage/async-storage';
// Assuming these images are in your assets directory
const coachImage = require('../assets/coachImage.png');
const verifiedTick = require('../assets/verified.png');
const qualificationIcon = require('../assets/coachQualification.png');
const experienceIcon = require('../assets/coachExperience.png');
const playingIcon = require('../assets/playingExperience.png');
const awardsIcon = require('../assets/coachAwards.png');

const CoachProfile = ({navigation, route}) => {
  
  const ID = route?.params?.ID;
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [coachData, setCoachData] = useState();

  // Function to handle opening URLs
  const handleMapNavigation = (latitude, longitude) => {
    const url = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
    Linking.openURL(url);
  };

  const fetchDataCoach = async () => {
    // Check if ID is provided
    if (!ID) {
      setError('Coach not found. Please try again.');
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await axios.get(`${NEXT_PUBLIC_BASE_URL}/api/coach/player/${ID}`);
      console.log("Response Inside Coach Profile: ", response.data);
      
      // Check if response has valid data
      if (!response.data || Object.keys(response.data).length === 0) {
        setError('Coach not found. Please try again.');
      } else {
        setCoachData(response.data);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      
      // Handle different types of errors
      if (error.response?.status === 404) {
        setError('Coach not found. Please try again.');
      } else if (error.response?.status >= 500) {
        setError('Server error. Please try again later.');
      } else if (error.code === 'NETWORK_ERROR') {
        setError('Network error. Please check your connection and try again.');
      } else {
        setError('Something went wrong. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDataCoach();
  }, [ID]);

  const handleGoBack = () => {
    navigation.goBack();
  };

  const handleRetry = () => {
    setError(null);
    setIsLoading(true);
    // Re-fetch the data
    const fetchDataCoach = async () => {
      try {
        const response = await axios.get(`${NEXT_PUBLIC_BASE_URL}/api/coach/player/${ID}`);
        if (!response.data || Object.keys(response.data).length === 0) {
          setError('Coach not found. Please try again.');
        } else {
          setCoachData(response.data);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        if (error.response?.status === 404) {
          setError('Coach not found. Please try again.');
        } else if (error.response?.status >= 500) {
          setError('Server error. Please try again later.');
        } else if (error.code === 'NETWORK_ERROR') {
          setError('Network error. Please check your connection and try again.');
        } else {
          setError('Something went wrong. Please try again.');
        }
      } finally {
        setIsLoading(false);
      }
    };
    fetchDataCoach();
  };

  const renderSectionCards = (title, data, icon) => {
    return (
      <View style={styles.coachDetails}>
        <LinearGradient
          colors={['rgba(227, 31, 38, 0.2)', 'rgba(0, 174, 239, 0.2)']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.gradient}>
          <Image source={icon} style={styles.sectionIcon} />
          <Text style={styles.coachDetailsTitle}>{title}</Text>
          {data.map((item, index) => (
            <View key={index} style={styles.coachDetailsItem}>
              <Text style={styles.bulletPoint}>•</Text>
              <Text style={styles.year}>{item.year}</Text>
              <Text style={styles.description}>{item.description}</Text>
            </View>
          ))}
        </LinearGradient>
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
        <Text style={styles.loadingText}>Loading coach details...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorTitle}>Oops!</Text>
        <Text style={styles.errorMessage}>{error}</Text>
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.retryButton} onPress={handleRetry}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.goBackButton} onPress={handleGoBack}>
            <Text style={styles.goBackButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  const isNonEmpty = (data) => {
    return data && (Array.isArray(data) ? data.length > 0 : true);
  }

  // Profession string with spaces
  const sportsCategoriesWithSpace = coachData?.sportsCategories?.map(category => category + ' ');

  return (
    <ScrollView>
      <View style={styles.container}>
        <View style={[styles.card, styles.coachCard]}>
          <Image source={{ uri: coachData?.profileImg || '' }} style={styles.coachImage} />
          <View style={styles.nameContainer}>
            <Text style={styles.coachName}>
              {coachData?.firstName} {coachData?.lastName}
            </Text>
            <Image source={verifiedTick} style={styles.verifiedTickIcon} />
          </View>
          {coachData?.sportsCategories?.map((sport, ind) => {
            <View style={styles.section}>
              <Text style={styles.sectionHeading}>Profession:</Text>
              <Text style={styles.sectionText}>{sport[ind]} </Text>
            </View>
          })}
          {/* //{product.coach.sportsCategories.join(', ')} */}
          <View style={[styles.section, {width:"80%"}]}>
            <Text style={styles.sectionHeading}>Profession:</Text>
            {Array.isArray(coachData?.sportsCategories) && coachData.sportsCategories.length > 0 ? (
              <Text style={styles.sectionText}>{`${sportsCategoriesWithSpace}Coach`}</Text>
            ) : (
              <Text style={styles.sectionText}>No Profession</Text>
            )}
          </View>
          <View style={styles.section}>
            <Text style={styles.sectionHeading}>Language:</Text>
            <Text style={styles.sectionText}>{coachData?.language}</Text>
          </View>
          <View style={styles.section}>
            <Text style={styles.sectionHeading}>Experience:</Text>
            <Text style={styles.sectionText}>{coachData?.experience} Years</Text>
          </View>
          <Text style={[styles.venueHeading, styles.sectionHeading]}>Venue: </Text>
          {coachData?.linkedFacilities && coachData?.linkedFacilities.length > 0 ? (
            coachData?.linkedFacilities.map((venue, index) => (

              <View key={index} style={styles.venueItem}>
                <Text style={styles.bulletPoint}>•</Text>
                <View style={{ flex: 1 }}>
                  <Text style={styles.venueName}>{venue.name} {venue.addressLine1} {venue.addressLine2} {venue.city}</Text>
                </View>
                {venue?.location?.coordinates[0] && venue.location?.coordinates[1] && (
                  <TouchableOpacity onPress={() => { handleMapNavigation(venue?.location?.coordinates[0], venue?.location?.coordinates[1]) }}>
                    <Text style={styles.showOnMap}>Show on map</Text>
                  </TouchableOpacity>
                )}

              </View>
            ))
          ) : (
            <Text>No venue information available</Text>
          )}
        </View>
        {coachData?.award &&
          renderSectionCards(
            'Awards',
            coachData?.award,
            require('../assets/coachAwards.png'),
          )}
        {coachData?.coachingExperience &&
          renderSectionCards(
            'Coaching Experience',
            coachData?.coachingExperience,
            require('../assets/coachExperience.png'),
          )}

        {coachData?.coachingQualifications &&
          renderSectionCards(
            'Coaching Qualifications',
            coachData?.coachingQualifications,
            require('../assets/coachQualification.png'),
          )}
        {coachData?.playerExperience &&
          renderSectionCards(
            'Playing Experience',
            coachData?.playerExperience,
            require('../assets/coachQualification.png'),
          )}

      </View>
      <CoachCourseCards ID={ID} coachName={coachData?.firstName}/>
      <Footer />

    </ScrollView>
  );
};

export default CoachProfile;

const styles = StyleSheet.create({
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
    fontFamily: 'Lato-Regular',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    backgroundColor: 'white',
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
    fontFamily: 'Lato-Bold',
  },
  errorMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 24,
    fontFamily: 'Lato-Regular',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 15,
  },
  retryButton: {
    backgroundColor: '#E31F26',
    paddingHorizontal: 25,
    paddingVertical: 12,
    borderRadius: 6,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Lato-Bold',
  },
  goBackButton: {
    backgroundColor: 'transparent',
    paddingHorizontal: 25,
    paddingVertical: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#E31F26',
  },
  goBackButtonText: {
    color: '#E31F26',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Lato-Bold',
  },
  container: {
    padding: 20,
    backgroundColor: '#F5F5F5', // Light grey background
  },
  card: {
    borderRadius: 10,
    overflow: 'hidden',
  },
  coachCard: {
    backgroundColor: '#FAFBFCFF',
    borderRadius: 10,
    marginBottom: 20,
    padding: 15,
  },
  recommendedCard: {
    marginTop: '5%',
    backgroundColor: '#FAFBFCFF',
    borderRadius: 10,
    marginBottom: 20,
    padding: 15,
  },
  gradient: {
    borderRadius: 10,
    padding: 20,
  },
  coachImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    alignSelf: 'center',
    marginBottom: 10, // Added space below the image
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  coachName: {
    fontSize: 20,
    fontFamily:"Lato-Bold",
    color: '#000',
  },
  verifiedTickIcon: {
    width: 30,
    height: 30,
    marginLeft: 5,
  },
  section: {
    marginTop: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionHeading: {
    fontFamily:"Lato-Bold",
    color: '#0EA5E9',
  },
  sectionText: {
    marginLeft: 5,
    color: '#000',
    fontFamily: 'Lato-Regular'
  },
  venueHeading: {
    marginTop: 10,
    fontFamily:"Lato-Bold",
    fontSize: 16,
    color: '#000',
  },
  venueItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
    fontFamily: 'Lato-Regular'
  },
  bulletPoint: {
    fontSize: 22,
    color: 'red',
    marginRight: 5,
    fontWeight: 'bold',
  },
  venueName: {
    // flex: 1,
    marginRight: 5,
    flexWrap: 'wrap',
    color: '#000',
    fontFamily: 'Lato-Regular'
  },
  showOnMap: {
    color: '#0EA5E9',
    fontWeight: '400',
    textDecorationLine: 'underline',
    alignSelf: 'flex-start',
    fontFamily: 'Lato-Regular'
  },
  coachDetails: {
    marginTop: 20,
    // padding: 15,
    backgroundColor: '#FAFBFCFF',
    borderRadius: 10,
    // elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  coachDetailsTitle: {
    fontFamily:"Lato-Bold",
    fontSize: 16,
    marginBottom: 10,
    color: '#000',
  },
  coachDetailsItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
    fontFamily: 'Lato-Regular'
  },
  year: {
    fontFamily:"Lato-Bold",
    marginRight: '3%',
    color: '#0EA5E9',
  },
  description: {
    flex: 1,
    color: 'grey',
    fontFamily: 'Lato-Regular'
  },
  sectionIcon: {
    width: 30,
    height: 30,
    position: 'absolute',
    right: 15,
    top: 15,
  },
});