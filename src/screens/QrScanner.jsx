import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet, Alert, ActivityIndicator, Linking, Platform } from "react-native";
import { Camera, useCameraDevice, useCameraPermission, useCodeScanner } from "react-native-vision-camera";
import { NEXT_PUBLIC_BASE_URL } from "@env";
import { useAuth } from "../Context/AuthContext";
import { useNavigation } from '@react-navigation/native';

const QrScanner = () => {
  const { hasPermission, requestPermission, status } = useCameraPermission();
  const [scanning, setScanning] = useState(false);
  const [requestingPermission, setRequestingPermission] = useState(false);
  const { isLoggedIn, logout, playerData, setUser, userToken } = useAuth();
  const navigation = useNavigation();
  const [showScanner, setShowScanner] = useState(true);

  const device = useCameraDevice('back');
  const codeScanner = useCodeScanner({
    codeTypes: ['qr', 'ean-13'],
    onCodeScanned: (codes) => {
      if (codes && !scanning) {
        const [data, error] = safeJsonParse(codes[0]?.value);
        if (error || !data) {
          setScanning(true);
          setShowScanner(false);
          Alert.alert("Invalid QR", "The scanned QR code is not valid for attendance.", [
            {
              text: "OK",
              onPress: () => {
                setScanning(false);
                setShowScanner(true);
              }
            }
          ]);
          return;
        }
        setScanning(true);
        handleAttendance(data);
      }
    }
  });

  useEffect(() => {
    handleRequestPermission();
  }, []);

  const handleAttendance = async (data) => {
    try {
      const myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");
      myHeaders.append("Authorization", `Bearer ${userToken}`);

      let raw = {
        player: playerData?._id,
      };

      if (data?.maxGroupSize === 1) {
        raw.Class = data?.classId;
      } else {
        raw.courseId = data?.courseId;
      }

      const requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: JSON.stringify(raw),
        redirect: "follow",
      };

      let url = data?.maxGroupSize === 1
        ? `${NEXT_PUBLIC_BASE_URL}/api/booking/attendance/${data?.bookingId}`
        : `${NEXT_PUBLIC_BASE_URL}/api/booking/markAttendance`;

      const response = await fetch(url, requestOptions);
      const result = await response.json();

      if (result.message) {
        Alert.alert("Attendance", result.message);
        navigation.navigate('Home');
      } else if (result.error) {
        Alert.alert("Attendance", result.error);
        navigation.navigate('Home');
      }
    } catch (error) {
      Alert.alert("Error", error.message);
      navigation.navigate('Home');
    } finally {
      setScanning(false); // Reset scanning state after processing
    }
  };

  const handleRequestPermission = async () => {
    setRequestingPermission(true);
    await requestPermission();
    setRequestingPermission(false);
  };

  // Helper to check if permission is blocked (not just denied)
  const isPermissionBlocked = status === 'denied';

  function safeJsonParse(str) {
    try {
      return [JSON.parse(str), null];
    } catch (e) {
      return [null, e];
    }
  }

  return (
    <View style={{ flex: 1 }}>
      {hasPermission && showScanner ? (
        <Camera
          style={StyleSheet.absoluteFill}
          device={device}
          isActive={true}
          codeScanner={codeScanner}
        />
      ) : !showScanner ? (
        <View style={{ flex: 1, backgroundColor: '#fff' }} />
      ) : (
        <View style={styles.centeredContainer}>
          {device == null ? (
            <>
              <Text style={styles.errorText}>No camera device found</Text>
              <Text style={styles.infoText}>
                This device does not have a back camera, or the camera is not available.
              </Text>
              <Text style={styles.infoText}>
                Please check your device hardware or try restarting the app.
              </Text>
            </>
          ) : requestingPermission ? (
            <>
              <ActivityIndicator size="large" color="#E31F26" style={{ marginBottom: 16 }} />
              <Text style={styles.infoText}>Requesting camera permission...</Text>
            </>
          ) : (
            <>
              <Text style={styles.errorText}>No camera permission</Text>
              <Text style={styles.infoText}>Camera access is required to scan QR codes.</Text>
              <Text style={styles.infoText}>
                If you are not seeing the permission prompt, please enable camera permission manually from your device settings.
              </Text>
              <View style={styles.buttonWrapper}>
                <Text style={styles.permissionButton} onPress={handleRequestPermission}>
                  Retry
                </Text>
              </View>
            </>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  centeredContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: 'red',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    fontFamily: 'Lato-Regular',
  },
  infoText: {
    color: '#555',
    fontSize: 14,
    marginTop: 8,
    marginBottom: 16,
    textAlign: 'center',
    fontFamily: 'Lato-Regular',
  },
  buttonWrapper: {
    marginTop: 8,
    alignItems: 'center',
  },
  permissionButton: {
    backgroundColor: '#E31F26',
    color: '#fff',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    fontSize: 16,
    fontFamily: 'Lato-Bold',
    overflow: 'hidden',
    textAlign: 'center',
  },
});

export default QrScanner;