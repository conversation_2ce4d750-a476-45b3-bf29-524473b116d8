import React, { useState, useEffect, useLayoutEffect } from 'react';
import {
  ScrollView,
  StyleSheet,
  View,
  ActivityIndicator,
  Text,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import Recommended from '../components/Courses/Recommended.jsx';
import CourseAmenities from '../components/Courses/Amenities.jsx';
import CourseDescription from '../components/Courses/Description.jsx';
import CourseCoach from '../components/Courses/CourseDetails/CourseCoach.jsx';
import CourseSideDetails from '../components/Courses/CourseDetails/CourseSideDetails.jsx';
import CourseImage from '../components/Courses/CourseDetails/CourseImage.jsx';
import { NEXT_PUBLIC_BASE_URL } from '@env';
import axios from 'axios';
import SearchLocationContainer from '../components/SearchLocationContainer/SearchLocationContainer.tsx';
import { useRoute } from '@react-navigation/native'
import Footer from '../components/footer.jsx';

export default function Courses({ route }) {
  const { courseId } = route?.params;
  const navigation = useNavigation();
  const [course, setCourse] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      // Check if courseId is provided
      if (!courseId) {
        setError('Course not found. Please try again.');
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);
      
      try {
        const response = await axios.get(`${NEXT_PUBLIC_BASE_URL}/api/course/${courseId}`);
        
        // Check if response has valid data
        if (!response.data || Object.keys(response.data).length === 0) {
          setError('Course not found. Please try again.');
        } else {
          setCourse(response.data);
        }
      } catch (error) {
        console.error('Error fetching course data:', error);
        
        // Handle different types of errors
        if (error.response?.status === 404) {
          setError('Course not found. Please try again.');
        } else if (error.response?.status >= 500) {
          setError('Server error. Please try again later.');
        } else if (error.code === 'NETWORK_ERROR') {
          setError('Network error. Please check your connection and try again.');
        } else {
          setError('Something went wrong. Please try again.');
        }
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [courseId]);

  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: course?.courseName ? course?.courseName : 'Loading...',
    });
  }, [course, navigation]);

  const handleGoBack = () => {
    navigation.goBack();
  };

  const handleRetry = () => {
    setError(null);
    setIsLoading(true);
    // Re-fetch the data
    const fetchData = async () => {
      try {
        const response = await axios.get(`${NEXT_PUBLIC_BASE_URL}/api/course/${courseId}`);
        if (!response.data || Object.keys(response.data).length === 0) {
          setError('Course not found. Please try again.');
        } else {
          setCourse(response.data);
        }
      } catch (error) {
        console.error('Error fetching course data:', error);
        if (error.response?.status === 404) {
          setError('Course not found. Please try again.');
        } else if (error.response?.status >= 500) {
          setError('Server error. Please try again later.');
        } else if (error.code === 'NETWORK_ERROR') {
          setError('Network error. Please check your connection and try again.');
        } else {
          setError('Something went wrong. Please try again.');
        }
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  };

  if (isLoading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
        <Text style={styles.loadingText}>Loading course details...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorTitle}>Oops!</Text>
        <Text style={styles.errorMessage}>{error}</Text>
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.retryButton} onPress={handleRetry}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.goBackButton} onPress={handleGoBack}>
            <Text style={styles.goBackButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
      <ScrollView>
        <SearchLocationContainer />
        <View style={styles.mainContainer}>
          <CourseImage course={course} />
          <CourseSideDetails course={course} />
          <CourseCoach course={course} coachData={course.coach_id} />
          <CourseDescription course={course} />
          {(course.amenitiesProvided || course.whatYouHaveToBring) && (
            <CourseAmenities course={course} />
          )}
          <Recommended courseId={courseId}/>
          <Footer />
        </View>
      </ScrollView>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    gap: 20,
    backgroundColor: 'white',
    fontFamily:"Lato-Regular"
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
    fontFamily: 'Lato-Regular',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    backgroundColor: 'white',
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
    fontFamily: 'Lato-Bold',
  },
  errorMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 24,
    fontFamily: 'Lato-Regular',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 15,
  },
  retryButton: {
    backgroundColor: '#E31F26',
    paddingHorizontal: 25,
    paddingVertical: 12,
    borderRadius: 6,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Lato-Bold',
  },
  goBackButton: {
    backgroundColor: 'transparent',
    paddingHorizontal: 25,
    paddingVertical: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#E31F26',
  },
  goBackButtonText: {
    color: '#E31F26',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Lato-Bold',
  },
});