import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { NEXT_PUBLIC_BASE_URL } from '@env';
import HTML from 'react-native-render-html';
const TermsOfService = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [service, setService] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch(`${NEXT_PUBLIC_BASE_URL}/api/cms/policy`);
        if (!response.ok) {
          throw new Error('Failed to fetch data');
        }
        const data = await response.json();
        setService(data);
      } catch (error) {
        console.error('Error fetching data: 26');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);
  const {width} = Dimensions.get('window');
  if (isLoading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  return (
    <ScrollView contentContainerStyle={styles.container}>
      {service && (
        <View style={styles.policyContainer}>
           <HTML tagsStyles={{
          p: { margin: 0, padding: 0, color: "#000", fontFamily:"Lato-Regular"},
          h2: { margin: 0, padding: 0, color: "#000", fontSize: 15 },
          br: { display: 'none' },
          li: { margin: 0, paddingHorizontal: "2%", color: "#000", alignItems: 'center' },
          ul: { marginHorizontal: "5%", alignItems: 'center', padding: 0 },
        }} source={{ html: service[0].TermsAndConditions }} />
        </View>
      )}
    </ScrollView>
  );
};

export default TermsOfService;

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: "10%",
    paddingVertical: "4%",
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  faqItem: {
    marginBottom: 20,
  },
  question: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'black',
  },
  answer: {
    fontSize: 14,
    color: 'grey',
  },
});
