import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Linking,
  Dimensions,
} from 'react-native';
import React from 'react';
import { useNavigation } from '@react-navigation/native';
import HTML from 'react-native-render-html';

const RegisterAsCoach = (data, blockData) => {
  const navigation = useNavigation();
  const screenWidth = Dimensions.get('window').width;
  const imageWidth = screenWidth - 55;

  const imageUrl = data.data[0].image;
  console.log("Image URL---------------------------->", imageUrl);
  const handleRegisterNow = () => {
    Linking.openURL('https://coach.khelcoach.com');
  };

  const handleReadMore = () => {
    navigation.navigate('ReadMore');
  };
  console.log("--imag url", imageUrl)
  return (
    <View style={styles.container}>
      <View style={styles.textContainer}>
        <Text style={styles.title}>{blockData?.title}</Text>
        <HTML
          tagsStyles={{
            p: { margin: 0, padding: 0, color: '#000', fontSize: 15 },
            h2: { margin: 0, padding: 0, color: '#000', fontSize: 15 },
            br: { display: 'none' },
            li: {
              margin: 0,
              paddingHorizontal: '2%',
              color: '#000',
              alignItems: 'center',
            },
            ul: { marginHorizontal: '5%', alignItems: 'center', padding: 0 },
          }}
          source={{ html: data.data[0].description }}
        />
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.button} onPress={handleRegisterNow}>
            <Text style={styles.buttonText}>Register Now</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.readMoreContainer}
            onPress={handleReadMore}>
            <Text style={styles.readMoreText}>Read More</Text>
          </TouchableOpacity>
        </View>
        {imageUrl && <View style={styles.instructorImg}>
          <Image
            style={{ width: imageWidth, height: imageWidth, borderRadius: 20 }} // Now dynamically calculated
            source={{ uri: imageUrl }}
            resizeMode="contain"
          />
        </View>}

      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    maxWidth: '100%',
    paddingHorizontal: '5%',
    alignItems: 'center',
  },
  textContainer: {
    width: '100%',
    // marginBottom: 16,
    paddingHorizontal: '2%',
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 16,
    fontFamily: 'Lato-Bold',
    color: '#000',
  },
  description: {
    fontSize: 16,
    marginBottom: 16,
    fontFamily: 'Lato-Regular',
    color: '#000',
    lineHeight: 22,
  },
  listContainer: {
    width: '90%',
    margin: 'auto',
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  bullet: {
    fontSize: 20,
    marginRight: 5,
  },
  listItemText: {
    fontSize: 15,
    fontFamily: 'Lato-Regular',
    color: '#000',
    lineHeight: 22,
  },
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 20,
  },
  button: {
    backgroundColor: '#E31F26',
    paddingVertical: '3%',
    paddingHorizontal: 10,
    borderRadius: 5,
  },
  buttonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Lato-Bold',
  },
  readMoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 20,
  },
  readMoreText: {
    textDecorationLine: 'underline',
    fontSize: 14,
    marginRight: 1,
    fontFamily: 'Lato-Bold',
    color: '#000',
    textDecorationColor: '#000',
    fontWeight: '600',
  },
  instructorImg: {
    width: '100%',
    marginTop: '8%',
    // marginHorizontal:"2%"
  },
});

export default RegisterAsCoach;