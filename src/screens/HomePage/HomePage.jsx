import {
  Safe<PERSON>reaV<PERSON><PERSON>,
  <PERSON>rollView,
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Animated,
  FlatList,
  Dimensions,
  Image,
  TouchableOpacity,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import Header from '../../components/header';
import TopCategories from '../../components/homepage/TopCategories.jsx';
import TopCourses from '../../components/homepage/TopCourses.jsx';
import RecommendedCoaches from '../../components/homepage/RecommendedCoaches.jsx';
import WhyPeopleLoveKhelSports from '../../components/homepage/WhyPeopleLoveKhelSports.jsx';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useAuth } from '../../Context/AuthContext.jsx';
import Footer from '../../components/footer.jsx';
import { NEXT_PUBLIC_BASE_URL } from '@env';
import axios from 'axios';
import RegisterAsCoach from '../RegisterAsCoach.jsx';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SearchLocationContainer from '../../components/SearchLocationContainer/SearchLocationContainer.tsx';
import DeleteAccountModal from '../../components/DeleteAccountModal.jsx';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { styles } from './homePageStyling.js';
import TopAcademies from '../../components/TopAcademies/TopAcademies.jsx';

const HomePage = () => {
  const navigation = useNavigation();
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [drawerAnimation] = useState(new Animated.Value(0));
  const [sports, setSports] = useState([]);
  const [showSportOptions, setShowSportOptions] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const { isLoggedIn, logout, playerData, setUser } = useAuth();
  const screenHeight = Dimensions.get("window").height;
  const screenWidth = Dimensions.get("window").width;
  const insets = useSafeAreaInsets();

  const [CMSData, setCMSData] = useState([]);
  const [componentsToRender, setComponentsToRender] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [topAcademies, setTopAcademies] = useState([]);
  const [loadingAcademies, setLoadingAcademies] = useState(true);

  
  useEffect(() => {
    const fetchCMSBlocks = async () => {
      try {
        const response = await axios.get(`${NEXT_PUBLIC_BASE_URL}/api/cms/blocks`);
        console.log("Block Data ----------------------->\n\n\n\n\n", response.data);
        setCMSData(response.data);
        setIsLoading(false);
      } catch (error) {
        console.error(error);
      }
    };
    fetchCMSBlocks();
  }, [])
  useEffect(() => {
    if (CMSData.length > 0) {
      const sortedCMSData = CMSData.sort((a, b) => a.blockData.position - b.blockData.position);
      const CMS_COMPONENT_MAP = {
        Category: TopCategories,
        Course: TopCourses,
        Testimonials: WhyPeopleLoveKhelSports,
        Registration: RegisterAsCoach,
        Coach: RecommendedCoaches,
        Academy: TopAcademies,
      };
      const components = sortedCMSData.map((data, index) => {
        const Component = CMS_COMPONENT_MAP[data.blockData.identity];
        if (!Component) return null;
        if (data.blockData.identity === 'Academy') {
          return (
            <Component
              key={`${data.id}-${index}`}
              academies={data.referencedData}
              loading={isLoading}
              blockData={data.blockData}
            />
          );
        }
        return (
          <Component
            key={`${data.id}-${index}`}
            data={data.referencedData}
            blockData={data.blockData}
          />
        );
      });
      setComponentsToRender(components);
    }
  }, [CMSData, isLoading]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios.get(
          `${NEXT_PUBLIC_BASE_URL}/api/category`
        );
        setSports(response.data.data);
        console.log('Fetched sports:', response.data.data);
      } catch (error) {
        console.error("Error fetching data: 42", error);
        setSports([]);
      }
    };
    fetchData();
  }, []);

  const openDrawer = () => {
    setDrawerVisible(true);
    Animated.timing(drawerAnimation, {
      toValue: 1,
      duration: 400,
      useNativeDriver: false,
    }).start();
  };
  const closeDrawer = () => {
    Animated.timing(drawerAnimation, {
      toValue: 0,
      duration: 400,
      useNativeDriver: false,
    }).start(() => setDrawerVisible(false));
    setShowSportOptions(false);
  };
  const handleSportChange = (sport) => {
    closeDrawer();
    if (sport) {
      navigation.navigate("Collection", { selectedOption: sport });
    }
  };
  const handleLogout = async () => {
    try {
      logout();
      closeDrawer();
      navigation.navigate("SignIn");
    } catch (error) {
      console.error("Error logging out: 97");
    }
  };
  const handleDeleteAccount = () => setShowDeleteModal(true);
  const handleDeleteModalClose = () => setShowDeleteModal(false);
  const handleDeleteSuccess = () => {
    setShowDeleteModal(false);
    closeDrawer();
    navigation.navigate("SignIn");
  };
  const handleRedirectToSignIn = () => { navigation.navigate("SignIn"); closeDrawer(); };
  const handleRedirectToSignUp = () => { navigation.navigate("SignUp"); closeDrawer(); };
  const handleRedirectToContact = () => { navigation.navigate("Contact"); closeDrawer(); };
  const handleUserAccountRedirect = () => { closeDrawer(); navigation.navigate("PlayerProfile"); };

  const drawerStyles = [
    styles.drawer,
    {
      transform: [
        {
          translateX: drawerAnimation.interpolate({
            inputRange: [0, 1],
            outputRange: [-screenWidth, 0],
          }),
        },
      ],
      height: screenHeight,
    },
  ];

  if (isLoading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }
  return (
    <SafeAreaView style={{ flex: 1 }}>
      <Header onMenuPress={openDrawer} />
      {drawerVisible && (
        <SafeAreaView style={[styles.drawerSafeArea, { zIndex: 10 }]}> 
          <Animated.View style={drawerStyles}>
            <ScrollView scrollEnabled={true}>
              <View style={[styles.drawerContent, { paddingTop: insets.top }]}>
                <View style={styles.drawerHeader}>
                  <Image
                    source={require("../../assets/MainKhelCoach.png")}
                    style={styles.drawerLogo}
                  />
                  <TouchableOpacity onPress={closeDrawer} style={styles.closeButton}>
                    <Text style={styles.closeButtonText}>×</Text>
                  </TouchableOpacity>
                </View>
                <View style={styles.drawerSeparator} />
                {!isLoggedIn && (
                  <>
                    <TouchableOpacity onPress={handleRedirectToSignIn}>
                      <Text style={styles.drawerItem}>Sign In</Text>
                    </TouchableOpacity>
                    <View style={styles.drawerSeparator} />
                    <TouchableOpacity onPress={handleRedirectToSignUp}>
                      <Text style={styles.drawerItem}>Sign Up</Text>
                    </TouchableOpacity>
                    <View style={styles.drawerSeparator} />
                  </>
                )}
                <TouchableOpacity
                  onPress={() => setShowSportOptions(!showSportOptions)}
                  style={styles.drawerSeparator}
                >
                  <Text style={[styles.selectSportButtonText]}>Sport</Text>
                </TouchableOpacity>
                {showSportOptions && (
                  <View style={[styles.sportOptionsContainer]}>
                    {Array.isArray(sports) && sports.length > 0 ? (
                      <FlatList
                        data={sports}
                        keyExtractor={item => item?._id || String(item)}
                        renderItem={({ item: sport }) => (
                          <TouchableOpacity
                            onPress={() => handleSportChange(sport.name)}
                            style={styles.sportOption}
                          >
                            <View style={styles.circularImage}>
                              {sport.image ? (
                                <Image source={{ uri: sport.image }} style={styles.image} />
                              ) : null}
                              <Text style={styles.sportOptionText}>{sport.name}</Text>
                            </View>
                          </TouchableOpacity>
                        )}
                        contentContainerStyle={styles.scrollView}
                        style={{ maxHeight: 600 }}
                        showsVerticalScrollIndicator={true}
                        nestedScrollEnabled={true}
                      />
                    ) : (
                      <Text style={{ color: '#000', padding: 20 }}>No sports available.</Text>
                    )}
                  </View>
                )}
                {isLoggedIn && (
                  <>
                    <View style={styles.drawerSeparator} />
                    <TouchableOpacity onPress={handleUserAccountRedirect}>
                      <Text style={styles.drawerItem}>{playerData.firstName} {playerData.lastName}</Text>
                    </TouchableOpacity>
                    <View style={styles.drawerSeparator} />
                    <TouchableOpacity onPress={handleLogout}>
                      <Text style={styles.drawerItem}>Logout</Text>
                    </TouchableOpacity>
                    <View style={styles.drawerSeparator} />
                    <TouchableOpacity onPress={handleDeleteAccount}>
                      <Text style={[styles.drawerItem, { color: "red" }]}>Forget Me</Text>
                    </TouchableOpacity>
                  </>
                )}
                <View style={styles.drawerSeparator} />
                <TouchableOpacity onPress={handleRedirectToContact} style={styles.drawerSeparator}>
                  <Text style={[styles.selectSportButtonText]}>Contact Us</Text>
                </TouchableOpacity>
              </View>
            </ScrollView>
            <DeleteAccountModal
              visible={showDeleteModal}
              onClose={handleDeleteModalClose}
              onSuccess={handleDeleteSuccess}
            />
          </Animated.View>
        </SafeAreaView>
      )}
      <ScrollView scrollEnabled={!drawerVisible} >
        <SearchLocationContainer/>
        <View style={styles.homeBackground}>
          {componentsToRender}
          <Footer />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default HomePage;
