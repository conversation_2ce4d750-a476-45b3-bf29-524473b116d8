import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
    homeBackground:{
      backgroundColor:"#fff",
      gap: 5,
    },
    loaderContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#fff',
    },
    drawer: {
      position: "absolute",
      top: 0,
      bottom: 0,
      left: 0,
      width: "100%",
      backgroundColor: "#fff",
      zIndex: 4,
      elevation: 16,
    },
    drawerContent: {
      paddingHorizontal: 10,
      justifyContent: "center",
      alignItems: "flex-start",
      marginLeft: "2%"
    },
    closeButton: {},
    closeButtonText: {
      fontSize: 40,
      color: "#000",
    },
    selectSportButton: {
      backgroundColor: "#fff",
      borderRadius: 5,
    },
    selectSportButtonText: {
      color: "#000",
      fontSize: 16,
      borderBottomColor: "#000",
      fontFamily: "Lato-Bold"
    },
    sportOptionsContainer: {
      marginTop: 10,
      maxHeight: 550
    },
    sportOption: {
      paddingVertical: 10,
      paddingHorizontal: 20,
    },
    circularImage: {
      alignItems: "center",
      display: "flex",
      flexDirection: "row",
    },
    image: {
      width: 40,
      height: 40,
      borderRadius: 25,
      marginRight: "10%"
    },
    sportOptionText: {
      color: "#000",
      fontSize: 16,
      fontWeight: "500",
      fontFamily: "Lato-Bold"
    },
    drawerItem: {
      marginBottom: 10,
      fontSize: 16,
      color: "#000",
      fontFamily: "Lato-Bold"
    },
    drawerSeparator: {
      borderBottomColor: "#fff",
      marginBottom: 10,
    },
    drawerLogo: {
      width: 100,
      height: 50,
      resizeMode: "contain",
      marginVertical: 5,
    },
    drawerHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      width: "100%",
    },
    scrollView: {
      paddingBottom: 20,
      paddingHorizontal: 10,
    },
    drawerSafeArea: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      width: '100%',
      height: '100%',
      zIndex: 10,
      backgroundColor: 'transparent',
    },
  });