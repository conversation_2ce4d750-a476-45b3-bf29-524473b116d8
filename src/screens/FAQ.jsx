import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacity,
  Image,
} from 'react-native';
import { NEXT_PUBLIC_BASE_URL } from '@env';

const FAQ = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [faq, setFaq] = useState([]);
  const [expandedIndex, setExpandedIndex] = useState(null); // State to track expanded FAQ item

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch(`${NEXT_PUBLIC_BASE_URL}/api/cms/faq`);
        if (!response.ok) {
          throw new Error('Failed to fetch data');
        }
        const data = await response.json();
        setFaq(data);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const toggleExpand = (index) => {
    setExpandedIndex(expandedIndex === index ? null : index);
  };

  if (isLoading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  return (
    <ScrollView contentContainerStyle={styles.container}>
      {faq.map((item, index) => (
        <View key={index} style={styles.faqItem}>
          <TouchableOpacity onPress={() => toggleExpand(index)} style={styles.touchableOpacity}>
            <View style={styles.questionContainer}>
              <Text style={styles.question}>{`Que: ${item.question}`}</Text>
              <Image
                source={expandedIndex === index ? require('../assets/minus.png') : require('../assets/plus.png')}
                style={styles.icon}
              />
            </View>
          </TouchableOpacity>
          {expandedIndex === index && (
            <Text style={styles.answer}>{`Ans: ${item.answer}`}</Text>
          )}
        </View>
      ))}
    </ScrollView>
  );
};

export default FAQ;

const styles = StyleSheet.create({
  container: {
    padding: 20,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  faqItem: {
    marginBottom: 20,
  },
  touchableOpacity: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  question: {
    flex: 1,
    fontSize: 16,
    fontWeight: 'bold',
    color: 'black',
    fontFamily:"Lato-Regular"
  },
  questionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    fontFamily:"Lato-Regular"
  },
  icon: {
    width: 18,
    height: 18,
  },
  answer: {
    fontSize: 14,
    color: 'grey',
    marginTop: 5,
  },
});
