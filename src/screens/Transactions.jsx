import { StyleSheet, Text, View, ScrollView } from 'react-native';
import React, { useEffect, useState } from 'react';
import { DataTable } from 'react-native-paper';
import { useAuth } from '../Context/AuthContext';
import { NEXT_WALLET_URL } from '@env';

const Transactions = () => {
  const { playerData } = useAuth();
  const [allTransaction, setAllTransactions] = useState([]);

  useEffect(() => {
    const fetchTransactionData = async () => {
      try {
        const requestOptions = { method: "GET", redirect: "follow" };
        const response = await fetch(`${NEXT_WALLET_URL}/api/transactions?playerId=6619128c5b491c4abb2cde90`, requestOptions);
        const result = await response.json();
        setAllTransactions(result.data);
      } catch (error) {
        console.error("Error fetching wallet data:", error);
      }
    };

    if (playerData) {
      fetchTransactionData();
    }
  }, [playerData]);

  return (
    <ScrollView horizontal style={styles.horizontalScroll}>
      <View>
        <DataTable>
          <DataTable.Header>
            <DataTable.Title style={[styles.cell, styles.smallColumn, styles.headerText]}>Sno.</DataTable.Title>
            <DataTable.Title style={[styles.cell, styles.largeColumn, styles.headerText]}>Course Name</DataTable.Title>
            <DataTable.Title style={[styles.cell, styles.mediumColumn, styles.headerText]}>Type</DataTable.Title>
            <DataTable.Title style={[styles.cell, styles.mediumColumn, styles.headerText]}>Amount</DataTable.Title>
          </DataTable.Header>
          <ScrollView>
            {allTransaction.map((transaction, index) => (
              <DataTable.Row key={transaction._id}>
                <DataTable.Cell style={[styles.cell, styles.smallColumn, styles.cellText]}>{index + 1}</DataTable.Cell>
                <DataTable.Cell style={[styles.cell, styles.largeColumn, styles.cellText]}>
                  <Text style={styles.wrapText}>{transaction.courseId.courseName}</Text>
                </DataTable.Cell>
                <DataTable.Cell style={[styles.cell, styles.mediumColumn, styles.cellText]}>{transaction.type}</DataTable.Cell>
                <DataTable.Cell style={[styles.cell, styles.mediumColumn, styles.cellText]}>{transaction.amount}</DataTable.Cell>
              </DataTable.Row>
            ))}
          </ScrollView>
        </DataTable>
      </View>
    </ScrollView>
  );
};

export default Transactions;

const styles = StyleSheet.create({
  horizontalScroll: {
    flex: 1,
  },
  smallColumn: {
    minWidth: 50,
  },
  mediumColumn: {
    minWidth: 100,
  },
  largeColumn: {
    minWidth: 150,
  },
  wrapText: {
    flex: 1,
    flexWrap: 'wrap',
  },
  cell: {
    flex: 1,
    padding: "1%",
    justifyContent: 'center',
    textAlign: 'left',
  },
  headerText: {
    color: '#000', 
    fontWeight: 'bold',
  },
  cellText: {
    color: 'black',
  },
  container: {
    flex: 1,
    padding: 20,
  },
  header: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  table: {
    flexDirection: 'column',
  },
  row: {
    borderBottomWidth: 1,
    borderColor: '#000',
  },
  headerCell: {
    fontWeight: 'bold',
  },
});
