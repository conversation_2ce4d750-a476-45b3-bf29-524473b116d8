import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Modal,
  StyleSheet,
} from 'react-native';

const getTimeDate = hourMinute => {
  const [hour, minute] = hourMinute.split(':').map(Number);
  const now = new Date();
  return new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate(),
    hour,
    minute,
  );
};

// Helper to check if a slot can fit the minimum duration (30min) within the window and without overlap
const canFitMinimumDuration = (slotTime, endTime, bookedSlots) => {
  const slotStart = getTimeDate(slotTime);
  const slotEnd = new Date(slotStart.getTime() + 30 * 60000); // 30min duration
  const windowEnd = getTimeDate(endTime);
  // Must not exceed the window
  if (slotEnd > windowEnd) return false;
  // Must not overlap with any booking
  return !bookedSlots.some(slot => {
    const bookedStart = getTimeDate(slot.start);
    const bookedEnd = getTimeDate(slot.end);
    return slotStart < bookedEnd && slotEnd > bookedStart;
  });
};

const generateTimeSlots = (startTime, endTime, interval, bookedSlots, selectedDate) => {
  const start = getTimeDate(startTime);
  const end = getTimeDate(endTime);
  const now = new Date();
  const isToday =
    selectedDate &&
    selectedDate.getFullYear() === now.getFullYear() &&
    selectedDate.getMonth() === now.getMonth() &&
    selectedDate.getDate() === now.getDate();
  const slots = [];
  let currentTime = new Date(start);
  while (true) {
    const slotTimeStr = `${currentTime.getHours().toString().padStart(2, '0')}:${currentTime.getMinutes().toString().padStart(2, '0')}`;
    const slotStart = getTimeDate(slotTimeStr);
    const slotEnd = new Date(slotStart.getTime() + 30 * 60000); // 30min duration
    if (slotEnd > end) break; // Don't show slots that can't fit 30min
    const isPast = isToday && currentTime < now;
    const isBooked = isPast || bookedSlots.some(slot => {
      const bookedStart = getTimeDate(slot.start);
      const bookedEnd = getTimeDate(slot.end);
      return slotStart < bookedEnd && slotEnd > bookedStart;
    });
    slots.push({ time: new Date(currentTime), isBooked });
    currentTime.setMinutes(currentTime.getMinutes() + interval);
  }
  return slots;
};




// const generateTimeSlots = (
//   startTime,
//   endTime,
//   interval = 10,
//   bookedSlots = [],
// ) => {
//   const start = getTimeDate(startTime);
//   const end = getTimeDate(endTime);
//   const slots = [];
//   let currentTime = new Date(start);

//   while (currentTime <= end) {
//     const isBooked = bookedSlots.some(slot => {
//       const bookedStart = getTimeDate(slot.start);
//       const bookedEnd = getTimeDate(slot.end);
//       return currentTime >= bookedStart && currentTime < bookedEnd;
//     });

//     slots.push({time: new Date(currentTime), isBooked});
//     currentTime.setMinutes(currentTime.getMinutes() + interval);
//   }

//   return slots;
// };

let timeSlotSelected;
const formatTime12Hour = date => {
  let hours = date.getHours();
  const minutes = date.getMinutes();
  const ampm = hours >= 12 ? 'PM' : 'AM';
  hours = hours % 12;
  hours = hours || 12;
  const minutesFormatted = minutes < 10 ? `0${minutes}` : minutes;
  timeSlotSelected = `${hours}:${minutesFormatted} ${ampm}`;
  return `${hours}:${minutesFormatted} ${ampm}`;
};

const formatDateToMatch = (dateString) => {
  if (!dateString) return '';
  if (typeof dateString === 'string') {
    if (dateString.includes('-')) {
      const [day, month, year] = dateString.split('-');
      return `${year}-${month}-${day}`;
    }
    // handle other string formats if needed
    return dateString;
  }
  if (dateString instanceof Date) {
    // Convert Date object to 'YYYY-MM-DD'
    const year = dateString.getFullYear();
    const month = (dateString.getMonth() + 1).toString().padStart(2, '0');
    const day = dateString.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
  return '';
};

const TimePicker = ({
  startTime,
  endTime,
  setSelectedTime,
  selectedTime,
  handleSelectTime,
  timePickerModal,
  handleCloseTimePicker,
  bookedSlots,
  date,
  selectedDateSlot
}) => {
  const [selectedTimeHere, setSelectedTimeHere] = useState(timeSlotSelected);
  const [localSelectedTime, setLocalSelectedTime] = useState(null);

  useEffect(() => {
    setSelectedTimeHere(timeSlotSelected);
  }, [timeSlotSelected]);

  // Set local selection to show previously selected time when modal opens
  useEffect(() => {
    if (timePickerModal && selectedTime) {
      setLocalSelectedTime(selectedTime);
      console.log('Setting local selected time:', selectedTime);
    }
  }, [timePickerModal, selectedTime]);

  const timeSlots = generateTimeSlots(startTime, endTime, 30, bookedSlots, selectedDateSlot);




  const handleTimeSelect = time => {
    setLocalSelectedTime(time);
    setSelectedTime(time);
    formatTime12Hour(time);
    handleSelectTime(time);
    // Auto-close modal when user selects a time
    handleCloseTimePicker();
  };

  return (
    <Modal visible={timePickerModal} transparent animationType="slide">
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          <View>
            <Text style={{color: '#000', fontSize: 16, fontWeight: '500'}}>
              Choose Time
            </Text>
          </View>
          <ScrollView contentContainerStyle={styles.scrollView}>
            {timeSlots.map((slot, index) => (
              <TouchableOpacity
                key={index}
                style={styles.timeSlot(
                  localSelectedTime &&
                    localSelectedTime.getTime() === slot.time.getTime(),
                  slot.isBooked,
                )}
                onPress={() => !slot.isBooked && handleTimeSelect(slot.time)}
                disabled={slot.isBooked}>
                <Text
                  style={styles.timeText(
                    localSelectedTime &&
                      localSelectedTime.getTime() === slot.time.getTime(),
                    slot.isBooked,
                  )}>
                  {formatTime12Hour(slot.time)}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={handleCloseTimePicker}>
            <Text style={styles.closeButtonText}>Close</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    marginTop: '1%',
    position: 'relative',
    top: '29%',
    width: '80%',
    alignSelf: 'center',
  },
  modalView: {
    backgroundColor: 'white',
    borderRadius: 20,
    paddingVertical: '5%',
    paddingHorizontal: '10%',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 1,
    maxHeight: 350,
  },
  scrollView: {
    flexDirection: 'column',
    alignItems: 'center',
  },
  timeSlot: (isSelected, isBooked) => ({
    width: 130,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: isBooked ? '#d3d3d3' : isSelected ? '#007AFF' : '#FFF',
    borderRadius: 8,
    marginVertical: 5,
    borderWidth: 1,
    borderColor: isBooked ? '#d3d3d3' : isSelected ? '#007AFF' : '#ccc',
  }),
  timeText: (isSelected, isBooked) => ({
    color: isBooked ? '#a9a9a9' : isSelected ? '#FFF' : '#000',
    fontSize: 16,
  }),
  closeButton: {
    backgroundColor: '#007AFF',
    paddingVertical: '3%',
    paddingHorizontal: '6%',
    alignItems: 'center',
    borderRadius: 10,
    marginTop: '5%',
  },
  closeButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default TimePicker;