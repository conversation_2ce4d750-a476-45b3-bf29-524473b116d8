import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import {NEXT_PUBLIC_BASE_URL} from '@env';
import RenderHtml from 'react-native-render-html';
import HTML from 'react-native-render-html';
const PrivacyPolicy = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [policy, setPolicy] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch(`${NEXT_PUBLIC_BASE_URL}/api/cms/policy`);
        if (!response.ok) {
          throw new Error('Failed to fetch data');
        }
        const data = await response.json();
        setPolicy(data[0]); 
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const {width} = Dimensions.get('window');

  if (isLoading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Text style={styles.title}>Privacy Policy</Text>
      {policy && (
        <View style={styles.policyContainer}>
          <View style={styles.policyContainer}>
           <HTML tagsStyles={{
          p: { margin: 0, padding: 0, color: "#000", fontFamily:"Lato-Regular"},
          h2: { margin: 0, padding: 0, color: "#000", fontSize: 15 },
          br: { display: 'none' },
          li: { margin: 0, paddingHorizontal: "2%", color: "#000", alignItems: 'center' },
          ul: { marginHorizontal: "5%", alignItems: 'center', padding: 0 },
        }}  source={{html: policy.privacyPolicy}} />
        </View>
        </View>
      )}
    </ScrollView>
  );
};

export default PrivacyPolicy;

const styles = StyleSheet.create({
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    paddingHorizontal: "10%",
    paddingVertical: "5%",
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#000',
  },
  policyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bulletPoint: {
    marginTop: 5,
    fontSize: 16,
  },
});
