import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  Image,
  Button,
  Dimensions,
  ActivityIndicator,
  Linking,
  FlatList,
  SafeAreaView,
  TextInput
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Divider } from 'react-native-paper';
import axios from "axios";
import { NEXT_PUBLIC_BASE_URL } from "@env";
import { useRoute } from "@react-navigation/native";
import Filter from "../components/Filters/Filter";
import { useNavigation } from "@react-navigation/native";
import { useAuth } from "../Context/AuthContext";
import HTML from "react-native-render-html";
import { Rating, AirbnbRating } from 'react-native-ratings';
const placeholder = require("../assets/placeholder.png");
const location = require("../assets/locatiothree.png");
const redirect = require("../assets/redirect.png");
const starFilled = require("../assets/starfilled.png");
const starEmpty = require("../assets/starempty.png");

const emailRegExp = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
export default function ListingCards({ filterUrl, firstParam, searchParam }) {

  const route = useRoute();
  const [selectedOption, setSelectedOption] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [filterVisible, setFilterVisible] = useState(false);
  const [courses, setCourses] = useState([]);
  const [emailError, setEmailError] = useState("");
  const [notifySuccess, setNotifySuccess] = useState(false);

  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const navigation = useNavigation();

  const [filterChange, setFilterChange] = useState(filterUrl);
  const [filterBoolean, setFilterBoolean] = useState(false)
  const [pageTrue, setPageTrue] = useState(false);
  const { isLoggedIn, logout, playerData, fetchUserDetails } = useAuth();
  const [email, setEmail] = useState(playerData?.email||"");

  let pageNumberState = 1;
  let pageBoolean = false;
  const handleNotifyMe = async () => {
    if (!emailRegExp.test(email)) {
      setEmailError("Please enter a valid email address.");
      return;
    }
    setEmailError("");
    let message = "";
    if (firstParam) message += `Category: ${firstParam} `;
    if (searchParam) message += `Search: ${searchParam} `;
    if (filterUrl) message += `Filter: ${filterUrl}`;
    try {
      const response = await axios.post(`${process.env.NEXT_PUBLIC_BASE_URL}/api/courseNotify`, {
        email,
        message: message.trim(),
      });
      if (response.status === 200) {
        setNotifySuccess(true);
      }
    } catch (error) {
      console.error("Error sending notification request:", error);
    }
  };
  const loadCourses = async () => {
    setNotifySuccess(false)
    let url = `${NEXT_PUBLIC_BASE_URL}/api/course/filter?page=${page}`;

    let categoryParam = "";
    if (firstParam && !filterUrl && !searchParam) {
      categoryParam = `?category=${firstParam}`;
      AsyncStorage.setItem('search', firstParam);
    }
    else if (searchParam && !filterUrl && !firstParam) {
      categoryParam = `${searchParam}`;
      AsyncStorage.setItem('search', filterUrl);
    } else if (filterUrl) {
      categoryParam = `&${filterUrl}`;
      AsyncStorage.setItem('search', filterUrl);
    }
    if (categoryParam) {
      url += categoryParam;
      if (pageBoolean) return;
      if (pageNumberState === 1) pageBoolean = true;
      try {
        const response = await axios.get(url);
        // const newData = response.data;
        if (response.data.length === 0) {
          setHasMore(false);
        }
        if (filterBoolean) setCourses(response.data);
        else setCourses((prevCourses) => [...prevCourses, ...response.data]);
        setFilterBoolean(false);
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setIsLoading(false);
        setIsRefreshing(false);
      }
    }
  };

  useEffect(() => {
    if (filterUrl != filterChange) {
      setPage(1);
      pageNumberState = 1;
      pageBoolean = true;
      setFilterBoolean(true);
      setFilterChange(filterUrl);
      setHasMore(true);
    }
    loadCourses();

  }, [firstParam, searchParam, filterUrl, page, filterChange, hasMore]);


  const renderFooter = () => {
    if (!isLoading) return null;
    return <ActivityIndicator size="large" color="#0000ff" />;
  };

  // const handleRefresh = () => {
  //   setIsRefreshing(true);
  //   setPage(1);
  //   setCourses([]);
  //   setHasMore(true);
  // };

  const handleLoadMore = () => {
    if (hasMore && !isLoading) {
      setPage(prevPage => prevPage + 1);
    }
  };

  const handleMapNavigation = (latitude, longitude) => {
    const url = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
    Linking.openURL(url);
  };
  const renderDescription = (description) => {
    const maxChars = 150; // Maximum number of characters to display
    let truncatedDescription = description.replace(/<[^>]+>/g, ''); // Remove HTML tags
    truncatedDescription = truncatedDescription.substring(0, maxChars); // Limit characters
    if (description.length > maxChars) {
      truncatedDescription += "...";
    }
    return <Text style={styles.description}>{truncatedDescription}</Text>;
  };


  if (isLoading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  const CourseCard = ({ course, index }) => {
    return (
      <TouchableOpacity
        key={index}
        style={styles.container}
        onPress={() =>
          navigation.navigate("Courses", {
            courseId: course._id,
            filter: route.params.selectedOption,
            search: route.params.searchQuery,
          })
        }
      >
        <View style={styles.sectionOne}>
          <Image
            source={
              course.images.length > 0
                ? { uri: course.images[0].url }
                : placeholder
            }
            style={styles.image}
          />
          <View style={styles.sectionTwo}>
            <View style={styles.rowReview}>
              <View style={styles.reviews}>
                <Rating
                  type='star'
                  ratingCount={5}
                  startingValue={course?.ratings ? course?.ratings?.stars : 0}
                  imageSize={20}
                  readonly={true}
                />
              </View>
              <Text style={styles.labelReview}> {`${course?.ratings ? course?.ratings?.noOfRatings : 0
                } Reviews`}</Text>
            </View>

            {course?.maxGroupSize ? (
              <View style={{ display: "flex", flexDirection: "row" }}>
                <Text style={styles.labelReview}>Group Size:</Text>
                <Text style={styles.facility}>{course?.maxGroupSize}</Text>
              </View>
            ) : null}
            {course.fees.feesCourse === null ? (
              <View style={styles.buttonContainer}  >
                <Button title="Book Now" key={index} color="#fff" onPress={() =>
                  navigation.navigate("Courses", {
                    courseId: course._id,
                    filter: route.params.selectedOption,
                    search: route.params.searchQuery,
                  })
                } />
              </View>
            ) : (
              <View
                style={{
                  backgroundColor: "#E31F26",
                  padding: 8,
                  textAlign: "center",
                  alignItems: "center",
                  borderRadius: 3,
                }}
              >
                <Text
                  style={{ color: "#fff", fontSize: 16, fontWeight: 500 }}
                >
                  {`₹ ${Math.round(course?.fees?.feesCourse)}`}
                </Text>
              </View>
            )}
            <View style={styles.rowView}>
              <Text style={styles.labelView}>View More</Text>
              <Image source={redirect} style={{ width: 15, height: 15, fontWeight: "bold" }} />
            </View>
          </View>
        </View>
        {course.maxGroupSize === 1 &&
          course.classType.toLowerCase() === "class" && (
            <View style={styles.classTypeContainer}>
              <Text style={styles.classType}>Individual Session</Text>
            </View>
          )}
        <View style={styles.horizontalLine} />

        <View style={styles.cardDetails}>
          <View style={styles.cardDetailsOne}>
            <View style={styles.detailsContainer}>
              <Text style={styles.title}>
                {course.courseName}{" "}
                <Text
                  style={{ color: "grey" }}
                >{`(${course.classType})`}</Text>
              </Text>
              <View style={styles.row}>
                <Text style={styles.label}>Coach Name:</Text>
                <View style={styles.valueContainer}>
                  <Text style={styles.value}>{course.coachName}</Text>
                </View>
              </View>
              <View style={styles.row}>
                <Text style={styles.label}>Description:</Text>
                <View style={styles.valueContainer}>
                  <Text style={styles.value}>
                    {/* {course?.description} */}
                    {renderDescription(course?.description)}
                  </Text>
                </View>
              </View>
              <View style={styles.proficiencyRow}>
                <Text style={styles.label}>Proficiency:</Text>
                <View
                  style={[
                    styles.proficiencyLabel,
                  ]}
                >
                  {course.proficiency.map((proficiency, index) => (
                    <Text
                      key={index}
                      style={[styles.proficiency]}
                    >
                      {proficiency}
                    </Text>
                  ))}
                </View>
              </View>
              <View style={styles.row}>
                <Text style={styles.label}>Facility:</Text>
                <View style={styles.valueContainer}>
                  <Text style={styles.facility}>
                    {course?.facility?.name}
                  </Text>

                </View>

              </View>
              {course?.facility?.addressLine1 ?
                <View style={styles.row}>
                  <Image source={location} />
                  <View style={styles.valueContainer}>
                    <Text style={styles.facility}>
                      {course?.facility?.addressLine1}, {course?.facility?.addressLine2}, {course?.facility?.city}, {course?.facility?.state}
                    </Text>
                  </View>
                  {course?.facility?.location?.coordinates[0] && course?.facility?.location?.coordinates[1] && (<TouchableOpacity onPress={() => { handleMapNavigation(course?.facility?.location?.coordinates[0], course?.facility?.location?.coordinates[1]) }} ><Text style={styles.showOnMap}>Show on map</Text></TouchableOpacity>)}


                </View> : ""}

            </View>
          </View>
          {/* <View style={styles.cardDetailsTwo}></View> */}
        </View>
      </TouchableOpacity>
    )
  }
  return (
    <SafeAreaView>
    <Text style={{ fontSize: 16, fontFamily: "Lato-Bold", color: "#000", marginLeft: "5%", marginBottom: "2%" }}>
      {courses.length === 0 ?   <View style={styles.noCoursesContainer}>
        <Text style={{fontSize: 16, fontFamily: "Lato-Bold", color: "#000",}}>No courses available at the moment.</Text>
        {notifySuccess ? (
          <Text style={{color:"skyblue"}}>You will be notified when new courses are available.</Text>
        ) : (
          <View style={{width:"98%", display:"flex", flexDirection:"column", justifyContent:"space-between", alignItems:"center"}}>
            {/* <Text style={{color:"#000"}}>jhvghvghvghcvghvchgc</Text> */}
            <TextInput
              style={styles.input}
              value={email}
              onChangeText={setEmail}
              placeholder="Enter your email"
                placeholderTextColor="#9CA3AF"
              keyboardType="email-address"
              autoCapitalize="none"
            />
            {emailError ? <Text style={{color:"red"}}>{emailError}</Text> : null}
            <TouchableOpacity style={[styles.notifyButton, {marginTop:"3%"}]} onPress={handleNotifyMe}>
              <Text style={{color:"#fff"}}>Notify Me</Text>
            </TouchableOpacity>
          </View>
        )}
      </View> : `No of Courses/Classes: ${courses.length}`}
    </Text>
    <FlatList
      data={courses}
      renderItem={({ item, index }) => <CourseCard course={item} index={index} />}
      keyExtractor={item => item._id}
      // ListFooterComponent={renderFooter}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.5}
      refreshing={isRefreshing}
      // onRefresh={handleRefresh}
    />
  </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    backgroundColor: "#F5F5F5",
    paddingHorizontal: "3%",
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 10,
    borderRadius: 5,
    width: '100%',
    color: '#000',
  },
  notifyButton: {
    backgroundColor: "#0EA5E9",
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 5, marginLeft:4},
  noCoursesContainer: {
    flex: 1,
    // justifyContent: "center",
    // alignItems: "center",
  },
  noCoursesText: {
    fontSize: 18,
    marginBottom: 20,
    textAlign: "center",
  },
  container: {
    flex: 1,
    backgroundColor: "#fff",
    borderRadius: 5,
    marginBottom: "7%",
    marginHorizontal: 10,
    // shadowColor: "#000",
    // shadowOffset: { width: 0, height: 2 },
    // shadowOpacity: 0.1,
    // shadowRadius: 8,
    // elevation: 1,
    // borderColor:"#000",
    borderWidth: 0.5,
    // paddingHorizontal:"1%"
  },
  horizontalLine: {
    backgroundColor: "#000",
    height: 0.5,
    width: "95%",
    alignSelf: "center",
    marginTop: "2%",
  },
  cardDetails: {
    display: "flex",
    justifyContent: "space-between",
  },
  cardDetailsOne: {},
  cardDetailsTwo: {},
  sectionOne: {
    display: "flex",
    flexDirection: "row",
  },
  sectionTwo: {
    display: "flex",
    padding: "2%",
    // marginLeft: "2%",
    justifyContent: "space-evenly",
    maxWidth: "40%",
  },
  image: {
    width: "55%",
    height: 200,
    borderRadius: 5,
    // borderTopRightRadius: 5,
    resizeMode: "cover",
    marginVertical: "3%",
    marginHorizontal: "3%"
  },

  descriptionContainer: {
    flex: 1, // Allow this container to expand as needed
    marginTop: 5,
    // If you still encounter issues, consider adding a maxHeight here
  },
  description: {
    color: "black",
    // Ensure the text fits within the container
    flexShrink: 1,
  },
  detailsContainer: {
    padding: 15,
    paddingBottom: 20, // Increase padding at the bottom if necessary
  },
  classTypeContainer: {
    position: "absolute",
    top: "3%",
    left: "3%",
    backgroundColor: "#0EA5E9",
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 5,
  },
  classType: {
    color: "white",
    fontSize: 14,
  },
  detailsContainer: {
    padding: 15,
  },
  title: {
    fontSize: 16,
    // fontWeight: "bold",
    color: "black",
    fontFamily: "Lato-Bold"
  },
  category: {
    fontSize: 16,
    color: "black",
    opacity: 0.6,
    fontFamily: "Lato-Regular"

  },
  description: {
    marginTop: 5,
    color: "black",
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginVertical: 5,
  },
  label: {
    flex: 1,
    fontSize: 14,
    fontWeight: "500",
    color: "black",
    fontFamily: "Lato-Regular"

  },
  valueContainer: {
    flex: 2,
    // flexWrap: "wrap",
    marginLeft: 5,

  },
  value: {
    fontSize: 14,
    color: "black",
    flexWrap: "wrap",
    fontFamily: "Lato-Regular"

  },
  rowView: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  labelView: {
    textDecorationColor: "#000",
    borderBottomWidth: 1,
    borderBottomColor: "#000",
    fontWeight: "600",
    color: "#000",
  },
  rowReview: {},
  labelReview: {
    color: "grey",
    fontFamily: "Lato-Regular"

  },
  reviews: {
    display: "flex",
    flexDirection: "row",
  },
  reviewStar: {
    width: 20,
    height: 20,
    marginRight: 4,
  },
  proficiencyRow: {
    flexDirection: "row",
    // justifyContent: "space-between",
    // marginVertical: 5,
    alignItems: "center",
  },
  proficiencyLabel: {
    flex: 2,
    flexWrap: "wrap",
    marginLeft: 5,
  },
  proficiencyContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
  },

  proficiency: {
    borderWidth: 1,
    borderColor: "#0EA5E9",
    backgroundColor: "rgba(14, 165, 233, 0.2)",
    color: "#0EA5E9",
    paddingVertical: 5,
    paddingHorizontal: 10,
    marginRight: 5,
    marginBottom: 5,
    borderRadius: 5,
    fontSize: 12,
    fontFamily: "Lato-Regular"

    // width: "50%",
  },

  facility: {
    fontSize: 14,
    color: "black",
    fontFamily: "Lato-Regular"

    // marginLeft: 5,
  },
  noCourseText: {
    fontSize: 16,
    color: "black",
    textAlign: "center",
    marginTop: 20,
    fontFamily: "Lato-Regular"

  },
  courseDesc: {
    maxHeight: 100,
    maxWidth: "90%",
    overflow: "hidden",
    marginLeft: "3%",
    margin: 0,
    padding: 0,
  },
  buttonContainer: {
    borderRadius: 5,
    overflow: "hidden",
backgroundColor:"#E31F26"
  },
  showOnMap: {
    color: '#0EA5E9',
    fontWeight: '400',
    textDecorationLine: 'underline',
    alignSelf: 'flex-start',
    fontFamily: "Lato-Regular"

  },
  
});
