import React, { useEffect, useState, useCallback } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Modal,
  TextInput,
  Button,
  Image,
  FlatList,
  ActivityIndicator,
  SafeAreaView,
  Alert,

} from "react-native";
import { Picker } from "@react-native-picker/picker";
import { SelectList } from "react-native-dropdown-select-list";
import { useAuth } from "../Context/AuthContext";
import { useFormik } from 'formik';
import * as Yup from 'yup';
import BookingDetails from "./BookingDetails";
import { NEXT_PUBLIC_BASE_URL, COACH_ID_TOKEN, NEXT_WALLET_URL } from "@env";
import axios, { all } from "axios";
import Search from "../components/Search";
import moment from "moment";
import Footer from "../components/footer";
import { Rating, AirbnbRating } from 'react-native-ratings';
import { RadioButton, DataTable, Searchbar } from "react-native-paper";
import CheckBox from '@react-native-community/checkbox';
import Tooltip from 'react-native-walkthrough-tooltip';
import { useNavigation, useIsFocused, useFocusEffect } from "@react-navigation/native";
import { setLocale } from "yup";
import CustomRadioButton from "../components/CustomRadioButton";
import { State } from "country-state-city";
const edit = require("../assets/edit.png");
const close = require("../assets/delete.png");
const location = require("../assets/locatiothree.png");
const wallet = require("../assets/coins-stack.png");
const coins = require("../assets/coins.png")

const FilterButton = ({ onPress, active, children }) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      style={{
        borderWidth: 1,
        borderColor: "darkgray",
        paddingVertical: 5,
        paddingHorizontal: 15,
        borderRadius: 5,
        backgroundColor: active ? "black" : "white",
        marginBottom: 10, // Add marginBottom for proper alignment
      }}
    >
      <Text style={{ color: active ? "white" : "black", fontWeight: "600" }}>
        {children}
      </Text>
    </TouchableOpacity>
  );
};

const TransactionTable = ({ transaction }) => {
  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'green';
      case 'pending':
        return 'yellow';
      default:
        return 'red';
    }
  };
  return (
    <ScrollView >
      <View>

        <View key={transaction?._id}>
          <View style={styles.transactionCard}>
            <View style={styles.transactionRow}>
              <Text style={styles.transactionLabel}>Course/Session:</Text>
              <Text style={styles.transactionValue}>{transaction?.courseId?.courseName}</Text>
            </View>
            <View style={styles.transactionRow}>
              <Text style={styles.transactionLabel}>Debit:</Text>
              <Text style={styles.transactionValue}>{transaction?.type === 'debit' ? transaction?.amount : 'N/A'}</Text>
            </View>
            <View style={styles.transactionRow}>
              <Text style={styles.transactionLabel}>Credit:</Text>
              <Text style={styles.transactionValue}>{transaction?.type === 'credit' ? transaction?.amount : 'N/A'}</Text>
            </View>
            <View style={styles.transactionRow}>
              <Text style={styles.transactionLabel}>Status:</Text>
              <View style={[styles.statusContainer, { borderColor: getStatusColor(transaction?.walletId?.status) }]}>
                <Text style={[styles.transactionValue, { color: getStatusColor(transaction?.walletId?.status) }]}>{transaction?.walletId?.status}</Text>
              </View>
            </View>
          </View>
        </View>


      </View>
    </ScrollView>
  );
};


const PlayerProfile = () => {

  const { userId, userToken, playerData, fetchUserDetails } = useAuth();
  const [userDetails, setUserDetails] = useState({});
  const [isPersonalInfoModalVisible, setIsPersonalInfoModalVisible] = useState(
    false
  );
  const [isSportsModalVisible, setIsSportsModalVisible] = useState(false);
  const homeStateName = State.getStateByCodeAndCountry(playerData.homeState, "IN")?.name || "";

  const [editedFirstName, setEditedFirstName] = useState("");
  const [editedLastName, setEditedLastName] = useState("");
  const [editedMobile, setEditedMobile] = useState("");
  const [editedSchoolName, setEditedSchoolName] = useState("");

  const [isStateDropDown, setIsStateDropDown] = useState(false)
  const [selectedState, setSelectedState] = useState("");
  const [states, setStates] = useState([])

  const [editedEmail, setEditedEmail] = useState("");
  const [sports, setSports] = useState([]);
  const [selectedSports, setSelectedSports] = useState([]);
  const [newSportName, setNewSportName] = useState("");
  const [selected, setSelected] = useState("");
  const [selectedSportId, setSelectedSportId] = useState("");
  const [selectedSportIndex, setSelectedSportIndex] = useState(null);
  const [bookings, setBookings] = useState([]);
  const [hasMore, setHasMore] = useState(true);
  const [pageNumber, setPageNumber] = useState(1);


  const [appliedFilters, setAppliedFilters] = useState([]);
  const navigation = useNavigation();


  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingBooking, setIsLoadingBooking] = useState(false);
  const [isLoadingTran, setIsLoadingTran] = useState(false);

  const [walletData, setWalletData] = useState([]);
  const [playerDataUser, setPlayerData] = useState("");

  const [checked, setChecked] = useState("bookings");

  const [allTransaction, setAllTransactions] = useState([]);
  const [transactionMore, setTransactionMore] = useState(true);
  const [page, setPage] = useState(1);

  const [dataFetched, setDataFetched] = useState(true);
  const [updateTrigger, setUpdateTrigger] = useState(false);

  const [filterChange, setFilterChange] = useState(appliedFilters);
  const [filterBoolean, setFilterBoolean] = useState(false)

  useFocusEffect(
    React.useCallback(() => {
      if (!playerData?.mobile || !playerData?.homeState) {
        if (!playerData.mobile) {
          Alert.alert("warning", "Please add mobile number")
        }
        if(!playerData.homeState)
        {
          Alert.alert("warning", "Please add home state")
        }
      }
    }, [])
  );
  const dummyData = [
    {
      label: 'data 1'
    },
    {
      label: 'data 2'
    }
  ];

  useEffect(() => {
    const stateData = State.getStatesOfCountry("IN");
    setStates(stateData);
  }, []);
  const toggleStateDropdown = () => {
    setIsStateDropDown(!isStateDropDown);
  };
  const handleSelectState = (stateCode) => {
    setSelectedState(stateCode);
    toggleStateDropdown();
  };
  const BookingCard = ({ booking }) => {
    const navigation = useNavigation();
    const [cancelModal, setCancelModal] = useState(false);
    const [checkedBox, setCheckedBox] = useState(false);
    const [checked, setChecked] = useState('cancel');
    const [selectedSort, setSelectedSort] = useState('');
    const [selectedDates, setSelectedDates] = useState([]);
    const [selectedRadioOption, setSelectedRadioOption] = useState(null);
    const [cancleLoad, setCancelLoad] = useState(false);
    const [bookingIds, setBookingIds] = useState(booking?._id);
    useEffect(() => {
      setBookingIds(booking?._id)
    }, [booking])
    const handleOptionChange = (e) => {
      setSelectedRadioOption(e?.target?.value);
    };
    const toggleCancleModal = () => {
      setCancelModal(!cancelModal);
    };
    const handleDatePress = (dateKey) => {
      handleCheckbox(dateKey);
    }
    const startDate = new Date(
      booking?.bookingDate
    ).toLocaleDateString("en-IN", {
      day: "numeric",
      month: "long",
      year: "numeric",
    });
    const classTypeText =
      booking?.courseId?.classType === "class"
        ? "Session"
        : booking?.courseId?.classType;
    const statusText = booking?.status === "Active" ? "Active" : booking?.status === "Inactive" ? "InActive" : booking?.status;
    const statusColor = booking?.status === "Active" ? "#4CAF50" : booking?.status === "Inactive" ? "red" : "black";
    const handleCheckbox = (date) => {
      if (selectedDates.includes(date)) {
        setSelectedDates(selectedDates.filter((selectedDate) => selectedDate !== date));
      } else {
        setSelectedDates([...selectedDates, date]);
      }
    };
    const handleCancellation = async () => {
      try {
        setCancelLoad(true);
        const dates = booking.classes.filter((x) => {
          const dateKey = `${x.startDate}-${x.startTime}-${x.endDate}-${x.endTime}`;
          return selectedDates.includes(dateKey);
        });

        const myHeaders = new Headers();
        myHeaders.append("Content-Type", "application/json");
        myHeaders.append("Authorization", `Bearer ${userToken}`);
        const raw = JSON.stringify({
          "sender": "player",
          "status": checked === "cancel" ? "cancelled" : "rescheduled",
          "classes": dates
        });

        const requestOptions = {
          method: "POST",
          headers: myHeaders,
          body: raw,
          redirect: "follow"
        };
        const response = await fetch(`${NEXT_PUBLIC_BASE_URL}/api/booking/cancel/${booking._id}`, requestOptions);
        const result = await response.json();
        if (result) {

          if (checked === "cancel") {
            setCancelModal(false);
            setSelectedDates([]);
            setCancelLoad(false)
            Alert.alert("Status", "Booking Cancelled Successfully", [
              { text: "OK", onPress: () => { setUpdateTrigger(prev => !prev) } } //
            ]);
          } else {
            setCancelModal(false);
            setSelectedDates([]);
            setCancelLoad(false)
            Alert.alert("Status", "To reschedule choose other class", [
              {
                text: "OK", onPress: () => {
                  setUpdateTrigger(prev => !prev); navigation.navigate("Courses", {
                    courseId: booking?.courseId?._id,
                  })
                }
              }
            ]);
          }
        } else {
          alert("Failed to cancel booking");
        }
      } catch (error) {
        console.error("Error cancelling booking: 227");
        Alert.alert("Result", "An error occurred while cancelling booking Please try again");
      }
    };

    ratingCompleted = async (newRating, bookingId) => {

      const requestOptions = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json', Authorization: `Bearer ${userToken}`,
        },
        body: JSON.stringify({ stars: newRating }),
      };
      const response = await fetch(`${NEXT_PUBLIC_BASE_URL}/api/course/ratings/${bookingId}`, requestOptions);
      const result = await response.json()
    };

    return (
      <TouchableOpacity
        onPress={() => {
          navigation.navigate("BookingDetails", { bookingId: booking?._id });
        }}
      >
        <View style={styles.bookingCard}>
          <View style={styles.bookingCardSection}>
            <View style={styles.bookingCardSubSection}>
              <Image
                source={{ uri: booking?.courseId?.images[0].url }}
                style={styles.bookingImage}
              />
            </View>
            <View style={styles.bookingCardSubSectionTwo}>
              <View>
                <Text style={styles.bookingName}>
                  {booking?.courseId?.courseName}
                </Text>

              </View>
              {booking?.status === "Completed" && (
                <View style={{ display: "flex", alignItems: "flex-start" }}>
                  <Rating
                    type='star'
                    ratingCount={5}
                    imageSize={20}
                    startingValue={0}
                    onFinishRating={(newRating) => this.ratingCompleted(newRating, booking?._id)}
                  />
                </View>
              )}

              <View >
                <Text style={styles.bookingAmount}>
                  Rs. {booking?.pricePaid}{' '}
                  <Text style={{ color: "grey" }}>
                    (inclusive all taxes)
                  </Text>
                </Text>
              </View>
              <View
                style={{
                  display: "flex",
                  flexDirection: "row",
                  marginVertical: "2%",
                }}
              >
                <Image source={location} />
                <Text style={{ color: "#5A5D60", fontSize: 12, fontWeight: "500" }}>
                  {booking?.courseId?.facility?.addressLine1} ,{" "}
                  {booking?.courseId?.facility?.city}
                </Text>
              </View>
              <View style={styles.status}>
                <Text
                  style={{
                    color: "#FCAB00",
                    borderColor: "#FCAB00",

                    marginLeft: "1%",
                    borderWidth: 1,
                    borderRadius: 5,
                    padding: "2%",
                    fontSize: 12,
                    marginLeft: "3%"
                  }}
                >
                  {classTypeText}
                </Text>
                <Text
                  style={{
                    color: statusColor,
                    marginLeft: "1%",
                    borderColor: statusColor,
                    borderWidth: 1,
                    borderRadius: 5,
                    padding: "2%",
                    fontSize: 12,
                    marginLeft: "3%"
                  }}
                >
                  {statusText}
                </Text>
              </View>
            </View>
          </View>
          <View style={styles.bookingCardSubSectionThree}>
            {
              booking?.groupSize > 1 ? (
                <TouchableOpacity disabled style={{ opacity: 0.5 }}>
                  <View
                    style={{
                      width: "100%",
                      height: 35,
                      borderRadius: 3,
                      backgroundColor: "#E31F26",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <Text style={{ fontSize: 14, color: "#fff" }}>Not Available</Text>
                  </View>
                </TouchableOpacity>
              ) : booking?.status == "Inactive" ? (
                <View style={{ opacity: 0.5 }}>
                  <View
                    style={{
                      width: "100%",
                      height: 35,
                      borderRadius: 3,
                      backgroundColor: "#E31F26",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <Text style={{ fontSize: 14, color: "#fff" }}>
                      InActive
                    </Text>
                  </View>
                </View>
              ) : (
                <TouchableOpacity onPress={toggleCancleModal}>
                  <View
                    style={{
                      width: "100%",
                      height: 35,
                      borderRadius: 3,
                      backgroundColor: "#E31F26",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <Text style={{ fontSize: 14, color: "#fff" }}>Cancel / Reschedule</Text>
                  </View>
                </TouchableOpacity>
              )
            }

          </View>
          <View style={styles.horizontalLine} />
        </View>
        <Modal visible={cancelModal} animationType="slide" transparent={true}>
          <View style={styles.modalOverlay}>
            <View style={styles.modalView}>
              <Text
                style={{ fontSize: 18, fontWeight: "500", marginBottom: "8%", color: "#000", marginLeft: "2%" }}
              >
                Cancellation & Refund
              </Text>
              <View style={[styles.cancleRefund, { marginBottom: "3%" }]}>
                <View style={[styles.colAlign, { marginRight: "5%" }]}>

                  <CustomRadioButton
                    value="cancel"
                    status={checked === 'cancel'}
                    onPress={() => setChecked('cancel')}
                    label="Cancel"
                  />
                </View>

                <View style={[styles.colAlign]}>
                  <CustomRadioButton
                    value="rescheduled"
                    status={checked === 'rescheduled'}
                    onPress={() => setChecked('rescheduled')}
                    label="Rescheduled"
                  />
                </View>
              </View>
              <ScrollView contentContainerStyle={styles.scrollView}>
                {booking?.classes?.map((bookingTime, index) => {
                  const date = new Date(bookingTime.date).toLocaleDateString('en-IN', { day: 'numeric', month: 'long', year: 'numeric' });
                  const dateKey = `${bookingTime.startDate}-${bookingTime.startTime}-${bookingTime.endDate}-${bookingTime.endTime}`; 
                  const startDate = moment(bookingTime.startDate).format("Do MMMM YYYY");
                  const endDate = moment(bookingTime.endDate).format("Do MMMM YYYY");
                  const startTime = moment(bookingTime.startTime, "HH:mm").format("hh:mm A");
                  const endTime = moment(bookingTime.endTime, "HH:mm").format("hh:mm A");
                  return (
                    <TouchableOpacity onPress={() => handleDatePress(bookingTime?.date)} disabled={bookingTime?.status !== "upcoming"} style={{
                      opacity: bookingTime?.status === "upcoming" ? 1 : 0.2,
                    }}>
                      <View key={index} style={styles.bookingTimeContainer}>
                        <View style={{ flexDirection: "row", alignItems: "center", width: "80%", marginVertical: "4%" }}>

                          <CheckBox
                            disabled={bookingTime?.status != "upcoming"}
                            value={selectedDates.includes(dateKey)}
                            onValueChange={() => handleCheckbox(dateKey)}
                            style={{ marginRight: 10 }}
                            tintColors={{ true: 'black', false: 'gray' }}
                          />
                          <View style={{ width: "90%" }}><Text style={{ color: "#000", fontSize: 14, lineHeight: 20 }}>
                            {booking?.courseId?.classType === "class"
                              ? date
                              : date}{" "}
                            ({startTime} - {endTime})
                            {' '} <Text style={{ color: bookingTime?.status === "upcoming" ? "green" : "red" }}>({bookingTime?.status})</Text>
                          </Text></View>
                        </View>
                      </View>
                    </TouchableOpacity>
                  );
                })}
              </ScrollView>
              <View style={styles.cancleButton}>
                <TouchableOpacity onPress={toggleCancleModal} style={{ marginRight: "2%" }}>
                  <Text style={{ color: "black", }}>Cancel</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={handleCancellation}
                  disabled={selectedDates.length === 0}  // Disable button when no dates are selected
                  style={{
                    marginTop: 'auto',
                    marginBottom: 'auto',
                    marginHorizontal: "3%",
                    opacity: selectedDates.length === 0 ? 0.5 : 1
                  }}>
                  <View style={{
                    width: "100%",
                    height: 35,
                    borderRadius: 3,
                    backgroundColor: "#000",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    padding: "5%"
                  }}>
                    <View>
                      {cancleLoad ? (<ActivityIndicator size="small" color="#fff" />) : (
                        <Text style={{ fontSize: 12, color: "#fff" }}>
                          Proceed
                        </Text>
                      )}
                    </View>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </TouchableOpacity>
    );
  };



  const fetchTransactionData = async (page = 1) => {
    if (isLoadingTran) return; // Avoid multiple simultaneous requests
    setIsLoadingTran(true);
    try {
      const requestOptions = {
        method: "GET",
        redirect: "follow",
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${userToken}`,
        },
      };
      const response = await fetch(
        `${NEXT_WALLET_URL}/api/transactions?playerId=${userId}&page=${page}`,
        requestOptions
      );
      const result = await response.json();
      if (result.data.length > 0) {
        setAllTransactions(prevTransactions => page === 1 ? result.data : [...prevTransactions, ...result.data]);
        if (result.data.length < 25) {
          setTransactionMore(false); // No more data if fetched less than the page size
        }
      } else {
        setTransactionMore(false); // No more data to load
      }
      setIsLoadingTran(false);
    } catch (error) {
      console.error("Error fetching wallet data:", error);
      setIsLoadingTran(false);
    }
  };
  useEffect(() => {
    if (playerData) {
      setPage(1); // Reset page to 1
      setAllTransactions([]); // Clear previous transactions
      fetchTransactionData(1); // Fetch the first page
    }
  }, [playerData, checked, walletData]);

  useEffect(() => {
    if (page > 1) {
      fetchTransactionData(page);
    }
  }, [page]);

  const handleEndReached = () => {
    if (!isLoadingTran && transactionMore) {
      setPage(prevPage => prevPage + 1);
    }
  };
  // Handle filter change
  const handleFilterChange = (filterParam) => {
    const updatedFilters = appliedFilters.includes(filterParam)
      ? appliedFilters.filter((filter) => filter !== filterParam)
      : [...appliedFilters, filterParam];
    setAppliedFilters(updatedFilters);
  };


  useEffect(() => {
    const fetchWalletData = async () => {
      try {
        setIsLoading(true);

        const requestOptions = {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${userToken}`,
          },
          redirect: "follow",
        };
        const response = await fetch(
          `${NEXT_WALLET_URL}/api/wallet?email=${playerData?.email}`,
          requestOptions
        );
        const result = await response.json();
        setWalletData(result.data[0]);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching wallet data:", error);
        setIsLoading(false);
      }
    };
    if (playerData) {
      fetchWalletData();
    }
  }, [playerData, dataFetched, updateTrigger]);

  const fetchDataPlayerBooking = useCallback(async (page) => {
    if (isLoadingBooking) return;
    setIsLoadingBooking(true);
    const filterParams = appliedFilters.map((param) => `&${param}`).join("");
    try {
      const requestOptions = {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userToken}`,
        },
      };
      const response = await fetch(
        `${NEXT_PUBLIC_BASE_URL}/api/booking/?playerId=${userId}&page=${page}${filterParams}`,
        requestOptions
      );
      const result = await response.json();
      if (result?.data?.length > 0) {
        setBookings(prevBookings => page === 1 ? result.data : [...prevBookings, ...result.data]);
        if (result.data.length < 25) {
          setHasMore(false);
        }
      } else {
        setHasMore(false);
      }
      setIsLoadingBooking(false);
    } catch (error) {
      console.error(error);
      setIsLoadingBooking(false);
    }
  }, [userToken, userId, appliedFilters, isLoadingBooking]);

  useEffect(() => {
    const initialFetch = async () => {
      setIsLoadingBooking(true);
      setBookings([]);
      setPageNumber(1);
      setHasMore(true);
      await fetchDataPlayerBooking(1);
    };
    initialFetch();
  }, [appliedFilters]);

  useEffect(() => {
    if (pageNumber > 1) {
      fetchDataPlayerBooking(pageNumber);
    }
  }, [pageNumber]);

  const loadMoreBookings = async () => {
    if (hasMore && !isLoadingBooking) {
      setPageNumber(prevPage => prevPage + 1);
    }
  };

  const fetchDataPlayer = async () => {
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_BASE_URL}/api/player/${userId}`,
        {
          headers: {
            Authorization: `Bearer ${userToken}`,
          },
        }
      );
      setUserDetails(response.data);
      setPlayerData(response.data);
      setIsLoading(false);
    } catch (error) {
      console.error("Error fetching data:", error);
      setIsLoading(false);
    }
  };
  useEffect(() => {
    fetchDataPlayer();
  }, [userId]);
  useEffect(() => {
    const fetchSports = async () => {
      try {
        const response = await axios.get(
          `${NEXT_PUBLIC_BASE_URL}/api/category`
        );
        setSports(response.data);
      } catch (error) {
        console.error("Error fetching data: 580");
      }
    };
    fetchSports();
  }, [userId]);
  const handleEditPersonalInfoClick = () => {
    setEditedFirstName(userDetails.firstName);
    setEditedLastName(userDetails.lastName);
    setEditedMobile(userDetails.mobile);
    setEditedSchoolName(userDetails.schoolName || "");
    setIsPersonalInfoModalVisible(true);
  };
  const handleCloseModal = () => {
    setIsPersonalInfoModalVisible(false);
  };
  const handleSaveChangesPersonal = async () => {
    try {
      const updatedPlayerData = {
        firstName: editedFirstName,
        lastName: editedLastName,
        mobile: editedMobile,
        schoolName: editedSchoolName,
        homeState: selectedState
      };

      const updateHeaders = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userToken}`,
      };

      const response = await fetch(
        `${NEXT_PUBLIC_BASE_URL}/api/player/${userId}`,
        {
          method: "PATCH",
          headers: updateHeaders,
          body: JSON.stringify(updatedPlayerData),
        }
      );

      if (response.ok) {
        setIsPersonalInfoModalVisible(false);
        fetchDataPlayer();
        fetchUserDetails(userToken);
        // Update local state if needed
      } else {
        console.error("Error updating player data:", response.data);
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };

  //sport modall and the function
  const handleEditSportsClick = () => {
    setIsSportsModalVisible(true);
  };

  const handleSaveSports = () => {
    setIsSportsModalVisible(false);
  };
  const handleAddSport = () => {
    setPlayerData((prevData) => ({
      ...prevData,
      hobbies: [...(prevData.hobbies || []), { id: "" }],
    }));
  };

  const handleSportSelection = (selectedSportId, index) => {
    let updatedPlayerData = { ...playerDataUser };
    if (!updatedPlayerData.hobbies) {
      updatedPlayerData.hobbies = [];
    }
    updatedPlayerData.hobbies[index] = { id: selectedSportId };
    setPlayerData(updatedPlayerData);
  };
  const handleDeleteHobby = (index) => {
    setPlayerData((prevData) => ({
      ...prevData,
      hobbies: prevData.hobbies.filter((hobby, i) => i !== index),
    }));
  };
  const handleSaveChangesSports = async () => {
    // Check for duplicate sports
    const sportIds = playerDataUser.hobbies.map((hobby) => hobby.id._id || hobby.id);
    const hasDuplicates = new Set(sportIds).size !== sportIds.length;

    if (hasDuplicates) {
      Alert.alert("Duplicate Sport", "You have selected a sport that is already added. Please choose a different sport.");
      return; // Exit the function if there are duplicates
    }

    // Proceed with the update if no duplicates are found
    let profileHeaders = new Headers();
    profileHeaders.append("Content-Type", "application/json");

    let updateHeaders = new Headers();
    updateHeaders.append("Content-Type", "application/json");
    updateHeaders.append("Authorization", `Bearer ${userToken}`);

    let updateResponse = await fetch(
      `${NEXT_PUBLIC_BASE_URL}/api/player/${userId}`,
      {
        method: "PATCH",
        headers: updateHeaders,
        body: JSON.stringify({ hobbies: playerDataUser.hobbies }),
      }
    );

    if (updateResponse.ok) {
      setIsSportsModalVisible(false);
      fetchDataPlayer();
    } else {
      console.error("Error updating sports");
    }
  };
  // const handleSaveChangesSports = async () => {
  //   // First, get the player ID and token
  //   let profileHeaders = new Headers();
  //   profileHeaders.append("Content-Type", "application/json");

  //   let updateHeaders = new Headers();
  //   updateHeaders.append("Content-Type", "application/json");
  //   updateHeaders.append("Authorization", `Bearer ${userToken}`);

  //   let updateResponse = await fetch(
  //     `${NEXT_PUBLIC_BASE_URL}/api/player/${userId}`,
  //     {
  //       method: "PATCH",
  //       headers: updateHeaders,
  //       body: JSON.stringify({ hobbies: playerDataUser.hobbies }),
  //     }
  //   );

  //   if (updateResponse.ok) {
  //     setIsSportsModalVisible(false);
  //     fetchDataPlayer();
  //   } else {
  //     console.error("Error updating sports");
  //   }
  // };
  const phoneRegExp =
    /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/;

  const personalInfoSchema = Yup.object().shape({
    firstName: Yup.string()
      .trim()
      .min(3, "Please Type atleast 3 Letters")
      .max(15, "Cannot exceed 15 Letters")
      .required("Please enter your first name"),
    lastName: Yup.string()
      .trim()
      .min(3, "Please Type atleast 3 Letters")
      .max(15, "Cannot Exceed 15 Letters")
      .required("Please enter your last name"),
    mobile: Yup.string()
      .min(10, "Min Limit is 10 Digits")
      .max(10, "Max Limit is 10 Digits")
      .matches(phoneRegExp, "Phone number is not valid")
      .required("Please enter your number"),
    schoolName: Yup.string()
      .min(4, "School Must be atleast 4 Letters")
      .max(30, "Cannot Exceed 30 Letters"),
  });
  return (
    <SafeAreaView style={{ flex: 1 }}>

      <ScrollView>
        <View contentContainerStyle={styles.container}>

          <View style={styles.card}>
            {isLoading ? (
              <View style={styles.loaderContainer}>
                <ActivityIndicator size="large" color="#0000ff" />
              </View>
            ) : (
              <View>
                <View style={styles.personalInfoHeader}>
                  <Text style={styles.cardHeading}>Personal Information</Text>
                  <TouchableOpacity
                    onPress={handleEditPersonalInfoClick}
                    style={styles.editButton}
                  >
                    <Image source={edit} style={{ width: 20, height: 20 }} />
                  </TouchableOpacity>
                </View>
                <View style={styles.userInfo}>
                  <Text style={styles.userInfoLabel}>First Name:</Text>
                  <Text style={styles.userInfoText}>{userDetails.firstName}</Text>
                </View>
                <View style={styles.userInfo}>
                  <Text style={styles.userInfoLabel}>Last Name:</Text>
                  <Text style={styles.userInfoText}>{userDetails.lastName}</Text>
                </View>
                <View style={styles.userInfo}>
                  <Text style={styles.userInfoLabel}>Phone No:</Text>
                  <Text style={styles.userInfoText}>{userDetails.mobile}</Text>
                </View>
                <View style={styles.userInfo}>
                  <Text style={styles.userInfoLabel}>Email:</Text>
                  <Text style={styles.userInfoText}>{userDetails.email}</Text>
                </View>
                <View style={styles.userInfo}>
                  <Text style={styles.userInfoLabel}>School Name:</Text>
                  <Text style={styles.userInfoText}>
                    {userDetails.schoolName}
                  </Text>
                </View>
                <View style={styles.userInfo}>
                  <Text style={styles.userInfoLabel}>Home State:</Text>
                  <Text style={styles.userInfoText}>
                    {homeStateName}
                  </Text>
                </View>
              </View>
            )}
          </View>

          <Modal visible={isPersonalInfoModalVisible} animationType="slide">
          <SafeAreaView style={{flex: 1}}>
            <View style={styles.modalContainer}>
              <View
                style={{
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "space-evenly",
                  alignItems: "center",
                  marginBottom: "3%",
                }}
              >
                <Text style={styles.editCardHeading}>
                  Edit Personal Information
                </Text>
              </View>
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>First Name:</Text>
                <TextInput
                  style={styles.input}
                  value={editedFirstName}
                  onChangeText={setEditedFirstName}
                  placeholder="Enter First Name"
                />
              </View>
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Last Name:</Text>
                <TextInput
                  style={styles.input}
                  value={editedLastName}
                  onChangeText={setEditedLastName}
                  placeholder="Enter Last Name"
                />
              </View>
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Phone No:</Text>
                <TextInput
                  style={styles.input}
                  value={editedMobile}
                  onChangeText={setEditedMobile}
                  placeholder="Enter Phone No"
                  keyboardType="numeric"
                />
              </View>
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>School Name:</Text>
                <TextInput
                  style={styles.input}
                  value={editedSchoolName}
                  onChangeText={setEditedSchoolName}
                  placeholder="Enter School Name"
                />
              </View>
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Email:</Text>
                <TextInput
                  style={styles.input}
                  value={userDetails.email}
                  editable={false} // Email field should be non-editable
                />
              </View>
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Home State:</Text>
                <TouchableOpacity
                  style={[styles.input, { justifyContent: 'center' }]} // Spread TextInput styles and adjust alignment
                  onPress={toggleStateDropdown}
                >
                  <Text style={{ color: '#000', paddingHorizontal: 12 }}>
                    {selectedState
                      ? states.find((state) => state.isoCode === selectedState)?.name
                      : homeStateName || "Select State"}
                  </Text>
                </TouchableOpacity>
                <Modal visible={isStateDropDown} animationType="slide" transparent={true}>
                  <View style={styles.inputContainerHobbies}>
                    <View style={styles.stateModalContainer}>
                      <ScrollView contentContainerStyle={styles.scrollView}>
                        {states.map((state) => (
                          <TouchableOpacity
                            key={state.isoCode}
                            style={styles.option}
                            onPress={() => handleSelectState(state.isoCode)}
                          >
                            <Text style={styles.optionText}>
                              {state.name} {selectedState === state.isoCode ? "✓" : ""}
                            </Text>
                          </TouchableOpacity>
                        ))}
                      </ScrollView>
                      <TouchableOpacity onPress={toggleStateDropdown} style={styles.closeButton}>
                        <Text style={{ color: "#fff", fontSize: 14 }}>Close</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </Modal>
              </View>
              <View style={styles.buttonContainer}>
                <View style={{backgroundColor:"#000", borderRadius:5}}><Button
                  color="#fff"
                  title="Save Changes"
                  onPress={handleSaveChangesPersonal}
                /></View>
                <View style={{backgroundColor:"#000", borderRadius:5}}>
                <Button title="Close" color="#fff" onPress={handleCloseModal} />

                </View>
              </View>
            </View>
            </SafeAreaView>
          </Modal>

          <View style={styles.card}>
            {isLoading ? (
              <View style={styles.loaderContainer}>
                <ActivityIndicator size="large" color="#0000ff" />
              </View>
            ) : (
              <View>
                <View style={styles.personalInfoHeader}>
                  <Text style={styles.cardHeading}>Sports you like most</Text>
                  <TouchableOpacity
                    onPress={handleEditSportsClick}
                    style={styles.editButton}
                  >
                    <Image source={edit} style={{ width: 20, height: 20 }} />
                  </TouchableOpacity>
                </View>
                <View style={styles.sportContainer}>
                  {userDetails?.hobbies?.map((hobby, index) => (
                    <View key={index} style={styles.sportItem}>
                      <Image
                        style={styles.sportIcon}
                        source={{ uri: hobby?.id?.image }}
                      />
                      <Text style={{ color: "#000", fontSize: 14 }}>
                        {hobby?.id?.name}
                      </Text>
                    </View>
                  ))}
                </View>
              </View>
            )}
          </View>
          <Modal visible={isSportsModalVisible} animationType="slide">
            <SafeAreaView style={{flex: 1}}>
            <ScrollView contentContainerStyle={styles.scrollView}>
              <View style={styles.modalContainer}>
                {playerDataUser?.hobbies?.map((hobby, index) => (
                  <View
                    key={index}
                    style={{
                      width: "90%",
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "space-between",
                    }}
                  >
                    <Text style={styles.inputvalue}>Sport {index + 1} : </Text>

                    <Picker
                      selectedValue={hobby?.id._id || hobby?.id}
                      style={{ width: 200, color: "#000" }}
                      onValueChange={(itemValue) => handleSportSelection(itemValue, index)}
                    >
                      <Picker.Item
                        label={hobby?.id?.name || "Select a sport"}
                        value=""
                      />
                      {sports?.data?.map((sport) => {
                        const isSportSelected = playerDataUser?.hobbies?.some((s) => s?.id?._id === sport._id || s?.id === sport._id);
                        return (
                          <Picker.Item
                            key={sport._id}
                            label={sport.name}
                            value={sport._id}
                            color={isSportSelected ? "grey" : "black"}
                            style={isSportSelected ? styles.disabledItem : styles.enabledItem}
                            enabled={!isSportSelected}
                          />
                        );
                      })}
                    </Picker>
                    <TouchableOpacity onPress={() => handleDeleteHobby(index)}>
                      {/* delete icon */}
                      <Image source={close} style={{ width: 20, height: 20 }} />
                    </TouchableOpacity>
                  </View>
                ))}

                <View style={styles.buttonContainer}>
                  <View style={{ backgroundColor: "#000", borderRadius: 4 }}>
                    <Button
                      color="#fff"
                      title="Add Sports"
                      onPress={handleAddSport}
                    />
                  </View>
                  <View style={{ backgroundColor: "#000", borderRadius: 4 }}>
                    <Button
                      color="#fff"
                      title="Save Changes"
                      onPress={handleSaveChangesSports}
                    />
                  </View>
                  <View style={{ backgroundColor: "#000", borderRadius: 4 }}>
                    <Button title="Close" color="#fff" onPress={handleSaveSports} />
                  </View>
                </View>
              </View>
            </ScrollView>
            </SafeAreaView>
          </Modal>

        </View>
        <View style={styles.card}>
          <View style={styles.bookingAlign}>
            <View style={[styles.colAlign, { marginRight: "10%" }]}>
              {/* <RadioButtonRN
                data={dummyData}
                selectedBtn={(e) => console.log(e)}
              /> */}
              {/* <RadioButton
                value="bookings"
                status={checked === "bookings" ? "checked" : "unchecked"}
                onPress={() => setChecked("bookings")}
                color="#000"
              /> */}
              <CustomRadioButton
                value="bookings"
                status={checked === 'bookings'}
                onPress={() => setChecked("bookings")}
                label="Bookings"
              />
              {/* <Text style={styles.bookingText}>Bookings</Text> */}

            </View>
            <View style={[styles.colAlign, { marginRight: "10%" }]} >
              <CustomRadioButton
                value="transactions"
                status={checked === 'transactions'}
                onPress={() => setChecked("transactions")}
                label="Transactions"
              />
              {/* <RadioButton
                value="transactions"
                status={checked === "transactions" ? "checked" : "unchecked"}
                onPress={() => setChecked("transactions")}
                color="#000"
              />
              <Text style={styles.bookingText}>Transactions</Text> */}

            </View>
            {walletData && (<View style={[styles.walletcolAlign]}>
              <Image source={wallet} style={{ width: 20, height: 20 }} />
              <Text style={styles.walletCoin}>₹{walletData?.balance?.toFixed(2)}</Text>
            </View>)}
          </View>
          {checked === "bookings" ?
            <View style={styles.filterContainer}>
              <View style={styles.filterRow}>
                <FilterButton
                  onPress={() => handleFilterChange("courseType=course")}
                  active={appliedFilters.includes("courseType=course")}
                >
                  Course
                </FilterButton>
                <FilterButton
                  onPress={() => handleFilterChange("courseType=class")}
                  active={appliedFilters.includes("courseType=class")}
                >
                  Session
                </FilterButton>
                <FilterButton
                  onPress={() => handleFilterChange("status=Active")}
                  active={appliedFilters.includes("status=Active")}
                >
                  Active
                </FilterButton>
              </View>
              <View style={styles.filterRow}>
                <FilterButton
                  onPress={() => handleFilterChange("status=Inactive")}
                  active={appliedFilters.includes("status=Inactive")}
                >
                  InActive
                </FilterButton>
              </View>
            </View> : null}

          <ScrollView>
            {isLoadingBooking && pageNumber === 1 ? (
              <View style={styles.loaderContainer}>
                <ActivityIndicator size="large" color="#0000ff" />
              </View>
            ) : checked === "bookings" ? (
              bookings.length > 0 ? (
                <View>
                  <FlatList
                    data={bookings}
                    renderItem={({ item, index }) => (
                      <TouchableOpacity key={index} onPress={() => {
                        navigation.navigate("BookingDetails", { bookingId: item?._id });
                      }}>
                        <BookingCard booking={item} />
                      </TouchableOpacity>
                    )}
                    keyExtractor={(item) => item._id}
                    onEndReached={loadMoreBookings}
                    onEndReachedThreshold={0.5}
                    ListFooterComponent={
                      isLoadingBooking && hasMore ? (
                        <ActivityIndicator size="large" color="#0000ff" />
                      ) : !hasMore ? (
                        <Text style={{ textAlign: 'center', marginVertical: 20, color: "#000" }}>No more bookings available.</Text>
                      ) : null
                    }
                  />
                </View>
              ) : (
                <Text style={{ color: "#000", fontFamily: "Lato-Bold", fontSize: 16 }}>No bookings available.</Text>
              )
            ) : isLoadingTran && page === 1 ? (
              <View style={styles.loaderContainer}>
                <ActivityIndicator size="large" color="#0000ff" />
              </View>
            ) : (
              <View>
                <FlatList
                  data={allTransaction}
                  renderItem={({ item, index }) => (
                    <TransactionTable key={index} transaction={item} />
                  )}
                  keyExtractor={(item, index) => index.toString()}
                  onEndReached={handleEndReached}
                  onEndReachedThreshold={0.5}
                  ListFooterComponent={() => (
                    <>
                      {isLoadingTran ? <ActivityIndicator /> : null}
                      {!transactionMore && (
                        <Text style={{ textAlign: 'center', marginVertical: 20, color: "#000" }}>
                          No more transactions available
                        </Text>
                      )}
                    </>
                  )}
                />
              </View>
            )}
          </ScrollView>

        </View>
        <Footer />
      </ScrollView>

    </SafeAreaView>

  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: "skyblue",
  },
  filterContainer: {
    marginBottom: "1%",
  },
  filterRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: "1%",
  },
  card: {
    backgroundColor: "#FAFBFCFF",
    borderRadius: 10,
    // marginBottom: 20,
    padding: 15,
    elevation: 1,
    marginHorizontal: "4%",
    marginVertical: "5%",
  },
  personalInfoHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 10,
  },
  playerImage: {
    width: 100,
    // height: 100,
    borderRadius: 50,
  },
  userInfo: {
    flexDirection: "row",
    marginBottom: 10,
  },
  userInfoLabel: {
    color: "grey",
    marginRight: 5,
    fontSize: 16,
    lineHeight: 19.2,
    fontFamily: 'Lato-Regular'
  },
  userInfoText: {
    flex: 1,
    color: "#000",
    fontSize: 16,
    lineHeight: 19.2,
    fontFamily: 'Lato-Regular'
    // fontWeight:"500"
  },
  playerInfo: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 5,
    position: "relative",
    fontFamily: 'Lato-Regular'
  },
  inputContainerHobbies: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  stateModalContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: '15%',
    width: '92%',
    maxHeight: '80%',
  },
  modalContainer: {
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
    backgroundColor: "#FAFBFCFF",
    borderRadius: 10,
    marginHorizontal: 20,
    // marginTop: "10%",
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 10,
  },
  inputLabel: {
    width: 100,
    color: "#000",
    fontSize: 16,
  },
  input: {
    flex: 1,
    height: 40,
    borderWidth: 1.5,
    borderColor: "grey",
    borderRadius: 5,
    paddingHorizontal: 12,
    marginLeft: "3%",
    color: "#000",
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    width: "100%",
    marginTop: "1%",

  },
  playerName: {
    fontSize: 18,
    color: "#000",
  },
  inputvalue: {
    color: "#000",
  },
  editButton: {
    fontSize: 16,
    color: "grey",
    position: "absolute",
    // bottom: 100,
    right: 5,
  },
  cardHeading: {
    fontSize: 18,
    color: "#000",
    marginBottom: 10,
    fontWeight: "500",
  },
  editCardHeading: {
    fontSize: 18,
    color: "#000",
    fontWeight: "500",
  },
  playerDetailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 10,
  },
  playerDetailColumn: {
    flex: 1,
  },
  detailHeading: {
    color: "grey",
    marginRight: 5,
  },
  detailText: {
    flex: 1,
    color: "#000",
  },
  cardSubHeading: {
    fontSize: 14,
    marginBottom: 10,
    color: "grey",
  },
  sportContainer: {
    flexDirection: "row",
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
  sportItem: {
    flexDirection: 'column', // Stack items vertically
    alignItems: "center",    // Center-align items for uniformity
    marginHorizontal: "2%",
    marginVertical: "1%",
    width: '25%',            // Assuming 2 items per row at roughly half width minus margin
  },
  sportName: {
    color: "grey",
  },
  sportIcon: {
    width: 30,
    height: 30,
    marginBottom: 5,
    borderRadius: 25,
  },
  bookingCard: {
    marginBottom: "1%",
  },
  bookingCardSection: {
    display: "flex",
    flexDirection: "row"
  },
  bookingCardSubSection: {
    width: "50%"
  },
  bookingCardSubSectionTwo: {
    width: "50%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-between"
  },
  bookingCardSubSectionThree: {
    marginVertical: "6%",
    width: "50%",
  },
  bookingImage: {
    width: "90%",
    height: 150,
    // aspectRatio: 10 / 10,
    borderRadius: 10,
    marginRight: 10,
    resizeMode: "cover",
  },
  nameStatus: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  status: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "flex-start",

  },
  bookingInfo: {
    flex: 1,
    marginTop: 5,
    color: "#000",
    // paddingVertical: "5%",
  },
  bookingName: {
    fontSize: 16,
    color: "#000",
    fontWeight: "500",
    // marginBottom: 5,
  },
  bookingAmount: {
    fontSize: 14,
    color: "#0EA5E9",
    fontWeight: "500",
  },
  bookingDate: {
    fontSize: 10,
    color: "grey",
  },
  bookingTimeText: {
    color: "#5A5D60",
    fontSize: 12,

  },
  horizontalLine: {
    backgroundColor: "#BCBEC0",
    height: 1,
    width: "98%",
    alignSelf: "center",
    marginVertical: "4%",
  },
  bookingTimeContainer: {
    // marginVertical: "2%",
  },
  walletCard: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  walletcolAlign: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    flexWrap: 'wrap',
    width: "35%" // Allow the items to wrap
  },
  walletCoin: {
    color: "#000",
    fontWeight: "500",
    fontSize: 12,
    flex: 1, // Allows text to expand and wrap as needed
    minWidth: 50, // Minimum width before wrapping starts, adjust as needed
    padding: 2, // Optional, adjust for better spacing
  },
  walletBox: {
    flexDirection: "row",
    alignItems: "center",
  },
  bookingText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#000",
  },
  horizontalScroll: {
    flex: 1,
  },
  smallColumn: {
    minWidth: 50,
  },
  mediumColumn: {
    minWidth: 100,
  },
  largeColumn: {
    minWidth: 150,
  },
  wrapText: {
    flex: 1,
    flexWrap: "wrap",
  },
  cell: {
    flex: 1,
    padding: "1%",
    justifyContent: "center",
    textAlign: "left",
  },
  headerText: {
    color: "#000",
    fontWeight: "bold",
  },
  cellText: {
    color: "black",
  },
  container: {
    flex: 1,
    padding: 20,
  },
  header: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 10,
  },
  table: {
    flexDirection: "column",
  },
  row: {
    borderBottomWidth: 1,
    borderColor: "#000",
  },
  headerCell: {
    fontWeight: "bold",
  },
  colAlign: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  bookingAlign: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: "6%",
    width: "80%"
    // marginRight:"4%"
  },
  cancleRefund: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalView: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: "5%",
    width: '90%',
    maxHeight: '80%',
    // height:"30%"
    // alignItems: 'center', 
  },
  cancleButton: {
    flexDirection: "row",
    justifyContent: "flex-end",
    width: "100%",
    marginTop: "10%",
    alignItems: "center",
  },
  transactionCard: {
    backgroundColor: '#fff',
    margin: 10,
    paddingHorizontal: "5%",
    borderRadius: 5,
    borderColor: "#DFDFDF",
    borderWidth: 1,
    paddingVertical: "2%"
  },
  transactionRow: {
    flexDirection: 'row',
    justifyContent: 'flex-start', // Adjusted to start from the left
    marginVertical: "3%",
    alignItems: "center"
  },
  transactionLabel: {
    width: 120,
    fontWeight: '600',
    color: '#333',
    fontSize: 14,
  },
  transactionValue: {
    flex: 1,
    textAlign: 'left',
    color: '#626262',
    fontSize: 14,
    fontWeight: '400',
  },
  statusContainer: {
    borderWidth: 1,
    borderRadius: 5,
    paddingHorizontal: "4%",
    paddingVertical: "1%",
    display: "flex",
    alignItems: "center"
  },
  scrollView: {
    flexDirection: 'column',
    alignItems: 'center',
    marginVertical: "2%"
  },
  option: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  optionText: {
    fontSize: 16,
    color: '#000',
  },
  closeButton: {
    padding: 10,
    backgroundColor: '#000',
    alignItems: 'center',
    borderRadius: 10,
    marginTop: '3%',
  },








});
export default PlayerProfile;
