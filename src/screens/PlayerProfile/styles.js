import { ScaledSheet } from 'react-native-size-matters';

export const styles = ScaledSheet.create({
  container: {
    padding: 20,
    backgroundColor: 'skyblue',
  },
  filterContainer: {
    marginBottom: '1%',
  },
  filterRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: '1%',
  },
  card: {
    backgroundColor: '#FAFBFCFF',
    borderRadius: 10,
    padding: 15,
    elevation: 1,
    marginHorizontal: '4%',
    marginVertical: '5%',
    width: '92%',
    alignSelf: 'center',
  },
  personalInfoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  userInfo: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  userInfoLabel: {
    color: 'grey',
    marginRight: 5,
    fontSize: 16,
    lineHeight: 19.2,
  },
  userInfoText: {
    flex: 1,
    color: '#000',
    fontSize: 16,
    lineHeight: 19.2,
  },
  inputContainerHobbies: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  stateModalContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: '15%',
    width: '92%',
    maxHeight: '80%',
  },
  modalContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#FAFBFCFF',
    borderRadius: 10,
    marginHorizontal: 20,
    marginTop: '10%',
    marginBottom: '5%',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  inputLabel: {
    width: 100,
    color: '#000',
    fontSize: 16,
  },
  input: {
    flex: 1,
    height: 40,
    borderWidth: 1.5,
    borderColor: 'grey',
    borderRadius: 5,
    paddingHorizontal: 12,
    marginLeft: '3%',
    color: '#000',
    backgroundColor: 'white',
    textAlign: "left"
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 5,
    flexWrap: "wrap",
    width: '100%',
    marginTop: '10%',
  },
  editButton: {
    fontSize: 16,
    color: 'grey',
    position: 'absolute',
    right: 5,
  },
  cardHeading: {
    fontSize: 18,
    color: '#000',
    marginBottom: 10,
    fontWeight: '500',
  },
  editCardHeading: {
    fontSize: 18,
    color: '#000',
    fontWeight: '500',
  },
  sportContainer: {
    flexDirection: 'row',
    width: "100%",
    justifyContent: "flex-start",
    flexWrap: 'wrap',
  },
  sportItem: {
    flexDirection: 'column',
    alignItems: 'center',
    marginHorizontal: '2%',
    marginVertical: '1%',
    width: '25%',
  },
  sportIcon: {
    width: 30,
    height: 30,
    marginBottom: 5,
    borderRadius: 25,
  },
  bookingCard: {
    marginBottom: '1%',
  },
  bookingCardSection: {
    display: 'flex',
    flexDirection: 'row',
  },
  bookingCardSubSection: {
    width: '50%',
  },
  bookingCardSubSectionTwo: {
    width: '50%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  bookingCardSubSectionThree: {
    marginVertical: '6%',
    width: '100%',
  },
  bookingImage: {
    width: '90%',
    height: 150,
    borderRadius: 10,
    marginRight: 10,
    resizeMode: 'cover',
  },
  status: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-start',
  },
  bookingName: {
    fontSize: 16,
    color: '#000',
    fontWeight: '500',
  },
  bookingAmount: {
    fontSize: 14,
    color: '#0EA5E9',
    fontWeight: '500',
  },
  horizontalLine: {
    backgroundColor: '#BCBEC0',
    height: 1,
    width: '98%',
    alignSelf: 'center',
    marginVertical: '4%',
  },
  walletcolAlign: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    width: '35%',
  },
  walletRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: '8@vs',
    width: '100%',
  },
  walletLabel: {
    fontWeight: '500',
    fontSize: '12@ms',
    color: '#000',
    marginRight: '6@s',
  },
  walletIcon: {
    width: '20@s',
    height: '20@s',
    marginRight: '4@s',
  },
  walletCoin: {
    color: '#000',
    fontWeight: '500',
    fontSize: '12@ms',
    flex: 1,
    minWidth: '50@s',
    padding: '2@s',
  },
  bookingText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000',
  },
  colAlign: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  bookingAlign: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: '6%',
    flexWrap: 'wrap',
  },
  cancleRefund: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: '3%',
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalView: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: '5%',
    width: '90%',
    maxHeight: '80%',
  },
  cancleButton: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    width: '100%',
    marginTop: '10%',
    alignItems: 'center',
  },
  transactionCard: {
    backgroundColor: '#fff',
    margin: 10,
    paddingHorizontal: '5%',
    borderRadius: 5,
    borderColor: '#DFDFDF',
    borderWidth: 1,
    paddingVertical: '2%',
  },
  transactionRow: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginVertical: '3%',
    alignItems: 'center',
  },
  transactionLabel: {
    width: 120,
    fontWeight: '600',
    color: '#333',
    fontSize: 14,
  },
  transactionValue: {
    flex: 1,
    textAlign: 'left',
    color: '#626262',
    fontSize: 14,
    fontWeight: '400',
  },
  statusContainer: {
    borderWidth: 1,
    borderRadius: 5,
    paddingHorizontal: '4%',
    paddingVertical: '1%',
    display: 'flex',
    alignItems: 'center',
  },
  scrollView: {
    flexDirection: 'column',
    alignItems: 'center',
    marginVertical: '2%',
  },
  option: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  optionText: {
    fontSize: 16,
    color: '#000',
  },
  closeButton: {
    padding: 10,
    backgroundColor: '#000',
    alignItems: 'center',
    borderRadius: 10,
    marginTop: '3%',
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  bookingTimeContainer: {
    marginVertical: 5,
  },
  modalHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    marginBottom: '3%',
  },
  modalCloseButton: {
    color: '#fff',
    fontSize: 14,
  },
  modalPickerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 15,
    paddingHorizontal: 5,
  },
  modalPicker: {
    height: 40,
    width: 200,
    color: '#000',
  },
  modalPickerIcon: {
    width: 20,
    height: 20,
  },
  modalPickerValue: {
    color: '#000',
    fontSize: 16,
  },
  modalPickerLabel: {
    fontWeight: '500',
    fontSize: 16,
    color: '#000',
    minWidth: 80,
  },
  modalPickerDelete: {
    width: 20,
    height: 20,
  },
  modalErrorText: {
    color: 'red',
    marginBottom: 8,
  },
  bookingLocationRow: {
    flexDirection: 'row',
    marginVertical: '2%',
    alignItems: 'center',
    display: 'flex',
  },
  bookingLocationText: {
    color: '#5A5D60',
    fontSize: 12,
    fontWeight: '500',
  },
  bookingStatusType: {
    color: '#FCAB00',
    borderColor: '#FCAB00',
    marginLeft: '3%',
    borderWidth: 1,
    borderRadius: 5,
    padding: '2%',
    fontSize: 12,
  },
  bookingStatusText: {
    marginLeft: '3%',
    borderWidth: 1,
    borderRadius: 5,
    padding: '2%',
    fontSize: 12,
  },
  bookingActionContainer: {
    flexDirection: 'column',
    gap: 10,
    width: '100%',
  },
  bookingActionBtn: {
    width: '100%',
    height: 35,
    borderRadius: 3,
    backgroundColor: '#E31F26',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  attendanceBtn: {
    width: '100%',
    height: 35,
    borderRadius: 3,
    backgroundColor: '#22c55e',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  attendanceBtnText: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '600',
  },
  bookingActionBtnText: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '600',
  },
  bookingActionBtnInactive: {
    width: '100%',
    height: 35,
    borderRadius: 3,
    backgroundColor: '#E31F26',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    opacity: 0.5,
  },
  bookingActionBtnProceed: {
    width: '100%',
    height: 35,
    borderRadius: 3,
    backgroundColor: '#000',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    padding: '5%',
  },
  bookingActionBtnProceedText: {
    fontSize: 12,
    color: '#fff',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '500',
    marginBottom: '8%',
    color: '#000',
    marginLeft: '2%',
  },
  modalRadioLabel: {
    fontSize: 16,
    color: 'grey',
    fontWeight: '500',
  },
  modalRadioCol: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: '5%',
  },
  modalRadioColLast: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalBookingTimeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '80%',
    marginVertical: '4%',
  },
  modalBookingTimeText: {
    color: '#000',
    fontSize: 14,
    lineHeight: 20,
  },
  modalBookingTimeStatus: {
    color: 'green',
  },
  modalBookingTimeStatusInactive: {
    color: 'red',
  },
  modalCancelBtn: {
    marginRight: '2%',
  },
  modalProceedBtn: {
    marginTop: 'auto',
    marginBottom: 'auto',
    marginHorizontal: '3%',
  },
  addSportButtonCentered: {
    alignSelf: 'center',
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: '#000',
    borderRadius: 6,
    elevation: 2,
    width: 200,
    height: 30,
  },
  addSportButtonTextCentered: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
});
