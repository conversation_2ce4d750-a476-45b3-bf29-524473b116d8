import React, { useEffect, useState, useCallback, useMemo } from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Modal,
  TextInput,
  Button,
  Image,
  FlatList,
  ActivityIndicator,
  SafeAreaView,
  Alert,
  InteractionManager,
} from "react-native";
import SelectDropdown from 'react-native-select-dropdown';
import { useAuth } from "../../Context/AuthContext";
import { useFormik } from 'formik';
import * as Yup from 'yup';
import moment from "moment";
import { useNavigation, useIsFocused, useFocusEffect } from "@react-navigation/native";
import { Country, State, City } from 'country-state-city';
import { RadioButton } from "react-native-paper";
import CheckBox from '@react-native-community/checkbox';
import { Rating } from "react-native-ratings";
import Footer from "../../components/footer";
import { styles } from "./styles";
// import { styles } from "../styles";
import OTPModal from "../../components/OTPModal/OTPModal";
import CustomRadioButton from "../../components/CustomRadioButton";

const edit = require("../../assets/edit.png");
const close = require("../../assets/delete.png");
const location = require("../../assets/locatiothree.png");
const wallet = require("../../assets/coins-stack.png");

const FilterButton = ({ onPress, active, children, textStyle = {}, contentContainerStyle={} }) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      style={[{
        borderWidth: 1,
        borderColor: "darkgray",
        paddingVertical: 5,
        paddingHorizontal: 15,
        borderRadius: 5,
        backgroundColor: active ? "black" : "white",
        marginBottom: 10,
      }, contentContainerStyle]}
    >
      <Text style={[{ color: active ? "white" : "black", fontWeight: "600", }, textStyle]}>
        {children}
      </Text>
    </TouchableOpacity>
  );
};

const TransactionTable = ({ transaction }) => {
  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'green';
      case 'pending':
        return 'yellow';
      default:
        return 'red';
    }
  };
  
  return (
    <ScrollView>
      <View key={transaction?._id}>
        <View style={styles.transactionCard}>
          <View style={styles.transactionRow}>
            <Text style={styles.transactionLabel}>Course/Session:</Text>
            <Text style={styles.transactionValue}>{transaction?.courseId?.courseName}</Text>
          </View>
          <View style={styles.transactionRow}>
            <Text style={styles.transactionLabel}>Debit:</Text>
            <Text style={styles.transactionValue}>{transaction?.type === 'debit' ? transaction?.amount : 'N/A'}</Text>
          </View>
          <View style={styles.transactionRow}>
            <Text style={styles.transactionLabel}>Credit:</Text>
            <Text style={styles.transactionValue}>{transaction?.type === 'credit' ? transaction?.amount : 'N/A'}</Text>
          </View>
          <View style={styles.transactionRow}>
            <Text style={styles.transactionLabel}>Status:</Text>
            <View style={[styles.statusContainer, { borderColor: getStatusColor(transaction?.walletId?.status) }]}>
              <Text style={[styles.transactionValue, { color: getStatusColor(transaction?.walletId?.status) }]}>
                {transaction?.walletId?.status}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const PlayerProfile = () => {
  const { userId, userToken, playerData, fetchUserDetails } = useAuth();
  const [userDetails, setUserDetails] = useState({});
  const [isPersonalInfoModalVisible, setIsPersonalInfoModalVisible] = useState(false);
  const homeStateName = State.getStateByCodeAndCountry(playerData?.homeState || "", "IN")?.name || "";
  const [isSportsModalVisible, setIsSportsModalVisible] = useState(false);
  const [editedFirstName, setEditedFirstName] = useState("");
  const [editedLastName, setEditedLastName] = useState("");
  const [editedMobile, setEditedMobile] = useState("");
  const [editedSchoolName, setEditedSchoolName] = useState("");
  const [isStateDropDown, setIsStateDropDown] = useState(false);
  const [selectedState, setSelectedState] = useState("");
  const [states, setStates] = useState([]);
  const [sports, setSports] = useState([]);
  const [playerDataUser, setPlayerDataUser] = useState({});
  const [selectedSports, setSelectedSports] = useState([]);
  const [bookings, setBookings] = useState([]);
  const [hasMore, setHasMore] = useState(true);
  const [pageNumber, setPageNumber] = useState(1);
  const [appliedFilters, setAppliedFilters] = useState([]);
  const navigation = useNavigation();
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingBooking, setIsLoadingBooking] = useState(false);
  const [isLoadingTran, setIsLoadingTran] = useState(false);
  const [walletData, setWalletData] = useState(null);
  const [checked, setChecked] = useState("bookings");
  const [allTransaction, setAllTransactions] = useState([]);
  const [transactionMore, setTransactionMore] = useState(true);
  const [page, setPage] = useState(1);
  const [updateTrigger, setUpdateTrigger] = useState(false);

  useEffect(() => {
    const stateData = State.getStatesOfCountry("IN");
    setStates(stateData);
  }, []);

  const toggleStateDropdown = () => {
    setIsStateDropDown(!isStateDropDown);
  };

  const handleSelectState = (stateCode) => {
    setSelectedState(stateCode);
    toggleStateDropdown();
  };

  const BookingCard = ({ booking }) => {
    const [cancelModal, setCancelModal] = useState(false);
    const [checkedBox, setCheckedBox] = useState(false);
    const [cancelType, setCancelType] = useState('cancel');
    const [selectedDates, setSelectedDates] = useState([]);
    const [cancleLoad, setCancelLoad] = useState(false);
    const [bookingIds, setBookingIds] = useState(booking?._id);
    const [attendanceMarked, setAttendanceMarked] = useState(false);
    const [isOTPModalOpen, setIsOTPModalOpen] = useState(false);

    // Helper function to find the current active session
    const getCurrentActiveSession = useMemo(() => {
      if (!booking?.classes || booking.classes.length === 0) return null;

      const now = new Date();
      const currentDate = now.toISOString().split('T')[0];

      const todaysClasses = booking.classes.filter(classItem => {
        const classDate = new Date(classItem.date).toISOString().split('T')[0];
        return classDate === currentDate && classItem.status === 'upcoming';
      });

      if (todaysClasses.length === 0) return null;
      if (todaysClasses.length === 1) return todaysClasses[0];

      const activeSession = todaysClasses.find(classItem => {
        const [hours, minutes] = classItem.startTime.split(':').map(Number);
        const scheduledTime = new Date();
        scheduledTime.setHours(hours, minutes, 0, 0);

        const windowStart = new Date(scheduledTime.getTime() - 15 * 60 * 1000);
        const windowEnd = new Date(scheduledTime.getTime() + 15 * 60 * 1000);

        return now >= windowStart && now <= windowEnd;
      });

      return activeSession || todaysClasses[0];
    }, [booking]);

    // Check if attendance is marked for current session
    const isAttendanceMarked = useMemo(() => {
      const currentSession = getCurrentActiveSession;
      return currentSession?.attendance === 'present';
    }, [getCurrentActiveSession]);

    // Check if mark attendance should be enabled (more flexible for testing)
    const isMarkAttendanceEnabled = useMemo(() => {
      const currentSession = getCurrentActiveSession;
      if (!currentSession) return false;

      const now = new Date();
      const [hours, minutes] = currentSession.startTime.split(':').map(Number);
      const scheduledTime = new Date();
      scheduledTime.setHours(hours, minutes, 0, 0);

      // For testing: Allow attendance marking within 2 hours before and after
      const windowStart = new Date(scheduledTime.getTime() - 2 * 60 * 60 * 1000); // 2 hours before
      const windowEnd = new Date(scheduledTime.getTime() + 2 * 60 * 60 * 1000); // 2 hours after

      console.log('Attendance check:', {
        currentTime: now.toLocaleTimeString(),
        scheduledTime: scheduledTime.toLocaleTimeString(),
        windowStart: windowStart.toLocaleTimeString(),
        windowEnd: windowEnd.toLocaleTimeString(),
        isInWindow: now >= windowStart && now <= windowEnd
      });

      return now >= windowStart && now <= windowEnd;
    }, [getCurrentActiveSession]);

    // Check if cancel/reschedule should be enabled
    const isCancelRescheduleEnabled = useMemo(() => {
      if (!booking?.classes || booking.classes.length === 0) return false;
      if (isMarkAttendanceEnabled) return false;

      const now = new Date();
      const hasClassWithinOneHour = booking.classes.some(classItem => {
        if (classItem.status !== 'upcoming') return false;

        const classDateTime = new Date(classItem.date);
        const [hours, minutes] = classItem.startTime.split(':').map(Number);
        classDateTime.setHours(hours, minutes, 0, 0);

        const oneHourFromNow = new Date(now.getTime() + 60 * 60 * 1000);
        return classDateTime <= oneHourFromNow;
      });

      return !hasClassWithinOneHour;
    }, [booking, isMarkAttendanceEnabled]);

    const handleMarkAttendance = () => {
      if (isMarkAttendanceEnabled && !isAttendanceMarked && !attendanceMarked) {
        setIsOTPModalOpen(true);
      }
    };

    const handleOTPSuccess = () => {
      setAttendanceMarked(true);
      setIsOTPModalOpen(false);
    };

    useEffect(() => {
      setBookingIds(booking?._id);
    }, [booking]);

    const toggleCancleModal = () => {
      setCancelModal(!cancelModal);
    };

    const handleDatePress = (dateKey) => {
      handleCheckbox(dateKey);
    };

    const startDate = new Date(booking?.bookingDate).toLocaleDateString("en-IN", {
      day: "numeric",
      month: "long",
      year: "numeric",
    });
    
    const classTypeText = booking?.courseId?.classType === "class" 
      ? "Session" 
      : booking?.courseId?.classType;
    
    const statusText = booking?.status === "Active" 
      ? "Active" 
      : booking?.status === "Inactive" 
        ? "InActive" 
        : booking?.status;
    
    const statusColor = booking?.status === "Active" 
      ? "#4CAF50" 
      : booking?.status === "Inactive" 
        ? "red" 
        : "black";

    const handleCheckbox = (date) => {
      if (selectedDates.includes(date)) {
        setSelectedDates(selectedDates.filter(selectedDate => selectedDate !== date));
      } else {
        setSelectedDates([...selectedDates, date]);
      }
    };

    const handleCancellation = async () => {
      try {
        setCancelLoad(true);
        const dates = booking.classes.filter((x) => {
          const dateKey = `${x.startDate}-${x.startTime}-${x.endDate}-${x.endTime}`;
          return selectedDates.includes(dateKey);
        });

        const myHeaders = new Headers();
        myHeaders.append("Content-Type", "application/json");
        myHeaders.append("Authorization", `Bearer ${userToken}`);
        
        const raw = JSON.stringify({
          sender: "player",
          status: cancelType === "cancel" ? "cancelled" : "rescheduled",
          classes: dates
        });

        const requestOptions = {
          method: "POST",
          headers: myHeaders,
          body: raw,
          redirect: "follow"
        };
        
        const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/booking/cancel/${booking._id}`, requestOptions);
        const result = await response.json();
        
        if (result) {
          setCancelModal(false);
          setSelectedDates([]);
          setCancelLoad(false);
          
          if (cancelType === "cancel") {
            Alert.alert("Status", "Booking Cancelled Successfully", [
              { text: "OK", onPress: () => setUpdateTrigger(prev => !prev) }
            ]);
          } else {
            Alert.alert("Status", "To reschedule choose other class", [
              { 
                text: "OK", 
                onPress: () => {
                  setUpdateTrigger(prev => !prev);
                  navigation.navigate("Courses", { courseId: booking?.courseId?._id });
                }
              }
            ]);
          }
        } else {
          Alert.alert("Error", "Failed to cancel booking");
        }
      } catch (error) {
        console.error("Error cancelling booking:", error);
        Alert.alert("Error", "An error occurred while cancelling booking. Please try again");
        setCancelLoad(false);
      }
    };

    const ratingCompleted = async (newRating, bookingId) => {
      const requestOptions = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json', 
          Authorization: `Bearer ${userToken}`,
        },
        body: JSON.stringify({ stars: newRating }),
      };
      
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/course/ratings/${bookingId}`, requestOptions);
        const result = await response.json();
      } catch (error) {
        console.error("Error submitting rating:", error);
      }
    };

    return (
      <TouchableOpacity
        onPress={() => {
          navigation.navigate("BookingDetails", { bookingId: booking?._id });
        }}
      >
        <View style={styles.bookingCard}>
          <View style={styles.bookingCardSection}>
            <View style={styles.bookingCardSubSection}>
              <Image
                source={{ uri: booking?.courseId?.images[0]?.url }}
                style={styles.bookingImage}
              />
            </View>
            <View style={styles.bookingCardSubSectionTwo}>
              <View>
                <Text style={styles.bookingName}>
                  {booking?.courseId?.courseName}
                </Text>
              </View>
              {booking?.status === "Inactive" && (
                <View style={{ display: "flex", alignItems: "flex-start" }}>
                  <Rating
                    type='star'
                    ratingCount={5}
                    imageSize={20}
                    startingValue={booking?.stars ? booking?.stars : 0}
                    onFinishRating={(newRating) => ratingCompleted(newRating, booking?._id)}
                  />
                </View>
              )}
              <View>
                <Text style={styles.bookingAmount}>
                  Rs. {booking?.pricePaid}{' '}
                  <Text style={{ color: "grey" }}>(inclusive all taxes)</Text>
                </Text>
              </View>
              <View style={styles.bookingLocationRow}>
                <Image source={location} />
                <Text style={styles.bookingLocationText}>
                  {booking?.courseId?.facility?.addressLine1} , {booking?.courseId?.facility?.city}
                </Text>
              </View>
              <View style={[styles.status]}>
                <Text style={styles.bookingStatusType}>
                  {classTypeText}
                </Text>
                <Text style={styles.bookingStatusText}>
                  {statusText}
                </Text>
              </View>
            </View>
          </View>
          <View style={styles.bookingCardSubSectionThree}>
            {booking?.groupSize > 1 ? (
              <TouchableOpacity disabled style={styles.bookingActionBtnInactive}>
                <Text style={styles.bookingActionBtnText}>Not Available</Text>
              </TouchableOpacity>
            ) : booking?.status == "Inactive" ? (
              <View style={styles.bookingActionBtnInactive}>
                <Text style={styles.bookingActionBtnText}>InActive</Text>
              </View>
            ) : (
              <View style={styles.bookingActionContainer}>
                <TouchableOpacity 
                  onPress={handleMarkAttendance}
                  disabled={!isMarkAttendanceEnabled || isAttendanceMarked || attendanceMarked}
                  style={[
                    styles.attendanceBtn,
                    (isAttendanceMarked || attendanceMarked) && { backgroundColor: "#4CAF50" },
                    (!isMarkAttendanceEnabled && !isAttendanceMarked && !attendanceMarked) && { opacity: 0.5 }
                  ]}
                >
                  <Text style={styles.attendanceBtnText}>
                    {isAttendanceMarked || attendanceMarked ? "Attendance Marked" : "Mark Attendance"}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity 
                  onPress={toggleCancleModal}
                  disabled={!isCancelRescheduleEnabled}
                  style={[
                    styles.bookingActionBtn,
                    !isCancelRescheduleEnabled && { opacity: 0.5 }
                  ]}
                >
                  <Text style={styles.bookingActionBtnText}>Cancel / Reschedule</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
          <View style={styles.horizontalLine} />
        </View>
        
        <OTPModal
          isOpen={isOTPModalOpen}
          onClose={() => setIsOTPModalOpen(false)}
          booking={booking}
          classId={getCurrentActiveSession?._id}
          playerId={booking?.player}
          onSuccess={handleOTPSuccess}
        />
        
        <Modal visible={cancelModal} animationType="slide" transparent={true}>
          <View style={styles.modalOverlay}>
            <View style={styles.modalView}>
              <Text style={styles.modalTitle}>Cancellation & Refund</Text>
              <View style={styles.cancleRefund}>
                <View style={styles.modalRadioCol}>
                  <RadioButton
                    value="cancel"
                    status={cancelType === 'cancel' ? 'checked' : 'unchecked'}
                    onPress={() => setCancelType('cancel')}
                    color="#000"
                  />
                  <Text style={styles.modalRadioLabel}>Cancel</Text>
                </View>
                <View style={styles.modalRadioColLast}>
                  <RadioButton
                    value="reschedule"
                    status={cancelType === 'reschedule' ? 'checked' : 'unchecked'}
                    onPress={() => setCancelType('reschedule')}
                    color="#000"
                  />
                  <Text style={styles.modalRadioLabel}>Reschedule</Text>
                </View>
              </View>
              <ScrollView contentContainerStyle={styles.scrollView}>
                {booking?.classes?.map((bookingTime, index) => {
                  const date = new Date(bookingTime.date).toLocaleDateString('en-IN', { day: 'numeric', month: 'long', year: 'numeric' });
                  const dateKey = `${bookingTime.startDate}-${bookingTime.startTime}-${bookingTime.endDate}-${bookingTime.endTime}`;
                  const startDate = moment(bookingTime.startDate).format("Do MMMM YYYY");
                  const endDate = moment(bookingTime.endDate).format("Do MMMM YYYY");
                  const startTime = moment(bookingTime.startTime, "HH:mm").format("hh:mm A");
                  const endTime = moment(bookingTime.endTime, "HH:mm").format("hh:mm A");
                  
                  return (
                    <TouchableOpacity 
                      key={dateKey} 
                      onPress={() => handleDatePress(dateKey)} 
                      disabled={bookingTime?.status !== "upcoming"}
                      style={{ opacity: bookingTime?.status === "upcoming" ? 1 : 0.2 }}
                    >
                      <View style={styles.bookingTimeContainer}>
                        <View style={styles.modalBookingTimeRow}>
                          <CheckBox
                            disabled={bookingTime?.status != "upcoming"}
                            value={selectedDates.includes(dateKey)}
                            onValueChange={() => handleCheckbox(dateKey)}
                            style={{ marginRight: 10 }}
                            tintColors={{ true: 'black', false: 'gray' }}
                          />
                          <View style={{ width: "90%" }}>
                            <Text style={styles.modalBookingTimeText}>
                              {booking?.courseId?.classType === "class" ? date : date} 
                              ({startTime} - {endTime})
                              {' '} 
                              <Text style={bookingTime?.status === "upcoming" ? styles.modalBookingTimeStatus : styles.modalBookingTimeStatusInactive}>
                                ({bookingTime?.status})
                              </Text>
                            </Text>
                          </View>
                        </View>
                      </View>
                    </TouchableOpacity>
                  );
                })}
              </ScrollView>
              <View style={styles.cancleButton}>
                <TouchableOpacity onPress={toggleCancleModal} style={styles.modalCancelBtn}>
                  <Text style={{ color: "black" }}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={handleCancellation}
                  disabled={selectedDates.length === 0}
                  style={styles.modalProceedBtn}
                >
                  <View style={styles.bookingActionBtnProceed}>
                    {cancleLoad ? (
                      <ActivityIndicator size="small" color="#fff" />
                    ) : (
                      <Text style={styles.bookingActionBtnProceedText}>Proceed</Text>
                    )}
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </TouchableOpacity>
    );
  };

  useFocusEffect(
    React.useCallback(() => {
      if (!playerData?.mobile || !playerData?.homeState) {
        if (!playerData?.mobile) {
          Alert.alert("Warning", "Please add mobile number");
        }
        if (!playerData?.homeState) {
          Alert.alert("Warning", "Please add home state");
        }
      }
    }, [playerData])
  );

  const fetchTransactionData = async (page = 1) => {
    if (isLoadingTran) return;
    setIsLoadingTran(true);
    
    try {
      const response = await fetch(
        `${process.env.NEXT_WALLET_URL}/api/transactions?playerId=${userId}&page=${page}`,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${userToken}`,
          },
        }
      );
      
      const result = await response.json();
      
      if (result.data && result.data.length > 0) {
        setAllTransactions(prevTransactions => 
          page === 1 ? result.data : [...prevTransactions, ...result.data]
        );
        setTransactionMore(result.data.length >= 25);
      } else {
        setTransactionMore(false);
      }
    } catch (error) {
      console.error("Error fetching transactions:", error);
    } finally {
      setIsLoadingTran(false);
    }
  };

  const fetchDataPlayer = useCallback(async () => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/player/${userId}`,
        {
          headers: {
            Authorization: `Bearer ${userToken}`,
          },
        }
      );
      
      const result = await response.json();
      setUserDetails(result);
      setPlayerDataUser(result);
    } catch (error) {
      console.error("Error fetching player data:", error);
    } finally {
      setIsLoading(false);
    }
  }, [userId, userToken]);

  const fetchWalletData = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch(
        `${process.env.NEXT_WALLET_URL}/api/wallet?email=${playerData?.email}`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${userToken}`,
          },
        }
      );
      
      const result = await response.json();
      setWalletData(result.data?.[0] || null);
    } catch (error) {
      console.error("Error fetching wallet data:", error);
    } finally {
      setIsLoading(false);
    }
  }, [playerData, userToken]);

  const fetchDataPlayerBooking = useCallback(async (page) => {
    if (isLoadingBooking) return;
    setIsLoadingBooking(true);
    
    const filterParams = appliedFilters.length 
      ? `&${appliedFilters.join('&')}` 
      : '';
    
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/booking/?playerId=${userId}&page=${page}${filterParams}`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${userToken}`,
          },
        }
      );
      
      const result = await response.json();
      
      if (result?.data?.length > 0) {
        setBookings(prev => page === 1 ? result.data : [...prev, ...result.data]);
        setHasMore(result.data.length >= 25);
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error("Error fetching bookings:", error);
    } finally {
      setIsLoadingBooking(false);
    }
  }, [userToken, userId, appliedFilters]);

  const fetchSports = useCallback(async () => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/category`);
      const result = await response.json();
      setSports(result);
    } catch (error) {
      console.error("Error fetching sports:", error);
    }
  }, []);

  const handleEditPersonalInfoClick = () => {
    formik.resetForm({
      values: {
        firstName: userDetails.firstName || '',
        lastName: userDetails.lastName || '',
        mobile: userDetails.mobile || '',
        homeState: userDetails.homeState || '',
        schoolName: userDetails.schoolName || '',
      }
    });
    setIsPersonalInfoModalVisible(true);
  };

  const handleCloseModal = () => {
    setIsPersonalInfoModalVisible(false);
  };

  const phoneRegExp =
    /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/;
  const personalInfoSchema = Yup.object().shape({
    firstName: Yup.string()
      .trim()
      .min(3, 'First name must be at least 3 characters')
      .max(15, 'Cannot exceed 15 characters')
      .required('Please enter your first name'),
    lastName: Yup.string()
      .trim()
      .min(3, 'Last name must be at least 3 characters')
      .max(15, 'Cannot exceed 15 characters')
      .required('Please enter your last name'),
    mobile: Yup.string()
      .min(10, 'Phone number must be 10 characters long')
      .max(10, 'Phone number must be 10 characters long')
      .matches(phoneRegExp, 'Phone number is not valid')
      .required('Please enter your phone number'),
    homeState: Yup.string()
      .required('Home State is required')
      .max(100, 'Only 100 characters are allowed'),
    schoolName: Yup.string()
      .test('len', 'School name must be between 4 and 50 characters', value => {
        if (!value) return true; // optional
        return value.length >= 4 && value.length <= 50;
      }),
  });

  const handleSaveChangesPersonal = async () => {
    try {
      const updatedPlayerData = {
        firstName: editedFirstName,
        lastName: editedLastName,
        schoolName: editedSchoolName,
        homeState: selectedState,
      };

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/player/${userId}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${userToken}`,
          },
          body: JSON.stringify(updatedPlayerData),
        }
      );

      if (response.ok) {
        setIsPersonalInfoModalVisible(false);
        fetchDataPlayer();
        fetchUserDetails(userToken);
      } else {
        console.error("Error updating player data----------", response);
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };

  const handleEditSportsClick = () => {
    const hobbiesWithIds = userDetails?.hobbies?.map(hobby => ({ id: hobby.id?._id || hobby.id })) || [];
    setPlayerDataUser({ ...userDetails, hobbies: hobbiesWithIds });
    setIsSportsModalVisible(true);
  };

  const handleCloseSportsModal = () => {
    setIsSportsModalVisible(false);
  };

  const handleAddSport = () => {
    setPlayerDataUser(prev => ({
      ...prev,
      hobbies: [...(prev.hobbies || []), { id: "" }],
    }));
  };

  const handleSportSelection = (selectedSportId, index) => {
    setPlayerDataUser(prev => {
      const newHobbies = [...prev.hobbies];
      newHobbies[index] = { id: selectedSportId };
      return { ...prev, hobbies: newHobbies };
    });
  };

  const handleDeleteHobby = (index) => {
    setPlayerDataUser(prev => ({
      ...prev,
      hobbies: prev.hobbies.filter((_, i) => i !== index),
    }));
  };

  const getSportName = (id) => {
    if (!id) return "Select a sport";
    const sport = sports?.data?.find(s => s._id === id);
    return sport ? sport.name : "Select a sport";
  };

  const handleSaveChangesSports = async () => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/player/${userId}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${userToken}`,
          },
          body: JSON.stringify({ hobbies: playerDataUser.hobbies }),
        }
      );

      if (response.ok) {
        setIsSportsModalVisible(false);
        fetchDataPlayer();
      } else {
        console.error("Error updating sports");
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };

  // Defer initial heavy data fetching until after navigation animation
  useEffect(() => {
    const task = InteractionManager.runAfterInteractions(() => {
      fetchDataPlayer();
      if (playerData?.email) fetchWalletData();
      fetchDataPlayerBooking(1);
      fetchSports();
      if (playerData) {
        setPage(1);
        setAllTransactions([]);
        fetchTransactionData(1);
      }
    });
    return () => task.cancel();
  }, []);

  useEffect(() => {
    if (pageNumber > 1) {
      fetchDataPlayerBooking(pageNumber);
    }
  }, [pageNumber]);

  const loadMoreBookings = () => {
    if (hasMore && !isLoadingBooking) {
      setPageNumber(prev => prev + 1);
    }
  };

  const handleEndReached = () => {
    if (!isLoadingTran && transactionMore) {
      setPage(prevPage => prevPage + 1);
    }
  };

  const handleFilterChange = (filterParam) => {
    setAppliedFilters(prev => 
      prev.includes(filterParam) 
        ? prev.filter(f => f !== filterParam) 
        : [...prev, filterParam]
    );
  };

  // Replace local state for modal fields with Formik
  const formik = useFormik({
    initialValues: {
      firstName: userDetails.firstName || '',
      lastName: userDetails.lastName || '',
      mobile: userDetails.mobile || '',
      homeState: userDetails.homeState || '',
      schoolName: userDetails.schoolName || '', // not validated
    },
    enableReinitialize: true,
    validationSchema: personalInfoSchema,
    onSubmit: async (values) => {
      try {
        const updatedPlayerData = {
          firstName: values.firstName,
          lastName: values.lastName,
          mobile: values.mobile,
          schoolName: values.schoolName,
          homeState: values.homeState,
        };
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/player/${userId}`,
          {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${userToken}`,
            },
            body: JSON.stringify(updatedPlayerData),
          }
        );
        if (response.ok) {
          setIsPersonalInfoModalVisible(false);
          fetchDataPlayer();
          fetchUserDetails(userToken);
        } else {
          console.error('Error updating player data');
        }
      } catch (error) {
        console.error('Error:', error);
      }
    },
  });

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ScrollView>
        <View style={{ paddingHorizontal: 0, paddingVertical: 10 }}>
          <View style={styles.card}>
            {isLoading ? (
              <View style={styles.loaderContainer}>
                <ActivityIndicator size="large" color="#0000ff" />
              </View>
            ) : (
              <View>
                <View style={styles.personalInfoHeader}>
                  <Text style={styles.cardHeading}>Personal Information</Text>
                  <TouchableOpacity onPress={handleEditPersonalInfoClick} style={styles.editButton}>
                    <Image source={edit} style={{ width: 20, height: 20 }} />
                  </TouchableOpacity>
                </View>
                <View style={styles.userInfo}>
                  <Text style={styles.userInfoLabel}>First Name:</Text>
                  <Text style={styles.userInfoText}>{userDetails.firstName}</Text>
                </View>
                <View style={styles.userInfo}>
                  <Text style={styles.userInfoLabel}>Last Name:</Text>
                  <Text style={styles.userInfoText}>{userDetails.lastName}</Text>
                </View>
                <View style={styles.userInfo}>
                  <Text style={styles.userInfoLabel}>Phone No:</Text>
                  <Text style={styles.userInfoText}>{userDetails.mobile}</Text>
                </View>
                <View style={styles.userInfo}>
                  <Text style={styles.userInfoLabel}>Email:</Text>
                  <Text style={styles.userInfoText}>{userDetails.email}</Text>
                </View>
                <View style={styles.userInfo}>
                  <Text style={styles.userInfoLabel}>School Name:</Text>
                  <Text style={styles.userInfoText}>{userDetails.schoolName}</Text>
                </View>
                <View style={styles.userInfo}>
                  <Text style={styles.userInfoLabel}>Home State:</Text>
                  <Text style={styles.userInfoText}>{homeStateName}</Text>
                </View>
              </View>
            )}
          </View>

          <Modal
            visible={isPersonalInfoModalVisible}
            animationType="slide"
            onRequestClose={() => setIsPersonalInfoModalVisible(false)}
          >
            <SafeAreaView>
            <View style={styles.modalContainer}>
              <View style={styles.modalHeaderRow}>
                <Text style={styles.editCardHeading}>Edit Personal Information</Text>
              </View>
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>First Name:</Text>
                <TextInput
                  style={styles.input}
                  value={formik.values.firstName}
                  onChangeText={formik.handleChange('firstName')}
                  onBlur={formik.handleBlur('firstName')}
                  placeholder="Enter First Name"
                />
              </View>
              {formik.touched.firstName && formik.errors.firstName && (
                <Text style={styles.modalErrorText}>{formik.errors.firstName}</Text>
              )}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Last Name:</Text>
                <TextInput
                  style={styles.input}
                  value={formik.values.lastName}
                  onChangeText={formik.handleChange('lastName')}
                  onBlur={formik.handleBlur('lastName')}
                  placeholder="Enter Last Name"
                />
              </View>
              {formik.touched.lastName && formik.errors.lastName && (
                <Text style={styles.modalErrorText}>{formik.errors.lastName}</Text>
              )}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Phone No:</Text>
                <TextInput
                  editable={false}
                  style={styles.input}
                  value={formik.values.mobile}
                  onChangeText={formik.handleChange('mobile')}
                  onBlur={formik.handleBlur('mobile')}
                  placeholder="Enter Phone No"
                  keyboardType="numeric"
                />
              </View>
              {formik.touched.mobile && formik.errors.mobile && (
                <Text style={styles.modalErrorText}>{formik.errors.mobile}</Text>
              )}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>School Name:</Text>
                <TextInput
                  style={styles.input}
                  value={formik.values.schoolName}
                  onChangeText={formik.handleChange('schoolName')}
                  placeholder="Enter School Name"
                />
              </View>
              {formik.touched.schoolName && formik.errors.schoolName && (
                <Text style={styles.modalErrorText}>{formik.errors.schoolName}</Text>
              )}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Email:</Text>
                <TextInput
                  style={styles.input}
                  value={userDetails.email}
                  editable={false}
                />
              </View>
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Home State:</Text>
                <TouchableOpacity
                  style={[styles.input, { justifyContent: 'center' }]}
                  onPress={toggleStateDropdown}
                >
                  <Text style={{ color: '#000' }}>
                    {formik.values.homeState
                      ? states.find(state => state.isoCode === formik.values.homeState)?.name
                      : homeStateName || "Select State"}
                  </Text>
                </TouchableOpacity>
                <Modal visible={isStateDropDown} animationType="slide" transparent={true}>
                  <View style={styles.inputContainerHobbies}>
                    <View style={styles.stateModalContainer}>
                      <ScrollView contentContainerStyle={styles.scrollView}>
                        {states.map(state => (
                          <TouchableOpacity
                            key={state.isoCode}
                            style={styles.option}
                            onPress={() => {
                              formik.setFieldValue('homeState', state.isoCode);
                              setSelectedState(state.isoCode);
                              toggleStateDropdown();
                            }}
                          >
                            <Text style={styles.optionText}>
                              {state.name} {formik.values.homeState === state.isoCode ? "✓" : ""}
                            </Text>
                          </TouchableOpacity>
                        ))}
                      </ScrollView>
                      <TouchableOpacity onPress={toggleStateDropdown} style={styles.closeButton}>
                        <Text style={styles.modalCloseButton}>Close</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </Modal>
              </View>
              {formik.touched.homeState && formik.errors.homeState && (
                <Text style={styles.modalErrorText}>{formik.errors.homeState}</Text>
              )}
              <View style={styles.buttonContainer}>
                <Button color="#000" title="Save Changes" onPress={formik.handleSubmit} disabled={!formik.isValid || formik.isSubmitting} />
                <Button title="Close" color="#000" onPress={handleCloseModal} />
              </View>
            </View>
            </SafeAreaView>
          </Modal>

          <View style={styles.card}>
            {isLoading ? (
              <View style={styles.loaderContainer}>
                <ActivityIndicator size="large" color="#0000ff" />
              </View>
            ) : (
              <View>
                <View style={styles.personalInfoHeader}>
                  <Text style={styles.cardHeading}>Sports you like most</Text>
                  <TouchableOpacity onPress={handleEditSportsClick} style={styles.editButton}>
                    <Image source={edit} style={{ width: 20, height: 20 }} />
                  </TouchableOpacity>
                </View>
                <View style={styles.sportContainer}>
                  {userDetails?.hobbies?.map((hobby, index) => (
                    <View key={index} style={styles.sportItem}>
                      <Image
                        style={styles.sportIcon}
                        source={{ uri: hobby?.id?.image }}
                      />
                      <Text style={{ color: "#000", fontSize: 16 }}>{hobby?.id?.name}</Text>
                    </View>
                  ))}
                </View>
              </View>
            )}
          </View>

          <Modal visible={isSportsModalVisible} animationType="slide">
            <SafeAreaView style={{flex: 1}}>
              <ScrollView contentContainerStyle={styles.scrollView}>
                <View style={styles.modalContainer}>
                  <View style={styles.modalHeaderRow}>
                    <Text style={styles.editCardHeading}>Edit Sports</Text>
                  </View>
                  {playerDataUser?.hobbies?.map((hobby, index) => (
                    <View key={index} style={styles.modalPickerRow}>
                      <Text style={styles.modalPickerLabel}>Sport {index + 1} : </Text>
                      <SelectDropdown
                        data={sports?.data?.filter(sport => {
                          const isSelected = playerDataUser.hobbies.some(h => h.id === sport._id);
                          return !isSelected || sport._id === hobby.id;
                        }) || []}
                        onSelect={(selectedItem) => {
                          handleSportSelection(selectedItem._id, index);
                        }}
                        renderButton={(selectedItem) => {
                          return (
                            <View style={[styles.modalPicker, {
                              borderWidth: 1,
                              borderColor: '#ccc',
                              borderRadius: 5,
                              paddingHorizontal: 10,
                              justifyContent: 'center',
                              backgroundColor: '#fff'
                            }]}>
                              <Text style={{ color: '#000', fontSize: 16 }}>
                                {selectedItem ? selectedItem.name : getSportName(hobby?.id)}
                              </Text>
                            </View>
                          );
                        }}
                        renderItem={(item, _, isSelected) => {
                          return (
                            <View style={{
                              padding: 10,
                              backgroundColor: isSelected ? '#f0f0f0' : '#fff',
                              borderBottomWidth: 1,
                              borderBottomColor: '#eee'
                            }}>
                              <Text style={{
                                color: isSelected ? '#000' : '#333',
                                fontSize: 16,
                                fontWeight: isSelected ? 'bold' : 'normal'
                              }}>
                                {item.name}
                              </Text>
                            </View>
                          );
                        }}
                        showsVerticalScrollIndicator={false}
                        dropdownStyle={{
                          backgroundColor: '#fff',
                          borderRadius: 5,
                          borderWidth: 1,
                          borderColor: '#ccc',
                          maxHeight: 200
                        }}
                      />
                      <TouchableOpacity onPress={() => handleDeleteHobby(index)}>
                        <Image source={close} style={styles.modalPickerDelete} />
                      </TouchableOpacity>
                    </View>
                  ))}
                  {playerDataUser?.hobbies?.length < 5 && (
                    <TouchableOpacity
                      style={styles.addSportButtonCentered}
                      onPress={handleAddSport}
                    >
                      <Text style={styles.addSportButtonTextCentered}>+ Add Another Sport</Text>
                    </TouchableOpacity>
                  )}
                  <View style={styles.buttonContainer}>
                    <Button color="#000" title="Save Changes" onPress={handleSaveChangesSports} />
                    <Button title="Close" color="#000" onPress={handleCloseSportsModal} />
                  </View>
                </View>
              </ScrollView>
            </SafeAreaView>
          </Modal>
        </View>
        
        <View style={styles.card}>
          <View style={styles.bookingAlign}>
            <View style={{ flexDirection: "row", justifyContent: "space-between", width: "100%"}}>
              <View style={{flex: 1, flexDirection: "row", alignItems: "center"}}>
                <CustomRadioButton
                  value="bookings"
                  status={checked === 'bookings'}
                  onPress={() => setChecked("bookings")}
                  label="Bookings"
                />
              </View>
              <View style={{flex: 1, flexDirection: "row", alignItems: "center", justifyContent: "flex-end"}} >
                <CustomRadioButton
                  value="transactions"
                  status={checked === 'transactions'}
                  onPress={() => setChecked("transactions")}
                  label="Transactions"
                />
              </View>
            </View>
            {walletData && (
              <View style={styles.walletRow}>
                <Text style={styles.walletLabel}>Wallet Balance:</Text>
                <Image source={wallet} style={styles.walletIcon} />
                <Text style={styles.walletCoin}>₹{walletData?.balance?.toFixed(2)}</Text>
              </View>
            )}
          </View>
          
          {checked === "bookings" && (
            <View style={styles.filterContainer}>
              <View style={styles.filterRow}>
                <FilterButton
                  onPress={() => handleFilterChange("courseType=course")}
                  active={appliedFilters.includes("courseType=course")}
                >
                  Course
                </FilterButton>
                <FilterButton
                  onPress={() => handleFilterChange("courseType=class")}
                  active={appliedFilters.includes("courseType=class")}
                >
                  Session
                </FilterButton>
                <FilterButton
                  onPress={() => handleFilterChange("status=Active")}
                  active={appliedFilters.includes("status=Active")}
                >
                  Active
                </FilterButton>
              </View>
              <View style={styles.filterRow}>
                <FilterButton
                  onPress={() => handleFilterChange("status=Inactive")}
                  active={appliedFilters.includes("status=Inactive")}
                >
                  InActive
                </FilterButton>
              </View>
            </View>
          )}

          <ScrollView>
            {isLoadingBooking && pageNumber === 1 ? (
              <View style={styles.loaderContainer}>
                <ActivityIndicator size="large" color="#0000ff" />
              </View>
            ) : checked === "bookings" ? (
              bookings.length > 0 ? (
                <FlatList
                  data={bookings}
                  renderItem={({ item }) => <BookingCard booking={item} />}
                  keyExtractor={item => item._id}
                  onEndReached={loadMoreBookings}
                  onEndReachedThreshold={0.5}
                  ListFooterComponent={
                    isLoadingBooking && hasMore ? (
                      <ActivityIndicator size="large" color="#0000ff" />
                    ) : !hasMore ? (
                      <Text style={{ textAlign: 'center', marginVertical: 20, color: "#000" }}>
                        No more bookings available.
                      </Text>
                    ) : null
                  }
                />
              ) : (
                <Text style={{ color: "#000", fontFamily: "Lato-Bold", fontSize: 16, textAlign: 'center' }}>
                  No bookings available.
                </Text>
              )
            ) : isLoadingTran && page === 1 ? (
              <View style={styles.loaderContainer}>
                <ActivityIndicator size="large" color="#0000ff" />
              </View>
            ) : allTransaction.length > 0 ? (
              <FlatList
                data={allTransaction}
                renderItem={({ item }) => <TransactionTable transaction={item} />}
                keyExtractor={(item, index) => index.toString()}
                onEndReached={handleEndReached}
                onEndReachedThreshold={0.5}
                ListFooterComponent={() => (
                  isLoadingTran ? <ActivityIndicator /> : 
                  !transactionMore && (
                    <Text style={{ textAlign: 'center', marginVertical: 20, color: "#000" }}>
                      No more transactions available
                    </Text>
                  )
                )}
              />
            ) : (
              <Text style={{ color: "#000", fontFamily: "Lato-Bold", fontSize: 16, textAlign: 'center' }}>
                No transactions available.
              </Text>
            )}
          </ScrollView>
        </View>
        <Footer/>
      </ScrollView>
    </SafeAreaView>
  );
};

export default PlayerProfile;
