import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Image from '@d11/react-native-fast-image';
import { useNavigation } from '@react-navigation/native';
import styles from './styles';
const redirect = require('../../assets/redirect.png');

const CourseCard = ({ course }) => {
  const navigation = useNavigation();
  const renderDescription = (description) => {
    const maxChars = 150;
    let truncatedDescription = description.replace(/<[^>]+>/g, '');
    truncatedDescription = truncatedDescription.substring(0, maxChars);
    if (description.length > maxChars) {
      truncatedDescription += '...';
    }
    return <Text style={styles.description}>{truncatedDescription}</Text>;
  };
  const renderTitle = (title) => {
    const maxChar = 20;
    let truncatedTitle = title.substring(0, maxChar);
    if (title.length > maxChar) {
      truncatedTitle += '...';
    }
    return <Text style={styles.courseName}>{truncatedTitle}</Text>;
  };
  return (
    <TouchableOpacity style={styles.courseItem} onPress={() => navigation.navigate('Courses', { courseId: course._id })}>
      <Image
        source={course.images && course.images.length > 0 ? { uri: course.images[0].url } : require('../../assets/placeholder.png')}
        style={styles.image}
      />
      <View style={styles.courseDetails}>
        <View style={styles.priceContainer}>
          <Text style={styles.price}>
            {course.fees?.feesCourse ? `₹ ${Math.round(course.fees?.feesCourse * 1.10)}` : 'Explore'}
          </Text>
        </View>
        {renderTitle(course.courseName)}
        <View style={{ minHeight: 40 }}>{renderDescription(course.description)}</View>
        <View style={styles.learnMore}>
          <Text style={styles.learnMoreText}>What will you learn</Text>
          <Image source={redirect} style={{ width: 15, height: 15 }} />
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default CourseCard; 