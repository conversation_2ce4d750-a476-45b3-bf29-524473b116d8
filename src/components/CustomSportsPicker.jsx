import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleSheet,
  TextInput,
} from 'react-native';

const CustomSportsPicker = ({
  data = [],
  selectedValue,
  onSelect,
  placeholder = "Select a sport",
  style = {},
  disabled = false,
  searchable = true,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const selectedItem = data.find(item => item._id === selectedValue);
  const displayText = selectedItem ? selectedItem.name : placeholder;

  const filteredData = useMemo(() => {
    if (!searchQuery.trim()) return data;
    return data.filter(item =>
      item.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [data, searchQuery]);

  const handleSelect = (item) => {
    onSelect(item);
    setSearchQuery('');
    setIsVisible(false);
  };

  const handleClose = () => {
    setSearchQuery('');
    setIsVisible(false);
  };

  const renderItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.optionItem,
        selectedValue === item._id && styles.selectedOptionItem
      ]}
      onPress={() => handleSelect(item)}
    >
      <Text style={[
        styles.optionText,
        selectedValue === item._id && styles.selectedOptionText
      ]}>
        {item.name}
      </Text>
    </TouchableOpacity>
  );

  return (
    <>
      <TouchableOpacity
        style={[styles.pickerButton, style, disabled && styles.disabledButton]}
        onPress={() => !disabled && setIsVisible(true)}
        disabled={disabled}
      >
        <Text style={[
          styles.pickerButtonText,
          !selectedItem && styles.placeholderText,
          disabled && styles.disabledText
        ]}>
          {displayText}
        </Text>
        <Text style={styles.dropdownIcon}>▼</Text>
      </TouchableOpacity>

      <Modal
        visible={isVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setIsVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={handleClose}
        >
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={handleClose}
              >
                <Text style={styles.closeButtonText}>✕</Text>
              </TouchableOpacity>
            </View>

            {searchable && (
              <View style={styles.searchContainer}>
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search sports..."
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  placeholderTextColor="#999"
                />
              </View>
            )}

            <FlatList
              data={filteredData}
              renderItem={renderItem}
              keyExtractor={(item) => item._id}
              style={styles.optionsList}
              showsVerticalScrollIndicator={false}
              ItemSeparatorComponent={() => <View style={styles.separator} />}
              ListEmptyComponent={() => (
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>No sports found</Text>
                </View>
              )}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  pickerButton: {
    height: 40,
    width: 200,
    borderWidth: 1.5,
    borderColor: 'grey',
    borderRadius: 5,
    paddingHorizontal: 10,
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  disabledButton: {
    backgroundColor: '#f5f5f5',
    borderColor: '#ddd',
  },
  pickerButtonText: {
    color: '#000',
    fontSize: 16,
    flex: 1,
  },
  placeholderText: {
    color: '#999',
  },
  disabledText: {
    color: '#999',
  },
  dropdownIcon: {
    color: '#666',
    fontSize: 12,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#FAFBFCFF',
    borderRadius: 10,
    width: '85%',
    maxHeight: '60%',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: '#DFDFDF',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    padding: 10,
    paddingBottom: 0,
  },
  closeButton: {
    padding: 8,
    borderRadius: 15,
    backgroundColor: '#f0f0f0',
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: '#666',
    fontWeight: 'bold',
  },
  optionsList: {
    maxHeight: 300,
    borderRadius: 6
  },
  optionItem: {
    padding: 15,
    backgroundColor: '#fff',
  },
  selectedOptionItem: {
    backgroundColor: '#FAFBFCFF',
  },
  optionText: {
    fontSize: 16,
    color: '#000',
    fontWeight: '400',
  },
  selectedOptionText: {
    fontWeight: '500',
    color: '#000',
  },
  separator: {
    height: 1,
    backgroundColor: '#DFDFDF',
  },
  searchContainer: {
    paddingHorizontal: 15,
    paddingTop: 5,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#DFDFDF',
  },
  searchInput: {
    height: 40,
    borderWidth: 1,
    borderColor: '#DFDFDF',
    borderRadius: 5,
    paddingHorizontal: 10,
    backgroundColor: '#fff',
    fontSize: 16,
    color: '#000',
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    fontStyle: 'italic',
  },
});

export default CustomSportsPicker;
