import React from 'react';
import { View, Text, Image, StyleSheet, TouchableOpacity, Linking } from 'react-native';
import { s } from 'react-native-size-matters';

interface FacilityCardProps {
  name: string;
  addressLine1: string;
  addressLine2: string;
  latitude?: number;
  longitude?: number;
}

const FacilityCard: React.FC<FacilityCardProps> = ({ name, addressLine1, addressLine2, latitude, longitude }) => {
  const handlePress = () => {
    if (latitude && longitude) {
      const url = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
      Linking.openURL(url);
    }
  };

  return (
    <TouchableOpacity activeOpacity={0.8} onPress={handlePress} disabled={!(latitude && longitude)}>
      <View style={styles.card}>
        <View style={styles.left}>
          <Image
            source={require('../../assets/pin-marker.png')}
            style={styles.pin}
            resizeMode="contain"
          />
        </View>
        <View style={styles.right}>
          <Text style={styles.text} numberOfLines={3}>
            {name}
            {addressLine1 ? `\n${addressLine1}` : ''}
            {addressLine2 ? `\n${addressLine2}` : ''}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    width: s(200),
    height: s(75),
    flexDirection: 'row',
    alignItems: 'center',
    gap: s(6), // reduced gap
    overflow: 'hidden',
  },
  left: {
    width: '20%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  right: {
    width: '80%',
    justifyContent: 'center',
  },
  pin: {
    width: '90%',
    height: '90%',
  },
  text: {
    color: '#2B2B2A99',
    fontSize: s(12),
    fontWeight: '500',
    textAlign: 'justify',
    lineHeight: s(15),
  },
});

export default FacilityCard; 