import { ScaledSheet } from 'react-native-size-matters';

export const styles = ScaledSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    minHeight: '100%',
  },
  locationAndSearchContainer: {
    paddingHorizontal: "5%",
    paddingVertical: "2%",
    backgroundColor: "#0EA5E9",
    gap: 20,
  },
  googlePlacesWrapper: {
    position: 'relative',
    zIndex: 1000,
  },
  searchInputContainer: {
    flexDirection: 'row',
    borderColor: '#ccc',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderRadius: 5,
    color: '#000',
    position: 'relative',
    zIndex: 1,
  },
  searchInput: {
    flex: 1,
    padding: 10,
    color: '#000',
    fontSize: 16,
    fontFamily: 'Lato-Regular'
  },
  searchButton: {
    backgroundColor: "#000",
    color: "white",
    display: "flex",
    flexDirection: "row",
    borderRadius: 5,
    padding: 12,
    justifyContent: "center",
    marginTop: "3%",
    alignItems: "center"
  },
  searchButtonIcon: {
    width: 20,
    height: 20,
  },
  searchButtonText: {
    color: "#fff",
    fontSize: 13,
    marginLeft: 5,
    fontFamily: 'Lato-Regular'
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  flatListContent: {
    paddingBottom: 20,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    fontFamily: 'Lato-Regular',
  },
  sectionHeader: {
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    fontFamily: 'Lato-Bold',
  },
  notFoundContainer: {
    paddingHorizontal: 16,
    paddingVertical: 20,
    alignItems: 'center',
  },
  notFoundText: {
    fontSize: 14,
    color: '#999',
    fontFamily: 'Lato-Regular',
    fontStyle: 'italic',
  },
  searchPromptContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 60,
  },
  searchPromptText: {
    fontSize: 18,
    color: '#666',
    fontFamily: 'Lato-Regular',
    textAlign: 'center',
    lineHeight: 24,
  },
  searchInstructionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 40,
  },
  searchInstructionText: {
    fontSize: 16,
    color: '#888',
    fontFamily: 'Lato-Regular',
    textAlign: 'center',
  },
  noResultsMainContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 60,
  },
  noResultsMainText: {
    fontSize: 18,
    color: '#333',
    fontFamily: 'Lato-Bold',
    textAlign: 'center',
    marginBottom: 8,
    lineHeight: 24,
  },
  noResultsSubText: {
    fontSize: '14@ms',
    color: '#666',
    fontFamily: 'Lato-Regular',
    textAlign: 'center',
  },
  loadingInlineContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: "32@s",
    paddingVertical: "40@vs",
  },
  loadingInlineText: {
    fontSize: "16@mvs",
    color: '#0EA5E9',
    fontFamily: 'Lato-Regular',
    textAlign: 'center',
  },
  toggleHeaderContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingVertical: '12@vs',
    paddingHorizontal: '5@ms',
    gap: '5@ms',
  },
  toggleButton: {
    flex: 1,
    height: '35@vs',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  toggleButtonActive: {
    backgroundColor: '#00000073',
    borderRadius: '4@s',
  },
  toggleButtonText: {
    color: '#00000073',
    fontSize: '16@ms',
    fontFamily: 'Lato-Bold',
  },
  toggleButtonTextActive: {
    color: '#fff',
  },
});

export const googlePlacesStyles = {
  container: {
    flex: 0,
    position: 'relative',
    zIndex: 1000,
  },
  textInputContainer: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
    borderTopWidth: 0,
    borderRadius: 5,
  },
  textInput: {
    marginLeft: 0,
    marginRight: 0,
    height: 48,
    color: '#000',
    borderColor: '#ccc',
    fontSize: 16,
    borderRadius: 5,
    borderWidth: 1,
    padding: 10,
    fontFamily: 'Lato-Regular'
  },
  listView: {
    maxHeight: 250,
    zIndex: 1001,
    elevation: 10,
    backgroundColor: '#fff',
  },
  row: {
    backgroundColor: '#fff',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  description: {
    color: '#000',
    fontSize: 16,
    fontFamily: 'Lato-Regular',
  },
  separator: {
    height: 1,
    backgroundColor: '#eee',
  },
};