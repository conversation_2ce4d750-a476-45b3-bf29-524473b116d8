import React, { useRef, useState, useEffect } from "react";
import {
  View,
  TextInput,
  TouchableOpacity,
  Text,
  FlatList,
} from "react-native";
import { GOOGLE_PLACES_API_KEY } from "@env";
import { GooglePlacesAutocomplete } from "react-native-google-places-autocomplete";
import {
  fetchSearchSuggestions,
  debounce,
  SearchResult,
} from "../../helpers/searchHelper";
import SearchResultCard from "../SearchResultCard/SearchResultCard";
import { extractCityFromPlaceDetails } from "../../helpers/locationHelper";
import { googlePlacesStyles, styles } from "./styles";
import { useNavigation } from "@react-navigation/native";
import axios from 'axios';
import Image from '@d11/react-native-fast-image';

interface SearchUpdatedProps {
  initialLocationObj?: {
    lat: number | null;
    lan: number | null;
  };
  initialCityName?: string;
  onCoursePress?: (course: any) => void;
  onCoachPress?: (coach: any) => void;
  showLocationSelector?: boolean;
}

// Tab configuration and static data outside the component
const TABS = [
  { key: 'courses', label: 'Courses', emptyMsg: 'No courses found' },
  { key: 'coaches', label: 'Coaches', emptyMsg: 'No coaches found' },
  { key: 'academies', label: 'Academy', emptyMsg: 'No academies found' },
];

function handleCardPress(type: string, data: any, navigation: any, image?: any) {
  if (type === 'course') {
    navigation.navigate('Courses', { courseId: data._id });
  } else if (type === 'coach') {
    navigation.navigate('CoachProfile', { ID: data.coach_id });
  } else if (type === 'academy') {
    // Use the passed image URL string if available, otherwise extract from data
    let imageUri = null;
    if (typeof image === 'string' && image) {
      imageUri = image;
    } else if (data?.profileImg) {
      imageUri = data.profileImg;
    } else if (data?.profileImage) {
      imageUri = data.profileImage;
    } else if (data?.image?.uri) {
      imageUri = data.image.uri;
    } else if (data?.academyImages && data.academyImages.length > 0) {
      imageUri = data.academyImages[0];
    }
    // Pass null if no image found, otherwise pass the URI string
    const profileImg = imageUri || null;
    console.log("Profile Image: -------------->\n\n\n\n\n", profileImg);
    navigation.navigate('AcademyProfile', {
      id: data.academy_id || data._id,
      academyImages: data.academyImages || [],
      title: data.academyName,
      profileImg
    });
  }
}

function getResultsToShow({ activeTab, searchQueryValue, isSearching, searchResults, coachImages }: {
  activeTab: 'courses' | 'coaches' | 'academies',
  searchQueryValue: string,
  isSearching: boolean,
  searchResults: any,
  coachImages: { [coachId: string]: string },
}) {
  if (activeTab === 'academies') {
    if (!searchQueryValue || searchQueryValue.trim() === '') {
      return [{ type: 'search-prompt', id: 'search-prompt', message: 'Start typing to search for courses, coaches, and academies' }];
    }
    if (Array.isArray(searchResults.academies) && searchResults.academies.length > 0) {
      return searchResults.academies.map((a: any) => {
        // Extract image URL from various possible sources
        let imageUri = null;
        if (a.profileImage) {
          imageUri = a.profileImage;
        } else if (a.profileImg) {
          imageUri = a.profileImg;
        } else if (a.academyImages && a.academyImages.length > 0) {
          imageUri = a.academyImages[0];
        } else if (a.data && a.data.profileImage) {
          imageUri = a.data.profileImage;
        } else if (a.data && a.data.profileImg) {
          imageUri = a.data.profileImg;
        } else if (a.data && a.data.academyImages && a.data.academyImages.length > 0) {
          imageUri = a.data.academyImages[0];
        }
        
        // Debug logging for academy data structure
        console.log("Academy data structure:", {
          academyId: a.academy_id || a._id,
          academyName: a.academyName,
          profileImage: a.profileImage,
          profileImg: a.profileImg,
          academyImages: a.academyImages,
          extractedImageUri: imageUri
        });
        
        return {
          type: 'academy',
          id: a.academy_id || a._id,
          image: imageUri ? { uri: imageUri } : undefined,
          title: a.academyName,
          academyImages: a.academyImages,
          profileImg: a.profileImg || a.profileImage,
          data: a,
        };
      });
    }
    return [{ type: 'no-results', id: 'no-results-academies', message: 'No academies found' }];
  }
  if (!searchQueryValue || searchQueryValue.trim() === '') {
    return [{ type: 'search-prompt', id: 'search-prompt', message: 'Start typing to search for courses, coaches, and academies' }];
  }
  if (searchQueryValue.trim().length < 3) {
    return [{ type: 'search-instruction', id: 'search-instruction', message: 'Type at least 3 characters to search' }];
  }
  if (isSearching) {
    return [{ type: 'loading', id: 'loading', message: 'Searching...' }];
  }
  const { courses, uniqueCoaches } = searchResults;
  if (activeTab === 'courses') {
    if (Array.isArray(courses) && courses.length > 0) {
      return courses.map((course: any) => ({
        type: 'course',
        id: `course-${course._id}`,
        image: course.images && course.images[0]?.url ? { uri: course.images[0].url } : undefined,
        title: course.courseName,
        price: getDisplayFee(course),
        data: course,
      }));
    }
    return [{ type: 'no-results', id: 'no-results-courses', message: 'No courses found' }];
  }
  if (activeTab === 'coaches') {
    if (Array.isArray(uniqueCoaches) && uniqueCoaches.length > 0) {
      return uniqueCoaches.map((coach: any) => ({
        type: 'coach',
        id: `coach-${coach._id}`,
        image: coachImages[coach.coach_id] ? { uri: coachImages[coach.coach_id] } : undefined,
        title: coach.coachName + (coach.lastName ? ' ' + coach.lastName : ''),
        data: coach,
      }));
    }
    return [{ type: 'no-results', id: 'no-results-coaches', message: 'No coaches found' }];
  }
  return [{ type: 'no-results', id: 'no-results-generic', message: `Sorry, we couldn't find any results matching "${searchQueryValue}"` }];
}

function renderUnifiedItem({ item, navigation }: { item: any, navigation: any }) {
  switch (item.type) {
    case 'course':
      
      return (
        <SearchResultCard
          data={{
            type: 'course',
            courseName: item.title,
            images: item.image ? [{ url: item.image.uri }] : [],
            price: item.price,
            onPress: () => handleCardPress('course', item.data, navigation),
          }}
        />
      );
    case 'coach':
      let coachName = item.title;
      let lastName = undefined;
      if (coachName && coachName.includes(' ')) {
        const parts = coachName.split(' ');
        coachName = parts[0];
        lastName = parts.slice(1).join(' ');
      }
      return (
        <SearchResultCard
          data={{
            type: 'coach',
            coachName,
            lastName,
            image: item.image,
            onPress: () => handleCardPress('coach', item.data, navigation),
          }}
        />
      );
    case 'academy':
      // Extract image URL string from various possible sources
      let imageUrl = null;
      if (item?.profileImg) {
        imageUrl = item.profileImg;
      } else if (item?.profileImage) {
        imageUrl = item.profileImage;
      } else if (item?.image?.uri) {
        imageUrl = item.image.uri;
      } else if (item?.data?.profileImage) {
        imageUrl = item.data.profileImage;
      } else if (item?.data?.profileImg) {
        imageUrl = item.data.profileImg;
      } else if (item?.data?.academyImages && item.data.academyImages.length > 0) {
        imageUrl = item.data.academyImages[0];
      }
      // Debug logging for academy image extraction
      console.log("Academy item image extraction:", {
        itemProfileImg: item?.profileImg,
        itemProfileImage: item?.profileImage,
        itemImageUri: item?.image?.uri,
        dataProfileImage: item?.data?.profileImage,
        dataProfileImg: item?.data?.profileImg,
        dataAcademyImages: item?.data?.academyImages,
        finalImageUrl: imageUrl
      });
      const image = imageUrl ? { uri: imageUrl } : undefined;
      return (
        <SearchResultCard
          data={{
            type: 'academy',
            name: item.title,
            image: image,
            onPress: () => handleCardPress('academy', item.data, navigation, imageUrl || null),
          }}
        />
      );
    case 'no-results':
    case 'search-prompt':
    case 'search-instruction':
    case 'loading':
      return (
        <View style={styles.noResultsMainContainer}>
          <Text style={styles.noResultsMainText}>{item.message}</Text>
          {item.type === 'no-results' && (
            <Text style={styles.noResultsSubText}>Try searching with different keywords</Text>
          )}
        </View>
      );
    default:
      return null;
  }
}

const SearchUpdated: React.FC<SearchUpdatedProps> = ({
  initialLocationObj = { lat: null, lan: null },
  initialCityName = "",
  showLocationSelector = true,
}) => {
  const [searchQueryValue, setSearchQueryValue] = useState("");
  const [shouldShowListView, setShouldShowListView] = useState(true);
  const [currentLocationObj, setCurrentLocationObj] = useState(
    initialLocationObj
  );
  const [cityName, setCityName] = useState(initialCityName);
  const autoCompleteRef = useRef<any>(null);
  const navigation = useNavigation() as any;

  const [searchResults, setSearchResults] = useState<SearchResult>({
    suggestions: [],
    courses: [],
    coaches: [],
    uniqueCoaches: [],
    academies: [],
  });

  const [isSearching, setIsSearching] = useState(false);
  const [combinedData, setCombinedData] = useState<any[]>([]);
  const [coachImages, setCoachImages] = useState<{ [coachId: string]: string }>({});
  const [searchError, setSearchError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'courses' | 'coaches' | 'academies'>("courses");

  useEffect(() => {
    if (initialLocationObj && (initialLocationObj.lat || initialLocationObj.lan)) {
      setCurrentLocationObj(initialLocationObj);
    }
  }, [initialLocationObj]);

  useEffect(() => {
    if (initialCityName && initialCityName.trim()) {
      setCityName(initialCityName);
      setShouldShowListView(false);
      autoCompleteRef.current?.setAddressText(initialCityName);
    }
  }, [initialCityName]);

  const handleSearchSuggestions = async (query: string) => {
    if (query.length < 3) {
      setSearchResults({
        suggestions: [],
        courses: [],
        coaches: [],
        uniqueCoaches: [],
        academies: [],
      });
      setCoachImages({});
      return;
    }

    setIsSearching(true);

    try {
      const result = await fetchSearchSuggestions({
        query,
        location: currentLocationObj,
        minLength: 3,
      });

      setSearchResults(result);

      // Fetch coach images for all unique coaches (profileImg) - always fetch, like the web
      if (result.uniqueCoaches && result.uniqueCoaches.length > 0) {
        result.uniqueCoaches.forEach(async (coach) => {
          if (coach.coach_id) {
            try {
              // console.log(`Fetching image for Coach ID: ${coach.coach_id}`);
              const response = await axios.get(`${process.env.NEXT_PUBLIC_BASE_URL}/api/coach/player/${coach.coach_id}`);
              const data = response.data;
              if (data?.profileImg) {
                setCoachImages(prev => ({ ...prev, [coach.coach_id]: data.profileImg }));
                // console.log(`Set image for Coach ID: ${coach.coach_id} -> ${data.profileImg}`);
              } else {
                setCoachImages(prev => ({ ...prev, [coach.coach_id]: '' }));
                // console.log(`No profileImg found for Coach ID: ${coach.coach_id}. Data: ${JSON.stringify(data)}`);
              }
            } catch (error) {
              setCoachImages(prev => ({ ...prev, [coach.coach_id]: '' }));
              console.log(`Error fetching image for Coach ID: ${coach.coach_id}`, error);
            }
          }
        });
      } else {
        setCoachImages({});
      }
    } catch (error) {
      console.error("❌ Error fetching search suggestions:", error);
      setCoachImages({});
    } finally {
      setIsSearching(false);
    }
  };

  const debouncedSearch = debounce(handleSearchSuggestions, 300);

  useEffect(() => {
    debouncedSearch(searchQueryValue);
    return () => debouncedSearch.cancel();
  }, [searchQueryValue, currentLocationObj]);

  useEffect(() => {
    const generateCombinedData = () => {
      const data: any[] = [];

      if (!searchQueryValue || searchQueryValue.trim() === "") {
        data.push({
          type: "search-prompt",
          message: "Start typing to search for courses, coaches, and academies",
          id: "search-prompt",
        });
        return data;
      }

      if (searchQueryValue.trim().length < 3) {
        data.push({
          type: "search-instruction",
          message: "Type at least 3 characters to search",
          id: "search-instruction",
        });
        return data;
      }

      if (isSearching) {
        data.push({
          type: "loading",
          message: "Searching...",
          id: "loading",
        });
        return data;
      }

      const { courses, uniqueCoaches } = searchResults;
      const hasCourses = Array.isArray(courses) && courses.length > 0;
      const hasCoaches = Array.isArray(uniqueCoaches) && uniqueCoaches.length > 0;

      // If no courses and no coaches, show only no-results (no section headers)
      if (!hasCourses && !hasCoaches) {
        let notFoundMsg = '';
        if (activeTab === 'courses') {
          notFoundMsg = 'No courses found';
        } else if (activeTab === 'coaches') {
          notFoundMsg = 'No coaches found';
        } else if (activeTab === 'academies') {
          notFoundMsg = 'No academies found';
        } else {
          notFoundMsg = `Sorry, we couldn't find any results matching "${searchQueryValue}"`;
        }
        data.push({
          type: "no-results",
          message: notFoundMsg,
          id: "no-results",
        });
        return data;
      }

      // Only show the relevant section and items based on activeTab
      if (activeTab === "courses" && hasCourses) {
        data.push({
          type: "section",
          title: `Courses (${Array.isArray(courses) ? courses.length : 0})`,
          id: "courses-header",
        });
        courses.forEach((course) => {
          data.push({
            type: "course",
            data: course,
            id: `course-${course._id}`,
          });
        });
      } else if (activeTab === "coaches" && hasCoaches) {
        data.push({
          type: "section",
          title: `Coaches (${Array.isArray(uniqueCoaches) ? uniqueCoaches.length : 0})`,
          id: "coaches-header",
        });
        uniqueCoaches.forEach((coach) => {
          data.push({
            type: "coach",
            data: coach,
            id: `coach-${coach._id}`,
          });
        });
      } else {
        // If the selected tab has no data, show a no-results message for that tab
        let notFoundMsg = '';
        if (activeTab === 'courses') {
          notFoundMsg = 'No courses found';
        } else if (activeTab === 'coaches') {
          notFoundMsg = 'No coaches found';
        } else if (activeTab === 'academies') {
          notFoundMsg = 'No academies found';
        } else {
          notFoundMsg = `Sorry, we couldn't find any results matching "${searchQueryValue}"`;
        }
        data.push({
          type: "no-results",
          message: notFoundMsg,
          id: `no-results-${activeTab}`,
        });
      }
      return data;
    };

    const updatedData = generateCombinedData();
    setCombinedData(updatedData);
  }, [searchQueryValue, isSearching, searchResults, activeTab]);

  const handleLocationSelect = (_data: any, details: any) => {
    if (details?.geometry?.location) {
      const { lat, lng } = details.geometry.location;
      setCurrentLocationObj({ lat, lan: lng });
      setShouldShowListView(false);

      const city = extractCityFromPlaceDetails(details);
      if (city) {
        setCityName(city);
        autoCompleteRef.current?.setAddressText(city);
      }
    }
  };

  const handleSearch = () => {
    if (!searchQueryValue || searchQueryValue.trim().length < 3) {
      setSearchError('Please enter at least 3 characters to search.');
      return;
    }
    setSearchError(null);
    const lat = currentLocationObj?.lat ?? undefined;
    const long = currentLocationObj?.lan ?? undefined;
    navigation.navigate("Collection", {
      q: searchQueryValue,
      lat,
      long,
    });
  };

  const renderItem = ({ item }: { item: any }) => {
    switch (item.type) {
      case "section":
        return null;
      case "course":
        return (
          <SearchResultCard
            data={{
              type: 'course',
              courseName: item.data.courseName,
              images: item.data.images,
              price: getDisplayFee(item.data),
              onPress: () => navigation.navigate('Courses', { courseId: item.data._id })
            }}
          />
        );
      case "coach":
        return (
          <SearchResultCard
            data={{
              type: 'coach',
              coachName: item.data.coachName,
              lastName: item.data.lastName,
              image: coachImages[item.data.coach_id] ? { uri: coachImages[item.data.coach_id] } : undefined,
              onPress: () => navigation.navigate('CoachProfile', { ID: item.data.coach_id })
            }}
          />
        );
      case "not-found":
      case "no-results":
        return (
          <View style={styles.noResultsMainContainer}>
            <Text style={styles.noResultsMainText}>{item.message}</Text>
            <Text style={styles.noResultsSubText}>
              Try searching with different keywords
            </Text>
          </View>
        );
      case "search-prompt":
        return (
          <View style={styles.searchPromptContainer}>
            <Text style={styles.searchPromptText}>{item.message}</Text>
          </View>
        );
      case "search-instruction":
        return (
          <View style={styles.searchInstructionContainer}>
            <Text style={styles.searchInstructionText}>{item.message}</Text>
          </View>
        );
      case "loading":
        return (
          <View style={styles.loadingInlineContainer}>
            <Text style={styles.loadingInlineText}>{item.message}</Text>
          </View>
        );
      case "academy":
        return (
          <SearchResultCard
            data={{
              type: 'academy',
              name: item.data.academyName,
              image: item.image,
              onPress: () => handleCardPress('academy', item.data, navigation, item.image)
            }}
          />
        );
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.locationAndSearchContainer}>
        {showLocationSelector && (
          <View style={styles.googlePlacesWrapper}>
            <GooglePlacesAutocomplete
              placeholder="City Name"
              placeholderTextColor="#000"
              
              ref={autoCompleteRef}
              onPress={handleLocationSelect}
              showPoiEnabled={false}
              fetchDetails
              query={{
                key: GOOGLE_PLACES_API_KEY,
                language: "en",
                components: "country:in",
              }}
              listViewDisplayed={shouldShowListView}
              textInputProps={{
                onChangeText: (text: string) => {
                  if (text === "") {
                    setCurrentLocationObj({ lat: null, lan: null });
                    setCityName("");
                  }
                },
              }}
              debounce={200}
              styles={googlePlacesStyles}
            />
          </View>
        )}
        <View style={styles.searchInputContainer}>
          <TextInput
            style={styles.searchInput}
            placeholder="Course/Coach/Sports"
            placeholderTextColor="#000"
            value={searchQueryValue}
            onChangeText={text => {
              setSearchQueryValue(text);
              if (searchError) setSearchError(null);
            }}
          />
        </View>
        {searchError && (
          <Text style={{ color: 'red', marginTop: 4, marginLeft: 8 }}>{searchError}</Text>
        )}

        <TouchableOpacity style={styles.searchButton} onPress={handleSearch}>
          <Image
            source={require("../../assets/searchicon.png")}
            style={styles.searchButtonIcon}
          />
          <Text style={styles.searchButtonText}>Search</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.toggleHeaderContainer}>
        {TABS.map(tab => (
          <TouchableOpacity
            key={tab.key}
            style={[styles.toggleButton, activeTab === tab.key && styles.toggleButtonActive]}
            onPress={() => setActiveTab(tab.key as typeof activeTab)}
          >
            <Text style={[styles.toggleButtonText, activeTab === tab.key && styles.toggleButtonTextActive]}>
              {tab.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      <View style={styles.resultsContainer}>
        <FlatList
          data={getResultsToShow({
            activeTab,
            searchQueryValue,
            isSearching,
            searchResults,
            coachImages,
          }) as any[]}
          renderItem={({ item }) => renderUnifiedItem({ item, navigation })}
          keyExtractor={item => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.flatListContent}
        />
      </View>
    </View>
  );
};

// Helper to get display fee for course
function getDisplayFee(course: any) {
  if (course.classType === "course") {
    return course.fees.feesCourse || course.fees.fees;
  } else {
    // For classes, show the lowest fee between fees30 and fees60
    const fees30 = course.fees.fees30;
    const fees60 = course.fees.fees60;
    if (fees30 && fees60) {
      return Math.min(fees30, fees60);
    } else if (fees30) {
      return fees30;
    } else if (fees60) {
      return fees60;
    } else {
      return course.fees.fees;
    }
  }
}

// Minimal test screen for Google Places scrollability
export const SearchPlacesTest = () => {
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#fff' }}>
      <View style={{ width: '90%', marginTop: 40, zIndex: 1000 }}>
        <GooglePlacesAutocomplete
          placeholder="City Name"
          fetchDetails
          query={{
            key: GOOGLE_PLACES_API_KEY,
            language: 'en',
            components: 'country:in',
          }}
          styles={{
            container: { flex: 0, zIndex: 1000, elevation: 10 },
            listView: {
              maxHeight: 250,
              zIndex: 1001,
              elevation: 10,
              backgroundColor: '#fff',
            },
          }}
        />
      </View>
    </View>
  );
};

export default SearchUpdated;