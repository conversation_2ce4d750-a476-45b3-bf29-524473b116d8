import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Image, Text, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';

const redirect = require('../../../assets/redirect.png')


export default function CourseCoach({ course, coachData }) {
  const navigation = useNavigation();
  const [isLoading, setIsLoading] = useState(false);
  const { coachName, coach_id, category } = course;
  useEffect(() => {
    if (Object.keys(coachData).length !== 0) {
      setIsLoading(false);
    } else {
      setIsLoading(true);
    }
  }, [coachData]);
  if (isLoading || Object.keys(coachData).length === 0) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }
  return (
    <View style={styles.container3}>
      <View style={styles.subContainer}>
        <Image
          source={{ uri: coachData.profileImg || '' }}
          style={styles.courseCoachImg}
        />
        <Text style={styles.courseCoachName}>{coachName}</Text>
        <Text style={styles.courseCoachGame}>{category} Coach</Text>
        <Text style={styles.courseCoachDescription} numberOfLines={3}>
          {coachName} is well known in {category} circle of Hyderabad with years of experience in {category} coaching.
        </Text>
        <TouchableOpacity
          onPress={() =>
            navigation.navigate('CoachProfile', { coach_id: coachData?._id, coachToken: coachData?.refreshToken, coachData: coachData, ID: coach_id._id })
          }
        >
          <View style={{display:"flex", flexDirection:"row", justifyContent:"center", alignItems:"center", marginVertical:"5%"}}>
            <Text
              style={{
                textAlign: 'center',
                color: 'black',
                fontWeight: '500',
                fontSize: 16,
                textDecorationLine: 'underline',
                fontFamily:"Lato-Regular"

              }}
            >
              View Profile
            </Text> 
            <Image source={redirect} style={{ width: 20, height: 20 }} />
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
}

// Styles remain unchanged
const styles = StyleSheet.create({
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  container3: {
    width: '90%',
    marginLeft: 'auto',
    marginRight: 'auto',
    display: 'flex',
    flexDirection: 'column',
    gap: 20,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 10,
    padding: 15,
  },
  subContainer: {
    width: '80%',
    marginLeft: 'auto',
    marginRight: 'auto',
    gap: 15,
  },
  courseCoachImg: {
    width: 150,
    height: 200,
    alignSelf: 'center',
    marginBottom: 10,
  },

  courseCoachName: {
    fontSize: 18,
    textAlign: 'center',
    fontWeight: '500',
    color: 'black',
    fontFamily:"Lato-Regular"

  },
  courseCoachGame: {
    fontSize: 18,
    textAlign: 'center',
    color: 'skyblue',
    fontWeight: '500',
    fontFamily:"Lato-Bold"
  },
  courseCoachDescription: {
    fontSize: 14,
    textAlign: 'center',
    color: 'grey',
    lineHeight:21,
    fontFamily:"Lato-Bold"
    // color:"#000000",
  },
});
