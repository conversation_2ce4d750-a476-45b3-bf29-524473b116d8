import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  ScrollView,
  Image,
  Button,
  Alert,
  Share,
  Linking,

} from 'react-native';
import { Divider } from 'react-native-paper';
import AsyncStorage from "@react-native-async-storage/async-storage";
import axios from 'axios';
import { NEXT_PUBLIC_BASE_URL } from '@env';
import { useRoute } from '@react-navigation/native';
// import Filter from '../components/Filters/Filter';
import BookingModal from '../../BookingModal/BookingModal';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../../Context/AuthContext';
// const delete = require('../../../assets/delete.png')
import RazorPay from '../../RazorPay/RazorPay';
import moment from 'moment-timezone';
import { ScaledSheet as StyleSheet } from 'react-native-size-matters';

export default function CourseSideDetails({ course, route }) {
  const { dates, fees, facility, coach_id } = course;
  const [coachId, setCoachId] = useState(coach_id._id);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedSlots, setSelectedSlots] = useState([]);
  const [totalPrice, setTotalPrice] = useState(0);
  const { isLoggedIn, playerData } = useAuth();
  const navigation = useNavigation();
  useEffect(() => {
    AsyncStorage.setItem('search', course.category.toLowerCase());
  }, [course])

  useEffect(() => {
    const updateAsyncStorage = async () => {
      try {
        await AsyncStorage.setItem('search', course.category.toLowerCase());
        const lastCourse = await AsyncStorage.getItem('lastCourse');
      } catch (error) {
        console.error("Error handling AsyncStorage:", error);
      }
    };
  
    updateAsyncStorage();
  }, [course]);
  
  const formatDateTime = (date, time) => {
    const formattedDate = new Date(date).toLocaleDateString('en-IN', {
      // weekday: 'long',
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
    const formattedTime = new Date(`2024-01-01T${time}:00`).toLocaleTimeString(
      'en-IN',
      {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      },
    );
    return `${formattedDate}`;
  };

  const formatTime = (date, time) => {
    const formattedDate = new Date(date).toLocaleDateString('en-IN', {
      // weekday: 'long',
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
    const formattedTime = new Date(`2024-01-01T${time}:00`).toLocaleTimeString(
      'en-IN',
      {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      },
    );

    return `${formattedDate} at ${formattedTime}`;

  }


  const allDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  const renderDayCircles = () => {
    return allDays.map(day => {
      const isActive = dates?.days?.includes(day);
      return (
        <View
          key={day}
          style={[
            styles.dayCircle,
            isActive ? styles.activeDayCircle : styles.inactiveDayCircle,
          ]}>
          <Text
            style={[
              styles.dayText,
              isActive ? styles.activeDayText : styles.inactiveDayText,
            ]}>
            {day}
          </Text>
        </View>
      );
    });
  };

  const handleMapNavigation = (latitude, longitude) => {
    const url = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
    Linking.openURL(url);
  };

  const handleCloseModal = () => {
    setModalVisible(false);
  };
  const handleBookNow = () => {
    if (!isLoggedIn) {
      navigation.navigate('SignIn')
    } else if (!playerData.mobile || !playerData.homeState) {
      navigation.navigate('PlayerProfile');
    } else {
      setModalVisible(true);
    }
  };

  const handleBookSlot = () => {
    setModalVisible(true);
  };
  const originalFee = fees?.feesCourse || totalPrice;

  // Calculate 10% of the original fee
  const tenPercent = originalFee * 0.1;

  // Calculate total amount before GST
  const totalBeforeGST = originalFee + tenPercent;

  // Calculate 18% GST on the total amount
  const gstAmount = totalBeforeGST * 0.18;

  //new calculation
  let total = 0;
  for (const booking of selectedSlots) {
    const price = Number(booking.fees);
    total += price;
  }
  let platformTax = course.classType === "course" ? course.fees.feesCourse * 0.12 : total * 0.12;
  let gst = platformTax * 0.18;
  let coachGst = course?.coach_id?.hasGst
    ? course.classType === "course"
      ? course.fees.feesCourse * 0.18
      : total * 0.18
    : 0;
  let finalTotal = Math.ceil((course.classType === "course" ? course.fees.feesCourse : total) + platformTax + gst + coachGst);
  let finalPrice = Math.ceil(
    (course.classType === "course" ? course.fees.feesCourse : total) + platformTax + gst + coachGst);
  useEffect(() => {
    setTotalPrice(finalTotal)
  }, [finalTotal, totalPrice])

  // Calculate the final total including GST
  // const finalTotal = totalBeforeGST + gstAmount;
  const handleDeleteSlot = index => {
    const updatedSlots = [...selectedSlots];
    const removedSlot = updatedSlots.splice(index, 1)[0];
    let priceDeduction = 0;
    if (removedSlot) {
      switch (removedSlot.duration) {
        case '30min':
          priceDeduction = course?.fees?.fees30;
          break;
        case '45min':
          priceDeduction = course?.fees?.fees45;
          break;
        default:
          priceDeduction = course?.fees?.fees60;
      }
    }
    if (priceDeduction) {
      setTotalPrice(prevTotalPrice => prevTotalPrice - priceDeduction);
      setTotalPrice(finalTotal)
    }
    setSelectedSlots(updatedSlots);
  };
  const [modifiedSlots, setModifiedSlots] = useState([]);

  // useEffect to update modifiedSlots whenever selectedSlots changes
  useEffect(() => {
    const updateModifiedSlots = () => {
      const modifiedSlots = selectedSlots.map(slot => {
        // No need to remove AM/PM from start and end times
        const startTime = slot.start;
        const endTime = slot.end;

        // Return a new slot object with the original start and end times
        return {
          ...slot,
          start: startTime,
          end: endTime
        };
      });

      setModifiedSlots(modifiedSlots);
    };

    updateModifiedSlots();
  }, [selectedSlots]);



  const renderSlotSelectionButton = () => {
    return (
      <View style={styles.slotSelectionButtonContainer}>
        <Button
          title="Select Slot to Book"
          onPress={handleBookNow}
          color="#E31F26"
        />
      </View>
    );
  };
  const renderBookedSlotPrice = () => {
    if (totalPrice > 0) {
      return (
        <>
          <Text
            style={styles.summaryPriceText}>
            {/* {` ₹${(total + platformTax).toFixed(2)} + ${gst.toFixed(2)} (18% GST)`} */}
          </Text> 
          <View style={styles.timingCard}>
            <Text style={styles.timingCardTitle}>Price Summary</Text>
            <View style={styles.row}>
              <Text style={styles.textCon}>Class Prices :</Text>
              <Text style={styles.valueWith}>{` ₹${total.toFixed(2)}`}</Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.textCon}>Platform Fee :</Text>
              <Text style={styles.valueWith}>{` ₹${platformTax.toFixed(2)}`}</Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.textCon}>GST :</Text>
              <Text style={styles.valueWith}>{` ₹${gst.toFixed(2)}`}</Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.textCon}>Coach GST(18%): </Text>
              <Text style={styles.valueWith}>{` ₹${coachGst.toFixed(2)}`}</Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.textCon}>Total :</Text>
              <Text style={styles.valueWith}>{` ₹${finalPrice.toFixed(2)}`}</Text>
            </View>

          </View>
          <View style={styles.slotSelectionButtonContainer}>
            {/* <Button title="PAY NOW" color="#E31F26" /> */}
            <RazorPay
              data={course}
              playerData={playerData}
              bookingArray={modifiedSlots}
              total={finalTotal}
              setSelectedSlots={setSelectedSlots}
              setModifiedSlots={setModifiedSlots}
            />
          </View>
        </>
      );
    } else {
      return (
        <View style={styles.buttonContainer}>
          <Text style={styles.buttonContainerText}>{fees?.feesCourse ? `${(fees.feesCourse + platformTax).toFixed(2)} + ${gst.toFixed(2)} (18% GST)`
            : total
              ? `${(total + platformTax).toFixed(2)} + ${(gst + coachGst).toFixed(2)} (18% GST)`
              : `Starts from ₹${(
                fees?.fees +
                fees?.fees * 0.12
              ).toFixed(2)} + 18% GST`}</Text>
          <TouchableOpacity onPress={handleBookNow}>
            <View style={styles.bookingButtonContainer}>
            {isLoggedIn === true ? (
                !playerData?.mobile && !playerData?.homeState ? (
                  <Text
                    style={styles.slotButtonText}
                    onPress={() => navigation.navigate('PlayerProfile')}
                  >
                    Enter mobile and home state to book
                  </Text>
                ) : !playerData?.mobile ? (
                  <Text
                    style={styles.slotButtonText}
                    onPress={() => navigation.navigate('PlayerProfile')}
                  >
                    Enter the mobile number to book
                  </Text>
                ) : !playerData?.homeState ? (
                  <Text
                    style={styles.slotButtonText}
                    onPress={() => navigation.navigate('PlayerProfile')}
                  >
                    Enter the home state to book
                  </Text>
                ) : (
                  <Text
                    style={styles.slotButtonText}
                    onPress={handleBookNow}
                  >
                    Select Slot to Book
                  </Text>
                )
              ) : (
                <Text
                  style={styles.slotButtonText}
                  onPress={handleBookNow}
                >
                  Login To Book Slot
                </Text>
              )}
            </View>
          </TouchableOpacity>
        </View>
      );
    }
  };

  const renderBookedSlots = () => {
    if (selectedSlots.length > 0) {
      return (
        <>
          {selectedSlots.map((slot, index) => (
            <View key={index} style={styles.bookedSlotContainer}>
              <Text style={styles.bookedSlotText}>
                Date: {slot.date} Time: {slot.start} - {slot.end}
              </Text>
              <TouchableOpacity onPress={() => handleDeleteSlot(index)} style={styles.deleteButtonAlign}>
                <Image
                  style={styles.deleteIcon}
                  source={require('../../../assets/delete.png')}
                />
              </TouchableOpacity>
            </View>
          ))}
          <View style={{justifyContent: "center"}}>
            <Button
              title="Add new booking"
              color="#000"
              onPress={handleBookNow}
            />
          </View>
        </>
      );
    } else {
      return (
        <View >
          <TouchableOpacity onPress={handleBookNow}>
            <View style={styles.bookingButtonContainer}>
              {isLoggedIn === true ? (
                !playerData?.mobile && !playerData?.homeState ? (
                  <Text
                    style={styles.slotButtonText}
                    onPress={() => navigation.navigate('PlayerProfile')}
                  >
                    Enter mobile and home state to book
                  </Text>
                ) : !playerData?.mobile ? (
                  <Text
                    style={styles.slotButtonText}
                    onPress={() => navigation.navigate('PlayerProfile')}
                  >
                    Enter the mobile number to book
                  </Text>
                ) : !playerData?.homeState ? (
                  <Text
                    style={styles.slotButtonText}
                    onPress={() => navigation.navigate('PlayerProfile')}
                  >
                    Enter the home state to book
                  </Text>
                ) : (
                  <Text
                    style={styles.slotButtonText}
                    onPress={handleBookNow}
                  >
                    Select Slot to Book
                  </Text>
                )
              ) : (
                <Text
                  style={styles.slotButtonText}
                  onPress={handleBookNow}
                >
                  Login To Book Slot
                </Text>
              )}


            </View>
          </TouchableOpacity>
        </View>
      );
    }
  };
  const handleShare = async () => {
    try {
      const result = await Share.share({
        message: `Check out this course: ${course.courseName}. Learn more about it here: https://www.khelcoach.com/courses/${course._id}`,
        // You can also add a URL property if you want to share a link
        url: 'https://example.com/course-detail'
      });

      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // shared with activity type of result.activityType
        } else {
          // shared
        }
      } else if (result.action === Share.dismissedAction) {
        // dismissed
      }
    } catch (error) {
      alert(error.message);
    }
  };

  return (
    <>
      <View style={styles.container2}>
        {!fees?.feesCourse ? (
          <View>{renderBookedSlotPrice()}</View>
        ) : (
          <View style={styles.buttonContainer}>
            <Text style={styles.buttonContainerText}>
              {fees?.feesCourse
                ?
                <View style={styles.marginBottom3}>
                  <Text style={styles.timingCardTitle}>Price Summary</Text>
                  <View style={styles.row}>
                    <Text style={styles.textCon}>Class Prices :</Text>
                    <Text style={styles.valueWith}>{` ₹${fees.feesCourse.toFixed(2)}`}</Text>
                  </View>
                  <View style={styles.row}>
                    <Text style={styles.textCon}>Platform Fee :</Text>
                    <Text style={styles.valueWith}>{` ₹${platformTax.toFixed(2)}`}</Text>
                  </View>
                  <View style={styles.row}>
                    <Text style={styles.textCon}>GST :</Text>
                    <Text style={styles.valueWith}>{` ₹${gst.toFixed(2)}`}</Text>
                  </View>
                  <View style={styles.row}>
                    <Text style={styles.textCon}>Total :</Text>
                    <Text style={styles.valueWith}>{` ₹${(fees.feesCourse + platformTax + gst).toFixed(2)}`}</Text>
                  </View>


                </View>

                : total
                  ? `${(total + platformTax).toFixed(2)} + ${gst.toFixed(2)} (18% GST)`
                  : `Starts from ₹${(fees?.fees + fees?.fees * 0.12).toFixed(2)} + 18% GST`}
            </Text>
            {course.playerEnrolled !== course.maxGroupSize ? (
              <>
                <RazorPay
                  data={course}
                  playerData={playerData}
                  bookingArray={modifiedSlots}
                  total={finalTotal}
                  setSelectedSlots={setSelectedSlots}
                  setModifiedSlots={setModifiedSlots}
                />
              </>
            ) : (
              <View style={[styles.bookingButtonContainer, styles.bookingButtonUnavailable]}>
                <Text style={styles.slotUnavailableText}>SLOTS UNAVAILABLE</Text>
              </View>
            )}
          </View>
        )}
        <View style={styles.buttonContainer}>
          <Text style={styles.buttonContainerText}>
            Share with your friends!
          </Text>
          <TouchableOpacity onPress={handleShare}>
            <View style={styles.shareButtonContainer}>
              <Text style={styles.shareButtonText} onPress={handleShare}>SHARE</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
      <View style={styles.container2}>
        {fees?.feesCourse ? (
          <View style={styles.timingCard}>
            <View style={styles.timingSlot}>
              <Text style={styles.timingCardTitle}>Slot Details:</Text>
              <Text style={styles.groupSize}>Group Size: {course.playerEnrolled}/{course.maxGroupSize}</Text>
            </View>

            <Text style={styles.timingCardText}>
              {formatDateTime(dates?.startDate, dates?.startTime)} - {formatDateTime(dates?.endDate, dates?.endTime)}
            </Text>
            <View style={styles.daysContainer}>{renderDayCircles()}</View>

            <Text style={styles.timingCardSubtitle}>Time:</Text>
            <Text style={styles.timingCardText}>
              {formatTime('2024-01-01', dates?.startTime).split('at')[1]} - {formatTime('2024-01-01', dates?.endTime).split('at')[1]}
            </Text>
          </View>
        ) : (
          selectedSlots && selectedSlots.length > 0 ? (
            <View style={styles.timingCard}>
              <Text style={styles.timingCardTitle}>Timings</Text>
              {renderBookedSlots()}
            </View>
          ) : <></>
        )}
        <View style={styles.adressContainer}>
          <View style={styles.locationContainer}>
            <Text style={styles.locationHeading}>Location</Text>
            {facility?.location?.coordinates[0] && facility?.location?.coordinates[1] && (

              <TouchableOpacity onPress={() => { handleMapNavigation(facility?.location?.coordinates[0], facility?.location?.coordinates[1]) }}>
                <Text style={styles.showOnMapLink}>Show on map</Text>
              </TouchableOpacity>
            )}
          </View>
          <Text style={styles.locationText}>
            {facility?.addressLine1}, {facility?.addressLine2}, {facility?.city} - {facility?.pinCode}, {facility?.state}
          </Text>
        </View>
      </View>
      <BookingModal
        dates={dates}
        course={course}
        coach_id={coach_id}
        fees={fees}
        isVisible={modalVisible}
        setModalVisible={setModalVisible}
        onClose={() => setModalVisible(false)}
        coachId={coachId}
        setSelectedSlots={setSelectedSlots}
        selectedSlots={selectedSlots}
        setTotalPrice={setTotalPrice}
        totalPrice={totalPrice}
      />
    </>
  );
}

const styles = StyleSheet.create({
  container2: {
    width: '90%',
    marginLeft: 'auto',
    marginRight: 'auto',
    display: 'flex',
    flexDirection: 'column',
    gap: '20@vs',
  },
  buttonContainer: {
    paddingVertical: '16@vs',
    paddingHorizontal: '16@s',
    backgroundColor: '#fff',
    gap: '10@vs',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#ccc',
  },
  adressContainer: {
    paddingVertical: '12@vs',
    paddingHorizontal: '10@s',
    gap: '20@vs',
    borderRadius: 10,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ccc',
  },
  locationHeading: {
    color: 'black',
    fontWeight: '500',
    fontSize: '14@ms',
    fontFamily: "Lato-Bold"
  },
  showOnMapLink: {
    color: '#0095FF',
    fontWeight: '500',
    fontSize: '12@ms',
    paddingBottom: "1%",
    borderBottomWidth: 1,
    borderBottomColor: '#0095FF',
    fontFamily: "Lato-Regular"

  },

  locationText: {
    color: 'black',
    fontWeight: '400',
    fontSize: '12@ms',
    fontFamily: "Lato-Regular"

  },

  locationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  buttonContainerText: {
    color: 'black',
    fontSize: '12@ms',
    fontWeight: '500', fontFamily: "Lato-Bold"
  },
  bookedSlotContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: '8@vs',
    gap: '8@s',
    minHeight: '32@vs',
  },
  bookedSlotText: {
    flex: 1,
    flexWrap: 'wrap',
    color: "#000",
    fontSize: '12@ms',
    fontFamily: "Lato-Regular"
  },
  deleteIconContainer: {
    width: 32,
    alignItems: 'flex-end',
    justifyContent: 'flex-start',
    paddingLeft: 8,
  },
  timingCard: {
    paddingVertical: '16@vs',
    paddingHorizontal: '16@s',
    backgroundColor: '#fff',
    gap: '10@vs',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#ccc',
  },
  timingSlot: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between"
  },
  groupSize: {
    fontSize: '13@ms',
    color: '#000',
    fontFamily: "Lato-Bold"

  },
  timingCardTitle: {
    color: '#000',
    fontFamily: "Lato-Bold",
    fontSize: '14@ms',
  },
  timingCardSubtitle: {
    color: '#000',
    fontFamily: "Lato-Bold",
    fontSize: '12@ms',
  },
  timingCardText: {
    color: '#000',
    fontSize: '12@ms',
    fontWeight: "500",
    fontFamily: "Lato-Regular"

  },
  daysContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    color: '#000',
    paddingHorizontal: '5%',

  },
  dayCircle: {
    width: 45,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: "0.5%",
  },
  dayText: {
    color: '#000',
    fontSize: '8@ms', fontFamily: "Lato-Regular"

  },
  activeDayCircle: {
    backgroundColor: '#0EA5E9',
  },
  inactiveDayCircle: {
    backgroundColor: '#D3D3D3',
  },
  activeDayText: {
    color: 'white',
  },
  inactiveDayText: {
    color: 'black',
  },
  slotSelectionButtonContainer: {
    marginTop: 15,
  },
  deleteIcon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  row: { flexDirection: 'row', alignItems: 'center', marginBottom: 8, color: "#000" },
  textCon: { width: 100, color: "#000", fontFamily: 'Lato-Bold', fontSize: '14@ms', },
  valueWith: { color: "#000", width: "70%", fontFamily: 'Lato-Regular', fontSize: '11@ms' },
  slotButtonText: {
    fontSize: '12@ms',
    color: '#fff',
    fontFamily: 'Lato-Regular',
  },
  slotUnavailableText: {
    fontSize: '12@ms',
    color: '#fff',
    fontFamily: 'Lato-Regular',
  },
  shareButtonText: {
    fontSize: '12@ms',
    color: '#fff',
    fontFamily: 'Lato-Regular',
  },
  summaryPriceText: {
    fontSize: '12@ms',
    fontWeight: '500',
    marginBottom: 10,
    color: '#000',
    fontFamily: 'Lato-Regular',
  },
  bookingButtonContainer: {
    width: '100%',
    height: 44,
    borderRadius: 3,
    backgroundColor: '#E31F26',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bookingButtonUnavailable: {
    opacity: 0.6,
  },
  shareButtonContainer: {
    width: '100%',
    height: 44,
    borderRadius: 3,
    backgroundColor: '#00AEEF',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  marginBottom3: {
    marginBottom: '8@vs',
  },
  deleteButtonAlign: {
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    height: '32@vs',
    width: '32@s',
  },
});