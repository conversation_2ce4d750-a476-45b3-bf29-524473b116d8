import React, { useState, useReducer, useRef } from 'react';
import { StyleSheet, Text, View, ScrollView, Dimensions, TouchableOpacity } from 'react-native';
import Image from '@d11/react-native-fast-image';

const { width } = Dimensions.get('window');
const SIDE_PADDING = 20;
const sliderWidth = width - 2 * SIDE_PADDING;
const IMAGE_HORIZONTAL_PADDING = 16;
import { Rating, AirbnbRating } from 'react-native-ratings';
export default function CourseImage({ course }) {
    const [activeSlide, setActiveSlide] = useState(0);
    const isMultipleImages = course?.images && course.images.length > 1;
    const scrollViewRef = useRef();

    const handleDotClick = (index) => {
        setActiveSlide(index);  // Set the current index
        scrollViewRef.current.scrollTo({ x: width * index, animated: true });
    };

    return (
        <View style={styles.container}>
            <View style={{ paddingHorizontal: "5%" }}>
                <Text style={styles.courseTitle}>{course?.courseName}</Text>
                <View style={styles.rowReview}>
                    <View style={styles.reviews}>
                        <Rating
                            type='star'
                            ratingCount={5}
                            startingValue={course?.ratings ? course?.ratings?.stars : 0}
                            imageSize={20}
                            readonly={true}
                            ratingBackgroundColor='#c8c7c8'
                        />
                    </View>
                    <Text style={styles.labelReview}> {`${course?.ratings ? course?.ratings?.noOfRatings : 0
                        } Reviews`}</Text>

                </View>
            </View>
            {isMultipleImages ? (
                <View style={styles.sliderContainer}>
                    <ScrollView
                        ref={scrollViewRef}
                        horizontal
                        pagingEnabled
                        showsHorizontalScrollIndicator={false}
                        onMomentumScrollEnd={(e) => {
                            const pageIndex = Math.floor(e.nativeEvent.contentOffset.x / sliderWidth);
                            setActiveSlide(pageIndex);
                        }}
                        style={styles.scrollViewStyle}
                    >
                        {course.images.map((image, index) => (
                            <Image key={index} source={{ uri: image.url }} style={styles.courseImage} />
                        ))}
                    </ScrollView>
                    <View style={styles.pagination}>
                        {course.images.map((_, index) => (
                            <TouchableOpacity key={index} onPress={() => handleDotClick(index)} style={[
                                styles.dot,
                                activeSlide === index ? styles.activeDot : styles.inactiveDot
                            ]} />
                        ))}
                    </View>
                </View>
            ) : (
                <Image source={{ uri: course?.images[0]?.url }} style={styles.courseImage} />
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        marginVertical: 25,
    },
    courseTitle: {
        fontSize: 18,
        color: 'black',
        letterSpacing: 1,
        lineHeight: 25,
        fontFamily:"Lato-Bold"
    },
    rowReview: {
        display: "flex",
        flexDirection: "row",
        marginVertical: "3%"
    },
    reviews: {
        display: "flex",
        flexDirection: "row",
    },
    reviewStar: {
        width: 20,
        height: 20,
        marginRight: 4,
    },
    labelReview: {
        color: "grey",
        fontFamily:"Lato-Regular"

    },
    sliderContainer: {
        height: width,
        paddingHorizontal: IMAGE_HORIZONTAL_PADDING,
    },
    scrollViewStyle: {
        width: sliderWidth, 
    },
    courseImage: {
        width: sliderWidth, 
        height: width,  
        resizeMode: 'cover',
        borderRadius: 12,
        alignSelf: 'center', 
    },
    pagination: {
        flexDirection: 'row',
        position: 'absolute',
        bottom: 10,
        alignSelf: 'center',
    },
    dot: {
        width: 8,
        height: 8,
        borderRadius: 4,
        marginHorizontal: 3,
    },
    activeDot: {
        backgroundColor: '#000',
    },
    inactiveDot: {
        backgroundColor: 'rgba(0,0,0,.2)',
    },
});