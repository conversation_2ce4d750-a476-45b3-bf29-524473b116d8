import { StyleSheet, Text, View, Image } from 'react-native'
import React from 'react'
import HTML from 'react-native-render-html';
const Amenities = [
  { title: 'First Item', },
  { title: 'Second Item', },
  { title: 'Third Item', },
  { title: 'Fourth Item', },
  { title: 'Fiveth Item', },
]
const Instructions = [
  { title: 'First Item', },
  { title: 'Second Item', },
  { title: 'Third Item', },
  { title: 'Fourth Item', },
  { title: 'Fiveth Item', },
]

export default function CourseAmenities({ course }) {
  const {
    amenitiesProvided,
    whatYouHaveToBring
  } = course;

  const tagsStyles = {
    p: { fontSize: 16, color: '#000', marginVertical: 0 }, 
  };
  return (
    <View style={styles.container5}>
      <View style={{ gap: 1 , marginBottom:"15%"}}>
        <Text style={styles.amenities}>Amenities</Text>

        <HTML tagsStyles={{
        p: { margin: 0, padding: 0, color: "#000" ,  fontFamily:"Lato-Regular"
      }, 
        ol: { margin: 0, padding: 0, color: "#000" ,}, 
        li: { margin: 0, paddingHorizontal: 0, color: "#000" , }}}  source={{ html: amenitiesProvided }} />

      </View>
      <View style={{ gap: 10 }}>
        <Text style={styles.amenities}>Instructions</Text>
        <HTML tagsStyles={{
        p: { margin: 0, padding: 0, color: "#000" , alignItems: 'center',    fontFamily:"Lato-Regular"
      }, 
        ol: { margin: 0, padding: "2%", color: "#000" , alignItems: 'center'}, 
        li: { margin: 0, paddingHorizontal: "2%", color: "#000" , alignItems: 'center'}}} source={{ html: whatYouHaveToBring }} />

      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container5: {
    width: '90%',
    marginLeft: 'auto',
    marginRight: 'auto',
    display: 'flex',
    flexDirection: 'column',
    gap: 20,
    paddingVertical: '8%',
    paddingHorizontal: '5%',
    gap: 10,
    borderRadius: 10,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ccc',
  },
  amenities: {
    fontSize: 20,
    fontWeight: '500',
    backgroundColor: 'black',
    color: 'white',
    paddingHorizontal: '1%',
    paddingVertical: '5%',
    textAlign: 'center',
    borderRadius: 10,
    marginBottom:"5%",
    fontFamily:"Lato-Bold"
  }
})