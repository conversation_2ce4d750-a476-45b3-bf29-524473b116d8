import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import Image from '@d11/react-native-fast-image';
import { useNavigation } from '@react-navigation/native';
import axios from 'axios';
import { NEXT_PUBLIC_BASE_URL } from '@env';
import AsyncStorage from "@react-native-async-storage/async-storage";

const redirect = require("../../assets/redirect.png");
const Recommended = ({courseId}) => {
  
  const [courses, setCourses] = useState([]);
  const navigation = useNavigation();

  useEffect(() => {
    const fetchCourses = async () => {
      const search = await getSearchFromStorage();
      let url = `${NEXT_PUBLIC_BASE_URL}/api/course/filter?`;
      if (search) {
        url += `category=${search}`;
      }
      try {
        const response = await axios.get(url);
        setCourses(response.data);
      } catch (error) {
        console.error('Error fetching data: 31');
      }
    };

    fetchCourses();
  }, []);

  const getSearchFromStorage = async () => {
    try {
      const search = await AsyncStorage.getItem('search');
      return search;
    } catch (error) {
      console.error('Error retrieving search from AsyncStorage: 43');
      return null;
    }
  };

  const renderDescription = (description) => {
    const maxChars = 150; // Maximum number of characters to display
    let truncatedDescription = description.replace(/<[^>]+>/g, ''); // Remove HTML tags
    // Replace all occurrences of &nbsp; or &nbsp with a space
    truncatedDescription = truncatedDescription.replace(/&nbsp;?/g, ' ');
    truncatedDescription = truncatedDescription.substring(0, maxChars); // Limit characters
    if (description.length > maxChars) {
      truncatedDescription += "...";
    }
    return <Text style={styles.description}>{truncatedDescription}</Text>;
  };

  const renderTitle = (title)=>{
    const maxChar = 20 ;
    truncatedTitle = title.substring(0, maxChar);
    if(title.length > maxChar){
      truncatedTitle += "...";
    }

    return <Text style={styles.courseName}>{truncatedTitle}</Text>

  }
  return (
    <View style={styles.container}>
     {courses.length > 0 && (
      <View>
      <Text style={styles.heading}>Similar Courses</Text>
       <ScrollView
         horizontal
         showsHorizontalScrollIndicator={false}
         contentContainerStyle={styles.scrollViewContent}>
         {courses.filter(course => !courseId || course._id !== courseId).map((course, index) => (
           <TouchableOpacity
             key={index}
             style={styles.courseItem}
             onPress={() =>
               navigation.push('Courses', { courseId: course._id })
             }>
             <Image
               source={
                 course.images.length > 0
                   ? { uri: course.images[0].url }
                   : require('../../assets/placeholder.png')
               }
               style={styles.image}
             />
             <View style={styles.courseDetails}>
               <Text style={styles.price} numberOfLines={1} ellipsizeMode='tail'>
                 {course.fees?.feesCourse
                   ? `₹ ${Math.round(course.fees?.feesCourse * 1.10)}`
                   : 'Explore'}
               </Text>
               <Text style={styles.courseName} numberOfLines={2}>
                 {renderTitle(course.courseName)}
               </Text>
               <View style={{minHeight:40}}>
               <Text style={styles.description} numberOfLines={2}>
               {renderDescription(course.description)}
               </Text>
               </View>
               <View style={styles.learnMore}>
                 <Text style={styles.learnMoreText}>What will you learn</Text>
                 <Image source={redirect} style={{width:15, height:15}}/>
               </View>
             </View>
           </TouchableOpacity>
         ))}
       </ScrollView>
      </View>
     )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    paddingBottom: '10%',
    paddingHorizontal: '5%',
    marginTop: '10%',
  },
  heading: {
    fontSize: 20,
    fontFamily:"Lato-Bold",
    marginBottom: 16,
    color: '#000',
  },
  scrollViewContent: {
    paddingRight: 16,
  },
  courseItem: {
    width: 250,
    marginRight: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    backgroundColor: '#FAFBFC',
    overflow: 'hidden',
    
  },
  image: {
    width: '100%',
    height: 180,
  },
  courseDetails: {
    borderTopWidth: 1,
    borderColor: '#e2e2e2',
    paddingTop: 8,
    padding: 8,
  },
  price: {
    backgroundColor: '#E31F26',
    color: '#FAFBFCff',
    paddingHorizontal: "5%",
    paddingVertical: "3%",
    borderRadius: 4,
    marginBottom: 8,
    alignSelf: 'flex-start',
  },
  courseName: {
    fontSize: 16,
    fontFamily:"Lato-Bold",
    marginBottom: 4,
    color: '#000',
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    fontFamily: 'Lato-Regular',
    
  },
  learnMore: {
    // padding: 8,
    borderRadius: 4,
    display:"flex",
    flexDirection:"row",
    alignItems:"center"
  },
  learnMoreText: {
    fontFamily:"Lato-Bold",
    color: '#000',
    paddingBottom: "0.5%", 
    borderBottomWidth: 1, 
    borderBottomColor: '#000', 
  },
});

export default Recommended;
