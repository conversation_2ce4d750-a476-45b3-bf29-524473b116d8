import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Animated,
  Dimensions,
  ActivityIndicator,
  ScrollView,
  Alert,
  SafeAreaView,
  Linking,
  FlatList
} from "react-native";
import SelectDropdown from 'react-native-select-dropdown';
import {ScaledSheet as StyleSheet } from 'react-native-size-matters';

import axios from "axios";
import { useNavigation } from "@react-navigation/native";
import { NEXT_PUBLIC_BASE_URL } from "@env";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Camera, useCameraDevice, useCameraPermission, getAvailableCameraDevices } from "react-native-vision-camera";
import { useAuth } from "../Context/AuthContext";
import DeleteAccountModal from "./DeleteAccountModal";
// import { ActivityIndicator } from "react-native-paper";

const rightArrow = require("../assets/next.png");
const downArrow = require("../assets/down.png")
const scanner = require("../assets/qr-code.png")
export default function Header({ onMenuPress }) {
  const screenWidth = Dimensions.get("window").width;
  const navigation = useNavigation();
  const [sports, setSports] = useState([]);
  const [selectedSport, setSelectedSport] = useState("");
  const [menuVisible, setMenuVisible] = useState(false);
  const [showSportOptions, setShowSportOptions] = useState(false);
  const [animation] = useState(new Animated.Value(0));
  const screenHeight = Dimensions.get("window").height;
  const { isLoggedIn, logout, playerData, fetchUserDetails } = useAuth();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios.get(
          `${NEXT_PUBLIC_BASE_URL}/api/category`
        );
        setSports(response.data.data);
      } catch (error) {
        console.error("Error fetching data: 42");
      }
    };
    fetchData();
  }, []);
  const handleSportChange = (sport) => {
    toggleDrawer();
    setSelectedSport(sport);
    if (sport) {
      // navigation.navigate('ListingCards', {selectedOption: sport});
      navigation.navigate("Collection", { selectedOption: sport });
      setMenuVisible(false);
    }

  };

  const toggleDrawer = () => {
    const isOpen = !menuVisible;
    setMenuVisible(isOpen);
    onMenuPress(isOpen);
    Animated.timing(animation, {
      toValue: isOpen ? 1 : 0,
      duration: 400,
      useNativeDriver: false,
    }).start();
    setShowSportOptions(false)
  };

  const toggleSportOptions = () => {
    setShowSportOptions(!showSportOptions);
  };

  const drawerStyles = [
    styles.drawer,
    {
      transform: [
        {
          translateX: animation.interpolate({
            inputRange: [0, 1],
            outputRange: [-800, 0],
          }),
        },
      ],
      height: screenHeight,
    },
  ];
  const handleLogout = async () => {
    try {
      logout();
      toggleDrawer();
      navigation.navigate("SignIn");
    } catch (error) {
      console.error("Error logging out: 97");
    }
  };
  const handleDeleteAccount = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteModalClose = () => {
    setShowDeleteModal(false);
  };

  const handleDeleteSuccess = () => {
    setShowDeleteModal(false);
    toggleDrawer();
    navigation.navigate("SignIn");
  };
  const handleRedirectToSignIn = () => {
    navigation.navigate("SignIn")
    toggleDrawer();
  }
  const handleRedirectToSignUp = () => {
    navigation.navigate("SignUp")
    toggleDrawer();
  }
  const handleRedirectToContact = () => {
    navigation.navigate("Contact")
    toggleDrawer();
  }
  const handleUserAccountRedirect = () => {
      toggleDrawer();
      navigation.navigate("PlayerProfile");
  }
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Image
          source={require("../../src/assets/MainKhelCoach.png")}
          style={styles.logo}
        />
        <View style={styles.iconContainer}>
          <TouchableOpacity
            onPress={() => navigation.navigate('QrScanner')}
            style={styles.iconButton}
          >
            <Image source={scanner} style={styles.iconImage} />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={onMenuPress}
            style={styles.iconButton}
          >
            <Image source={require("../../src/assets/hamburger.png")} style={styles.iconImage} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    // flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: '10@s',
    backgroundColor: "#FAFBFC",
    height: '60@vs',
  },
  iconContainer: {
    flexDirection: "row",
    alignItems: "center",
    height: '100%',
    gap: '20@ms',
  },
  iconButton: {
    width: '25@s',
    height: '25@s',
    justifyContent: "center",
    alignItems: "center",
  },
  iconImage: {
    flex: 1,
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  logo: {
    width: '100@s',
    height: '50@vs',
    resizeMode: "contain",
  },
  drawer: {
    position: "absolute",
    top: 0,
    bottom: 0,
    left: 0,
    width: "100%",
    backgroundColor: "#fff",
    zIndex: 4,
    elevation: 16,
  },
  drawerContent: {
    paddingHorizontal: '10@s',
    justifyContent: "center",
    alignItems: "flex-start",
    marginLeft: "2%"
  },
  closeButton: {
    // alignSelf: 'flex-end',
    // marginRight: 10,
  },
  closeButtonText: {
    fontSize: '40@s',
    color: "#000",
  },
  selectSportButton: {
    // marginTop: 10,
    // padding: 10,
    backgroundColor: "#fff",
    borderRadius: '5@ms',
  },
  selectSportButtonText: {
    color: "#000",
    // fontWeight: "bold",
    fontSize: '16@ms',
    // borderBottomWidth: 1,
    borderBottomColor: "#000",
    fontFamily: "Lato-Bold"
    // fontWeight:500,
  },
  sportOptionsContainer: {
    marginTop: '10@vs',
    maxHeight: '600@vs'
  },
  sportOption: {
    paddingVertical: '10@vs',
    paddingHorizontal: '20@s',
  },
  circularImage: {
    alignItems: "center",
    display: "flex",
    flexDirection: "row",
  },
  image: {
    width: '40@s',
    height: '40@s',
    borderRadius: '25@s',
    marginRight: "10%"
  },
  sportOptionText: {
    color: "#000",
    fontSize: '16@ms',
    fontWeight: "500",
    fontFamily: "Lato-Bold"
  },
  drawerItem: {
    marginBottom: '10@vs',
    // fontWeight: "bold",
    fontSize: '16@ms',
    color: "#000",
    fontFamily: "Lato-Bold"
  },
  drawerSeparator: {
    borderBottomColor: "#fff",
    // borderBottomWidth: 1,
    marginBottom: '10@vs',
  },
  drawerLogo: {
    width: '100@s',
    height: '50@vs',
    resizeMode: "contain",
    marginVertical: '5@vs',
  },
  drawerHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
  },
  scrollView: {
    paddingBottom: '20@vs',
    paddingHorizontal: '10@s',
  },
});