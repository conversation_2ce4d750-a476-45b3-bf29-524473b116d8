import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

const CustomRadioButton = ({ value, status, onPress, color = '#000', label }) => {
  return (
    <TouchableOpacity style={styles.radioButtonContainer} onPress={() => onPress(value)}>
      <View style={[styles.radioButton, { borderColor: color }]}>
        {status && <View style={[styles.radioButtonInner, { backgroundColor: color }]} />}
      </View>
      <Text style={styles.radioButtonLabel}>{label}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  radioButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  radioButton: {
    height: 20,
    width: 20,
    borderRadius: 10,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 5,
  },
  radioButtonInner: {
    height: 10,
    width: 10,
    borderRadius: 5,
  },
  radioButtonLabel: {
    fontSize: 16,
  },
});

export default CustomRadioButton;
