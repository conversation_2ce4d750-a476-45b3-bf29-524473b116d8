import React, { useRef, useEffect, useState } from 'react';
import { View, FlatList, StyleSheet, NativeSyntheticEvent, NativeScrollEvent } from 'react-native';
import Image from '@d11/react-native-fast-image';
import { vs, s } from 'react-native-size-matters';

type AcademyImageGalleryProps = {
  images: string[];
  autoScroll?: boolean;
};

const GALLERY_HEIGHT = vs(219);
const IMAGE_HEIGHT = vs(144); // Reduced from vs(159)
const IMAGE_WIDTH = s(210); // Reduced from s(225)
const INDICATOR_WIDTH = s(24); // Increased from s(18)
const INDICATOR_HEIGHT = vs(3); // Increased from vs(1)
const GAP = s(12);
const AUTOSCROLL_INTERVAL = 2000;

const AcademyImageGallery: React.FC<AcademyImageGalleryProps> = ({ images}) => {
  const autoScroll = false; 
  const displayImages = images.slice(0, 5);
  const [activeIndex, setActiveIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);
  const timer = useRef<NodeJS.Timeout | null>(null);
  const currentIndexRef = useRef(0); // Track the current real index
  const isAutoScroll = autoScroll && displayImages.length > 1;
  const showIndicator = displayImages.length > 1;
  const canScroll = displayImages.length > 1;

  // Infinite scroll logic
  const getData = () => {
    if (displayImages.length === 1) return displayImages;
    // Duplicate first and last for infinite effect
    return [displayImages[displayImages.length - 1], ...displayImages, displayImages[0]];
  };
  const data = getData();

  // Initial index (1 if infinite, 0 if only 1 image)
  const initialIndex = displayImages.length === 1 ? 0 : 1;

  // Handle auto-scroll
  useEffect(() => {
    if (!isAutoScroll) {
      if (timer.current) {
        clearInterval(timer.current);
        timer.current = null;
      }
      return;
    }
    timer.current = setInterval(() => {
      // Calculate next index
      let nextIndex = currentIndexRef.current + 1;
      if (nextIndex >= displayImages.length) {
        nextIndex = 0;
      }
      // Scroll to the next real image (account for duplicated first item)
      flatListRef.current?.scrollToIndex({
        index: nextIndex + 1, // +1 for the duplicated first item
        animated: true,
      });
    }, AUTOSCROLL_INTERVAL);
    return () => {
      if (timer.current) clearInterval(timer.current);
      timer.current = null;
    };
    // eslint-disable-next-line
  }, [isAutoScroll, displayImages.length]);

  // Handle scroll event for infinite effect
  const onMomentumScrollEnd = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    if (!canScroll) return;
    let index = Math.round(event.nativeEvent.contentOffset.x / (IMAGE_WIDTH + GAP));
    if (index === 0) {
      // Scrolled to duplicate last, jump to real last
      flatListRef.current?.scrollToIndex({ index: displayImages.length, animated: false });
      setActiveIndex(displayImages.length - 1);
      currentIndexRef.current = displayImages.length - 1;
    } else if (index === displayImages.length + 1) {
      // Scrolled to duplicate first, jump to real first
      flatListRef.current?.scrollToIndex({ index: 1, animated: false });
      setActiveIndex(0);
      currentIndexRef.current = 0;
    } else {
      setActiveIndex(index - 1);
      currentIndexRef.current = index - 1;
    }
  };

  // Handle scroll event for indicator sync
  const onScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    if (!canScroll) return;
    const offsetX = event.nativeEvent.contentOffset.x;
    const index = Math.round(offsetX / (IMAGE_WIDTH + GAP));
    // Clamp index to valid range for infinite scroll
    if (index === 0) {
      setActiveIndex(displayImages.length - 1);
    } else if (index === displayImages.length + 1) {
      setActiveIndex(0);
    } else {
      setActiveIndex(index - 1);
    }
  };

  // On mount, scroll to initial index
  useEffect(() => {
    if (flatListRef.current && canScroll) {
      setTimeout(() => {
        flatListRef.current?.scrollToIndex({ index: initialIndex, animated: false });
        setActiveIndex(0);
        currentIndexRef.current = 0;
      }, 10);
    }
    // eslint-disable-next-line
  }, [displayImages.length, canScroll]);

  // Render image item
  const renderItem = ({ item }: { item: string }) => (
    <View style={{ width: IMAGE_WIDTH, height: IMAGE_HEIGHT, borderRadius: s(6), overflow: 'hidden' }}>
      <Image source={{ uri: item }} style={{ width: '100%', height: '100%' }} resizeMode="cover" />
    </View>
  );

  return (
    <View style={styles.galleryContainer}>
      {/* Images Container */}
      <View style={styles.imagesContainer}>
        <FlatList
          ref={flatListRef}
          data={data}
          renderItem={renderItem}
          keyExtractor={(_, idx) => idx.toString()}
          horizontal
          showsHorizontalScrollIndicator={false}
          pagingEnabled={false}
          snapToInterval={IMAGE_WIDTH + GAP}
          decelerationRate="fast"
          getItemLayout={(_, index) => ({
            length: IMAGE_WIDTH + GAP,
            offset: (IMAGE_WIDTH + GAP) * index,
            index,
          })}
          contentContainerStyle={{ gap: GAP, paddingHorizontal: s(16) }}
          onMomentumScrollEnd={onMomentumScrollEnd}
          onScroll={onScroll}
          scrollEventThrottle={16}
          scrollEnabled={canScroll}
        />
      </View>
      {/* Indicator Container */}
      {showIndicator && (
        <View style={styles.indicatorContainer}>
          {displayImages.map((_, idx) => (
            <View
              key={idx}
              style={[
                styles.indicator,
                { backgroundColor: activeIndex === idx ? '#000000' : '#00000066' },
              ]}
            />
          ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  galleryContainer: {
    height: GALLERY_HEIGHT,
    justifyContent: 'space-between',
    paddingVertical: vs(16),
  },
  imagesContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: GAP,
    marginTop: vs(12),
  },
  indicator: {
    width: INDICATOR_WIDTH,
    height: INDICATOR_HEIGHT,
    borderRadius: vs(1),
  },
});

export default AcademyImageGallery; 