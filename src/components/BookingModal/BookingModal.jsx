import React, { useEffect, useState } from "react";
import {
  Modal,
  View,
  Text,
  Button,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  SafeAreaView, // <-- Add SafeAreaView
} from "react-native";
import CustomCalendar from "../Calendar";
import { NEXT_PUBLIC_BASE_URL } from "@env";
import axios from "axios";
import CalendarPicker from "react-native-calendar-picker";
import TimePicker from "../../screens/TimePicker";
import { useAuth } from "../../Context/AuthContext";
import { styles } from "./bookingModalStyling";

const BookingModal = ({
  dates,
  isVisible,
  onClose,
  coachId,
  coach_id,
  setSelectedSlots,
  selectedSlots,
  totalPrice,
  setTotalPrice,
  fees,
  setModalVisible,
  course,
}) => {
  const { userToken } = useAuth();
  const [date, setDate] = useState(new Date());
  const [selectedDateSlot, setSelectedDateSlot] = useState(new Date());
  const [time, setTime] = useState("");
  const [openDatePicker, setOpenDatePicker] = useState(false);
  const [openTimePicker, setOpenTimePicker] = useState(false);
  const [selectedDuration, setSelectedDuration] = useState("");
  const [showTimeSlotWarning, setShowTimeSlotWarning] = useState(false);
  const [formatDate, setFormatDate] = useState("");
  const [eventsTime, setEventsTime] = useState([]);
  const [selectedAmPm, setSelectedAmPm] = useState("AM");
  const [bookedSlot, setBookedSlot] = useState(null);
  const [selectedTime, setSelectedTime] = useState(null);
  const [slotTiming, setSlotTiming] = useState("HH:MM");
  const [timePickerModal, setTimePickerModal] = useState(false);
  const [dateChanged, setDateChanged] = useState(false);

  const handleSelectTime = (time, duration) => {
    // Pass the selected duration to the overlap check
    const { overlap, message } = checkForTimeOverlap(formatTime12Hour(time), date, duration);
    if (overlap) {
      Alert.alert("Warning", message);
      setSelectedTime(null); // Clear selection if overlap
    } else {
      formatTime12HourFinal(time)
    }
  };

  const resetState = () => {
    setSlotTiming("HH:MM");
    setDateChanged(false);
    setDate(new Date());
    setSelectedDateSlot(new Date());
    setTime("");
    setSelectedTime(null);
    setSelectedDuration("");
    setOpenDatePicker(false);
    setOpenTimePicker(false);
    setTimePickerModal(false);
    setShowTimeSlotWarning(false);
    setFormatDate("");
    setEventsTime([]);
    setSelectedAmPm("AM");
    setBookedSlot(null);
  };
  
  // Reset all states when modal opens
  useEffect(() => {
    if (isVisible) {
      resetState();
    }
  }, [isVisible]);
  
  const formatTime12Hour = (date) => {
    let hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours || 12; // Convert '0' hour to '12'
    const minutesFormatted = minutes < 10 ? `0${minutes}` : minutes;
    timeSlotSelected = `${hours}:${minutesFormatted} ${ampm}`;
    return `${hours}:${minutesFormatted} ${ampm}`;
  };
  const formatTime12HourFinal = (date) => {
    let hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours || 12; // Convert '0' hour to '12'
    const minutesFormatted = minutes < 10 ? `0${minutes}` : minutes;
    timeSlotSelected = `${hours}:${minutesFormatted} ${ampm}`;
    setSlotTiming(timeSlotSelected)
    setTime(timeSlotSelected)
    return `${hours}:${minutesFormatted} ${ampm}`;
  };
  const startDate = dates.startDate;
  const endDate = dates.endDate;
  const [bookings, setBookings] = useState([]);
  const fetchBooking = async () => {
    try {
      const requestOptions = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userToken}`,
        },
      };
      const response = await axios.get(`${NEXT_PUBLIC_BASE_URL}/api/booking/`, requestOptions);
      setBookings(response.data);
    } catch (error) {
      console.log("Error fetching data: 99");
    }
  };
  useEffect(() => {
    fetchBooking();
  }, []);

  const getDisabledDates = () => {
    const startDate = new Date(dates.startDate);
    const endDate = new Date(dates.endDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const days = dates?.days; // ensure this is provided
    const dayOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const disabledDays = dayOfWeek.filter((day) => !days.includes(day));

    const disabledDates = [];

    let currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      // Disable dates that are either in the past or on disabled days of the week
      if (currentDate < today || disabledDays.includes(dayOfWeek[currentDate.getDay()])) {
        disabledDates.push(new Date(currentDate));
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }
    return disabledDates;
  };

  let event = [];

  const formatTime = (isoString) => {
    const date = new Date(isoString);
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    return `${hours}:${minutes}`;
  };
  const bookedSlots = eventsTime.map((event) => ({

    start: formatTime(event.start),
    end: formatTime(event.end),
    title: event.title,
  }));

  useEffect(() => {
    setBookedSlot(bookedSlots);
  }, [eventsTime])
  const handleOpenDatePicker = () => {
    setOpenDatePicker(!openDatePicker);
    if (openTimePicker) {
      setOpenTimePicker(false);
      setTimePickerModal(false);
    }
  };

  const handleSaveDate = () => {
    setOpenDatePicker(false);
  };

  const handleOpenTimePicker = () => {
    if (dateChanged) {
      setOpenTimePicker(true);
      setTimePickerModal(true);
      if (openDatePicker) setOpenDatePicker(false);
    } else {
      Alert.alert("Error", "Please select date first.");
    }
  };
  const handleCloseTimePicker = () => {
    setTimePickerModal(false)
    // Don't clear selectedTime so it shows when reopening
  }
  
  const handleTimeDateState = () => {
    setModalVisible(false);
    setDate(new Date());
    setSelectedDateSlot(new Date())
    setTime("");
    setSelectedDuration("");
  };
  
  
  const handleBookSlot = () => {
    // 1. Check all required fields
    if (!date || !time) {
      Alert.alert("Error", "Please select both date and time.");
      return;
    }
    if (!selectedDuration) {
      Alert.alert("Warning", "Please select the duration");
      return;
    }
    // 2. Check for overlap with booked and recently added slots
    const { overlap, message } = checkForTimeOverlap(time, date, selectedDuration);
    if (overlap) {
      Alert.alert("Warning", message);
      return;
    }
    // 3. Check if selected time + duration exceeds the available window
    const convertTo24Hour = (time12Hour) => {
      const [timeStr, ampm] = time12Hour.split(' ');
      const [hours, minutes] = timeStr.split(':').map(Number);
      let hour24 = hours;
      if (ampm === 'PM' && hours !== 12) hour24 = hours + 12;
      if (ampm === 'AM' && hours === 12) hour24 = 0;
      return hour24 * 60 + minutes;
    };
    const selectedTimeInMinutes = convertTo24Hour(time);
    let durationMinutes = 0;
    if (selectedDuration === '30min') durationMinutes = 30;
    if (selectedDuration === '45min') durationMinutes = 45;
    if (selectedDuration === '1hr') durationMinutes = 60;
    const endTimeInMinutes = parseInt(dates.endTime.split(':')[0], 10) * 60 + parseInt(dates.endTime.split(':')[1], 10);
    if (selectedTimeInMinutes + durationMinutes > endTimeInMinutes) {
      Alert.alert("Warning", "The selected time and duration exceed the available booking window. Please select a shorter duration or an earlier time.");
      return;
    }
    const parts = date.split('-');
    const formattedDate = `${parts[2]}-${parts[1]}-${parts[0]}`;
    const d = new Date(`${parts[2]}-${parts[1]}-${parts[0]}T00:00:00Z`);
    const ds = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const dow = ds[d.getUTCDay()];
    let duration;
    let endDateTime;
    if (selectedDuration === "45min") {
      endDateTime = addMinutes(time, 45);
      duration = "45 mins";
    } else if (selectedDuration === "30min") {
      endDateTime = addMinutes(time, 30);
      duration = "30 mins";
    } else {
      endDateTime = addHours(time, 1);
      duration = "60 mins";
    }
    let price = 0;
    if (selectedDuration === "30min") {
      price = fees?.fees30;
    } else if (selectedDuration === "45min") {
      price = fees?.fees45;
    } else {
      price = fees?.fees60;
    }
    const newBookedSlot = {
      date: formattedDate,
      duration: duration,
      start: time,
      end:
        selectedDuration === "45min"
          ? addMinutes(time, 45)
          : selectedDuration === "30min"
            ? addMinutes(time, 30)
            : addHours(time, 1),
      days: dow,
      fees: price
    };
    setSelectedSlots([...selectedSlots, newBookedSlot]);
    setTotalPrice((prevTotalPrice) => prevTotalPrice + price);
    handleTimeDateState();
    setSlotTiming("HH:MM")
  };
  
  const addMinutes = (time, minutesToAdd) => {
    const [hourStr, minuteStr, ampm] = time.split(/:| /);
    let hours = parseInt(hourStr);
    let minutes = parseInt(minuteStr);

    // Convert the hour to a 24-hour format for easier manipulation.
    if (ampm === "PM" && hours !== 12) {
      hours += 12;
    } else if (ampm === "AM" && hours === 12) {
      hours = 0;  // Midnight is 0 hours in 24-hour time.
    }

    // Calculate total minutes after addition
    let totalMinutes = hours * 60 + minutes + minutesToAdd;

    // Convert back to hours and minutes
    hours = Math.floor(totalMinutes / 60) % 24;  // Get hour in 24-hour format.
    minutes = totalMinutes % 60;

    // Determine AM or PM and convert hour back to 12-hour format.
    let newAmPm = hours >= 12 ? "PM" : "AM";
    hours = hours % 12;
    if (hours === 0) hours = 12;  // Adjust the 12-hour format specifics.

    const newTime = `${hours}:${minutes < 10 ? "0" + minutes : minutes} ${newAmPm}`;
    return newTime;
  };



  const addHours = (time, hours) => {
    const [selectedHour, selectedMinute] = time.split(":");
    const newTime = `${parseInt(selectedHour) + hours}:${selectedMinute}`;
    return newTime;
  };
  const hours = Array.from({ length: 24 }, (_, i) =>
    i < 10 ? `0${i}` : `${i}`
  );
  const minutes = Array.from({ length: 60 }, (_, i) =>
    i < 10 ? `0${i}` : `${i}`
  );

  // === RESTORED HELPER FUNCTIONS ===
  // Converts 24-hour time (e.g. "13:30") to 12-hour AM/PM (e.g. "1:30 PM")
  const convertToAmPm = (time) => {
    if (!time) return '';
    const [hours, minutes] = time.split(":").map(Number);
    const period = hours >= 12 ? "PM" : "AM";
    const hours12 = hours % 12 || 12;
    return `${hours12}:${minutes < 10 ? "0" : ""}${minutes} ${period}`;
  };

  const [selectedHour, selectedMinute] = time.split(":");
  const [timeHour, setTimeHour] = useState();
  const [timeMinute, setTimeMinute] = useState();
  useEffect(() => {
    if (timeHour) {
      setTime(timeHour)
    }
    if (timeHour && timeMinute) {
      const selectedTimezone = `${timeHour}:${timeMinute}`;
      const timeIs = isTimeEditable(timeHour, timeMinute);
      if (timeIs) {
        setTime(selectedTimezone)
      } else {
        Alert.alert("Selected time is outside the allowed range");
      }
    }
  }, [timeHour, timeMinute])

  const isTimeEditable = (timeHour, timeMinute) => {

    const [startHour, startMinute] = dates.startTime.split(":").map(Number);
    const [endHour, endMinute] = dates.endTime.split(":").map(Number);

    const currentTimeInMinutes = (Number(timeHour) * 60) + Number(timeMinute);
    const startTimeInMinutes = (Number(startHour) * 60) + Number(startMinute);
    const endTimeInMinutes = (Number(endHour) * 60) + Number(endMinute);

    return (
      currentTimeInMinutes >= startTimeInMinutes &&
      currentTimeInMinutes <= endTimeInMinutes
    );
  };


  const startTimeAmPm = convertToAmPm(dates.startTime);
  const endTimeAmPm = convertToAmPm(dates.endTime);
  const onDateChange = (date) => {
    setSelectedDateSlot(date)
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear();
    const formattedDate = `${day}-${month}-${year}`;
    const formattedDateTwo = `${year}/${month}/${day}`;
    setDate(formattedDate);
    setDateChanged(true);
    handleDateValue(formattedDateTwo);
  };
  const [isTimePickerVisible, setTimePickerVisibility] = useState(false);
  
  
  const handleDateValue = (value) => {
    let newDate;
    if (value) {
      const [year, month, day] = value.split("/");
      newDate = `${year}-${month}-${day}`;
    }
    const myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");
    myHeaders.append("Authorization", `Bearer ${userToken}`);
    const raw = JSON.stringify({
      coachId: coachId,
      startDate: `${newDate}T00:00:00`,
      endDate: `${newDate}T23:59:00`,
      // email: coach_id?.googleEmail,
      courseId: course?._id,
    });
    const requestOptions = {
      method: "POST",
      headers: myHeaders,
      body: raw,
      redirect: "follow",
    };

    fetch(`${NEXT_PUBLIC_BASE_URL}/api/booking/events`, requestOptions)
      .then((response) => response.json())
      .then((result) => {
        const events = result ? result?.map((event) => {
          const startDate = new Date(date);
          const startTime = event.startTime;
          const [startHour, startMinute] = startTime?.split(":").map(Number);
          startDate.setHours(startHour, startMinute, 0, 0);
          const endDate = new Date(date);
          const endTime = event.endTime;
          const [endHour, endMinute] = endTime?.split(":").map(Number);
          endDate.setHours(endHour, endMinute, 0, 0);

          return {
            start: startDate,
            end: endDate,
          };
        })
          : null;
        setEventsTime(events);
      })
      .catch((error) => console.error(error));
  };
  


  const checkForTimeOverlap = (selectedTime, selectedDate, duration) => {
    const formattedSelectedDate = formatDateToMatch(selectedDate);
    
    // Convert selected time to 24-hour format for comparison
    const convertTo24Hour = (time12Hour) => {
      const [time, ampm] = time12Hour.split(' ');
      const [hours, minutes] = time.split(':').map(Number);
      let hour24 = hours;
      
      if (ampm === 'PM' && hours !== 12) {
        hour24 = hours + 12;
      } else if (ampm === 'AM' && hours === 12) {
        hour24 = 0;
      }
      
      return hour24 * 60 + minutes; // Return minutes for easier comparison
    };
    
    const selectedTimeInMinutes = convertTo24Hour(selectedTime);
    
    // Calculate end time based on selected duration
    let selectedEndTimeInMinutes;
    if (duration === "30min") {
      selectedEndTimeInMinutes = selectedTimeInMinutes + 30;
    } else if (duration === "45min") {
      selectedEndTimeInMinutes = selectedTimeInMinutes + 45;
    } else if (duration === "1hr") {
      selectedEndTimeInMinutes = selectedTimeInMinutes + 60;
    } else {
      // If no duration selected, assume 30 minutes minimum
      selectedEndTimeInMinutes = selectedTimeInMinutes + 30;
    }
    
    console.log('=== OVERLAP DEBUG ===');
    console.log('Selected time:', selectedTime, 'Duration:', duration);
    console.log('Selected start minutes:', selectedTimeInMinutes, 'Selected end minutes:', selectedEndTimeInMinutes);
    
    // Check overlap with recently selected slots (not yet booked)
    const selectedDateSlots = selectedSlots.filter(slot => slot.date === formattedSelectedDate);
    const overlapsWithSelected = selectedDateSlots.some(slot => {
      const slotStartMinutes = convertTo24Hour(slot.start);
      const slotEndMinutes = convertTo24Hour(slot.end);
      
      console.log('Checking selected slot:', slot.start, 'to', slot.end);
      console.log('Slot start minutes:', slotStartMinutes, 'Slot end minutes:', slotEndMinutes);
      
      // Check if the new booking overlaps with existing slot
      const overlaps = (selectedTimeInMinutes < slotEndMinutes && selectedEndTimeInMinutes > slotStartMinutes);
      console.log('Overlaps with selected:', overlaps);
      return overlaps;
    });
    
    // Check overlap with already booked slots
    const overlapsWithBooked = bookedSlot && bookedSlot.some(slot => {
      const slotStartMinutes = convertTo24Hour(formatTimeWithAmPm(slot.start));
      const slotEndMinutes = convertTo24Hour(formatTimeWithAmPm(slot.end));
      
      console.log('Checking booked slot:', slot.start, 'to', slot.end);
      console.log('Booked slot start minutes:', slotStartMinutes, 'Booked slot end minutes:', slotEndMinutes);
      
      // Check if the new booking overlaps with booked slot
      const overlaps = (selectedTimeInMinutes < slotEndMinutes && selectedEndTimeInMinutes > slotStartMinutes);
      console.log('Overlaps with booked:', overlaps);
      return overlaps;
    });
    
    console.log('Final result - overlapsWithSelected:', overlapsWithSelected, 'overlapsWithBooked:', overlapsWithBooked);
    console.log('=== END DEBUG ===');
    
    if (overlapsWithSelected) {
      return { overlap: true, message: "The selected time and duration overlap with a recently booked slot." };
    } else if (overlapsWithBooked) {
      return { overlap: true, message: "The selected time and duration overlap with a slot which is already booked for coach." };
    } else {
      return { overlap: false, message: "" }; // No overlap
    }
  };
  const formatDateToMatch = (dateString) => {
    if (!dateString) return '';
    if (typeof dateString === 'string') {
      if (dateString.includes('-')) {
        const parts = dateString.split('-');
        if (parts.length === 3) {
          // If already in YYYY-MM-DD, return as is
          if (parts[0].length === 4) return dateString;
          // Else, assume DD-MM-YYYY
          return `${parts[2]}-${parts[1]}-${parts[0]}`;
        }
      }
      // handle other string formats if needed
      return dateString;
    }
    if (dateString instanceof Date) {
      // Convert Date object to 'YYYY-MM-DD'
      const year = dateString.getFullYear();
      const month = (dateString.getMonth() + 1).toString().padStart(2, '0');
      const day = dateString.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
    return '';
  };
  const formatTimeWithAmPm = (time) => {
    const [hours, minutes] = time.split(':');
    const hoursInt = parseInt(hours, 10);
    const suffix = hoursInt >= 12 ? 'PM' : 'AM';
    const formattedHours = ((hoursInt + 11) % 12 + 1);
    return `${formattedHours}:${minutes} ${suffix}`;
  };
  // Helper to check if a duration button should be disabled
  const getDurationDisabledState = (duration) => {
    if (!slotTiming || slotTiming === 'HH:MM') return true; // No time selected
    if (duration === '45min') return false;
    // Check if selected time + duration exceeds the available window
    const convertTo24Hour = (time12Hour) => {
      const [time, ampm] = time12Hour.split(' ');
      const [hours, minutes] = time.split(':').map(Number);
      let hour24 = hours;
      if (ampm === 'PM' && hours !== 12) {
        hour24 = hours + 12;
      } else if (ampm === 'AM' && hours === 12) {
        hour24 = 0;
      }
      return hour24 * 60 + minutes;
    };
    const selectedTimeInMinutes = convertTo24Hour(slotTiming);
    let durationMinutes = 0;
    if (duration === '30min') durationMinutes = 30;
    if (duration === '1hr') durationMinutes = 60;
    const endTimeInMinutes = parseInt(dates.endTime.split(':')[0], 10) * 60 + parseInt(dates.endTime.split(':')[1], 10);
    if (selectedTimeInMinutes + durationMinutes > endTimeInMinutes) return true;
    // Use the overlap check logic
    const { overlap } = checkForTimeOverlap(slotTiming, date, duration);
    return overlap;
  };

// Helper to convert 12-hour time with AM/PM to 24-hour HH:mm
const to24Hour = (time12) => {
  if (!time12) return '';
  const [time, ampm] = time12.split(' ');
  let [hours, minutes] = time.split(':').map(Number);
  if (ampm === 'PM' && hours !== 12) hours += 12;
  if (ampm === 'AM' && hours === 12) hours = 0;
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
};

  useEffect(() => {
    if (selectedDuration && getDurationDisabledState(selectedDuration)) {
      setSelectedDuration("");
    }
  }, [slotTiming, selectedDuration]);

  // Helper to convert date string to comparable format (YYYY-MM-DD)
  const normalizeDate = (dateStr) => {
    if (!dateStr || typeof dateStr !== 'string') return '';
    // Accepts 'DD-MM-YYYY' or 'YYYY-MM-DD' or 'YYYY/MM/DD'
    if (dateStr.includes('-')) {
      const parts = dateStr.split('-');
      if (parts[0].length === 4) return dateStr; // already YYYY-MM-DD
      // else DD-MM-YYYY
      return `${parts[2]}-${parts[1]}-${parts[0]}`;
    } else if (dateStr.includes('/')) {
      const parts = dateStr.split('/');
      return `${parts[0]}-${parts[1]}-${parts[2]}`;
    }
    return dateStr;
  };

  // Prepare cart slots for the selected date
  const cartSlotsForDate = selectedSlots
    .filter(slot => normalizeDate(slot.date) === normalizeDate(date))
    .map(slot => ({
      ...slot,
      start: to24Hour(slot.start),
      end: to24Hour(slot.end)
    }));

  // Merge server and cart slots for disabling
  const allDisabledSlots = [
    ...bookedSlots, // already for the selected date
    ...cartSlotsForDate
  ];

  return (
    <ScrollView>
      <Modal
        animationType="slide"
        transparent={false}
        visible={isVisible}
        onRequestClose={onClose}
      >
        <SafeAreaView style={{ flex: 1 }}>
          <View style={styles.fullScreenView}>
            <View style={styles.datePickerSection}>
              <View style={styles.inputLabel}>
                <Text style={{ color: "#000" }}>Choose Date</Text>
              </View>
              <TextInput
                style={styles.input}
                placeholder="DD-MM-YYYY"
                value={date}
                editable={false}
                placeholderTextColor="#9CA3AF"
                onPressIn={handleOpenDatePicker}
              />
              <Text style={{ color: "#64b5f6", fontWeight: "500", marginBottom: "5%", marginLeft: "4%", }}>
                The available time slots are between {dates.days.join("-")} from{" "}
                {startTimeAmPm} to {endTimeAmPm}
              </Text>
            </View>
            {openDatePicker && (
              <View>
                <CalendarPicker
                  onDateChange={onDateChange}
                  selectedStartDate={selectedDateSlot}
                  disabledDates={getDisabledDates()}
                  minDate={startDate}
                  maxDate={endDate}
                  previousTitleStyle={{ color: "#000" }}
                  nextTitleStyle={{ color: "#000" }}
                />
                <View style={styles.buttonContainer}>
                  <Button
                    title="Save Date"
                    onPress={() => setOpenDatePicker(false)}
                  />
                </View>
              </View>
            )}
            <View>
              <View style={styles.inputLabel}>
                <Text style={{ color: "#000" }}>Choose Time</Text>
              </View>
              <TextInput
                style={styles.input}
                placeholder="HH:MM"
                value={slotTiming}
                placeholderTextColor="#9CA3AF"
                editable={false}
                onPressIn={handleOpenTimePicker}
              />
            </View>
            {openTimePicker && (
              <View style={styles.timePickerModal}>
                <TimePicker
                  key={selectedSlots.map(s => s.start + s.end + s.date).join('-') + '-' + date}
                  selectedTime={selectedTime}
                  setSelectedTime={setSelectedTime}
                  startTime={dates.startTime}
                  endTime={dates.endTime}
                  handleSelectTime={handleSelectTime}
                  handleCloseTimePicker={handleCloseTimePicker}
                  timePickerModal={timePickerModal}
                  setTimePickerModal={setTimePickerModal}
                  bookedSlots={allDisabledSlots}
                  date={date}
                  selectedDateSlot={selectedDateSlot}
                  selectedDuration={selectedDuration}
                />
                {showTimeSlotWarning && (
                  <Text style={styles.warningText}>
                    The selected time slot is already booked. Please choose another time.
                  </Text>
                )}
              </View>
            )}
            <View style={styles.durationContainer}>
              <View style={styles.durationSelector}>
                <TouchableOpacity
                  disabled={fees.fees30 <= 0 || getDurationDisabledState('30min')}
                  style={[
                    styles.durationButton,
                    { opacity: fees.fees30 <= 0 || getDurationDisabledState('30min') ? 0.5 : 1 },
                    selectedDuration === "30min" && styles.selectedDurationButton,
                  ]}
                  onPress={() => setSelectedDuration("30min")}
                >
                  <Text style={[styles.durationButtonText, selectedDuration === "30min" && styles.selectedDurationButtonText]}>30 mins</Text>
                  <Text style={[styles.durationButtonText, selectedDuration === "30min" && styles.selectedDurationButtonText]}> ₹{fees?.fees30 ? fees?.fees30 : "N/A"}</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  disabled={fees.fees45 <= 0}
                  style={[
                    styles.durationButton,
                    { opacity: fees.fees45 <= 0 ? 0.5 : 1 },
                    selectedDuration === "45min" && styles.selectedDurationButton,
                  ]}
                  onPress={() => setSelectedDuration("45min")}
                >
                  <Text style={[styles.durationButtonText, selectedDuration === "45min" && styles.selectedDurationButtonText]}>45 mins</Text>
                  <Text style={[styles.durationButtonText, selectedDuration === "45min" && styles.selectedDurationButtonText]}> ₹ {fees?.fees45 ? fees?.fees45 : "N/A"}</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  disabled={fees.fees60 <= 0 || getDurationDisabledState('1hr')}
                  style={[
                    styles.durationButton,
                    { opacity: fees.fees60 <= 0 || getDurationDisabledState('1hr') ? 0.5 : 1 },
                    selectedDuration === "1hr" && styles.selectedDurationButton,
                  ]}
                  onPress={() => setSelectedDuration("1hr")}
                >
                  <Text style={[styles.durationButtonText, selectedDuration === "1hr" && styles.selectedDurationButtonText]}>1 hour</Text>
                  <Text style={[styles.durationButtonText, selectedDuration === "1hr" && styles.selectedDurationButtonText]}> ₹ {fees?.fees60 ? fees.fees60 : "N/A"}</Text>
                </TouchableOpacity>
              </View>
            </View>
            <CustomCalendar bookedSlots={bookedSlots} />
            <View style={styles.buttonContainer}>
              <Button title="Book Slot" onPress={handleBookSlot} />
              <Button title="Close" onPress={handleTimeDateState} />
            </View>
          </View>
        </SafeAreaView>
      </Modal>
    </ScrollView>
  );
};


export default BookingModal;