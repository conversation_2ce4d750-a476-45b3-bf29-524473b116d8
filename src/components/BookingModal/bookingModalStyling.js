import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
    fullScreenView: {
      flex: 1,
      padding: 20,
    },
    card: {
      backgroundColor: "#FAFBFCFF",
      width: "100%",
      padding: 10,
      borderRadius: 10,
      marginBottom: 10,
      elevation: 1,
    },
    input: {
      margin: 12,
      borderWidth: 1,
      padding: 10,
      color: "#000",
    },
    inputLabel: {
      color: "#000",
      fontSize: 16,
      fontWeight: "500"
    },
    datePickerSection: {
      color: "#000",
    },
    buttonContainer: {
      justifyContent: "space-around",
      flexDirection: "row",
      alignItems: "center",
      marginVertical: "5%",
    },
    timePickerContainer: {
      flexDirection: "column",
      alignItems: "center",
      justifyContent: "center",
      color: "#000",
    },
    timePicker: {
      flexDirection: "row",
      justifyContent: "center",
      marginBottom: 20,
      color: "#000",
    },
    numberSelector: {
      height: 200,
      width: 100,
      color: "#000",
    },
    numberItem: {
      alignItems: "center",
      justifyContent: "center",
      height: 40,
      color: "#000",
    },
    selectedNumberItem: {
      backgroundColor: "#ddd",
    },
    numberItemText: {
      fontSize: 18,
      color: "#000",
    },
    selectedNumberItemText: {
      fontSize: 18,
      color: "blue",
    },
    warningText: {
      color: "red",
      marginTop: 10,
      textAlign: "center",
    },
    durationContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-evenly",
      marginTop: 10,
      marginBottom: 10,
    },
    durationLabel: {
      fontSize: 16,
      fontWeight: "bold",
    },
    durationSelector: {
      flexDirection: "row",
    },
    durationButton: {
      paddingHorizontal: 15,
      paddingVertical: 5,
      borderRadius: 7,
      marginHorizontal: 5,
      borderColor: "#000",
      borderWidth: 1,
    },
    selectedDurationButton: {
      backgroundColor: "#64b5f6",
      color: "#fff"
    },
    durationButtonText: {
      color: "#000",
      fontSize: 14,
    },
    selectedDurationButtonText: {
      color: "#fff",
      fontSize: 14,
    },
    timePickerModal: {
      position: "absolute"
    }
  });