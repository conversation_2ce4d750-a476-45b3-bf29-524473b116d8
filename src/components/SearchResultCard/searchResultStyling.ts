import { ScaledSheet } from "react-native-size-matters";

export const styles = ScaledSheet.create({
    cardContainer: {
      width: '100%',
      flexDirection: 'row',
      alignItems: 'flex-start',
      backgroundColor: '#fff',
      paddingVertical: '12@vs',
      paddingHorizontal: '10@ms',
      marginBottom: '10@vs',
      borderBottomWidth: 1,
      borderBottomColor: '#E2E8F0',
    },
    leftContainer: {
      width: '60@s',
      height: '60@s',
      marginRight: '12@ms',
      borderRadius: '6@s',
      overflow: 'hidden',
      backgroundColor: '#eee',
      justifyContent: 'center',
      alignItems: 'center',
    },
    image: {
      width: '60@s',
      height: '60@s',
      borderRadius: '6@s',
    },
    placeholderImage: {
      width: '60@s',
      height: '60@s',
      borderRadius: '6@s',
      backgroundColor: '#ccc',
      justifyContent: 'center',
      alignItems: 'center',
    },
    placeholderText: {
      color: '#888',
      fontSize: '12@ms',
    },
    rightContainer: {
      flex: 1,
      flexDirection: 'column',
      justifyContent: 'flex-start',
      alignItems: 'flex-start',
    },
    topContent: {
      width: '100%',
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: '4@vs',
    },
    title: {
      fontSize: '16@ms',
      fontWeight: 'bold',
      color: '#222',
      fontFamily: 'Lato-Bold',
      textAlign: 'left',
    },
    price: {
      color: '#00AEEF',
      fontSize: '15@ms',
      fontWeight: 'bold',
      marginTop: '2@vs',
    },
  });