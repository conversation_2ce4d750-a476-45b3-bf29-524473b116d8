import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ViewStyle, StyleProp } from 'react-native';
import { styles } from './searchResultStyling';

// Define the union type for card data
export type SearchResultCardData =
  | { type: 'course'; courseName: string; images?: any[]; price?: number; onPress?: () => void }
  | { type: 'coach'; coachName: string; lastName?: string; image?: any; onPress?: () => void }
  | { type: 'academy'; name: string; image: any; onPress?: () => void };

interface SearchResultCardProps {
  data: SearchResultCardData;
  styleOverrides?: StyleProp<ViewStyle>;
}
import Image from '@d11/react-native-fast-image';



const SearchResultCard: React.FC<SearchResultCardProps> = ({ data, styleOverrides }) => {
  const [pressed, setPressed] = useState(false);
  // console.log("Search Result Card Data: -------------->\n\n\n\n\n", data);
  let image, title, price, onPress;
  
  // Debug logging for academy images
  if (data.type === 'academy') {
    console.log("Academy image data:", {
      type: data.type,
      name: data.name,
      image: data.image,
      hasImage: !!data.image,
      imageUri: data.image?.uri
    });
  }
  if (data.type === 'course') {
    image = data.images && data.images[0]?.url ? { uri: data.images[0].url } : undefined;
    title = data.courseName;
    price = data.price;
    onPress = data.onPress;
  } else if (data.type === 'coach') {
    image = data.image;
    title = data.coachName + (data.lastName ? ' ' + data.lastName : '');
    onPress = data.onPress;
  } else if (data.type === 'academy') {
    image = data.image;
    title = data.name;
    onPress = data.onPress;
  }
  return (
    <TouchableOpacity
      style={[styles.cardContainer, pressed && { backgroundColor: '#E2E8F0' }, styleOverrides]}
      onPress={onPress}
      onPressIn={() => setPressed(true)}
      onPressOut={() => setPressed(false)}
      activeOpacity={0.8}
      disabled={!onPress}
    >
      <View style={styles.leftContainer}>
        {image ? (
          <Image 
            source={image} 
            style={styles.image} 
            resizeMode="cover"
            onError={() => {
              console.log("Image loading error for:", image.uri);
            }}
            onLoad={() => {
              console.log("Image loaded successfully:", image.uri);
            }}
          />
        ) : (
          <View style={styles.placeholderImage}>
            <Text style={styles.placeholderText}>No Image</Text>
          </View>
        )}
      </View>
      <View style={styles.rightContainer}>
        <View style={styles.topContent}>
          <Text style={styles.title} numberOfLines={1}>{title}</Text>
        </View>
        {/* Only show price for courses */}
        {data.type === 'course' && price !== undefined && (
          <Text style={styles.price}>
            ₹{typeof price === 'number' ? price.toLocaleString('en-IN') : price}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );
};

export default SearchResultCard; 