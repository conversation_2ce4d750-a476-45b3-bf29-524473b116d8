import React, { useEffect, useState } from 'react';
import { View, FlatList, Text, StyleSheet, ActivityIndicator } from 'react-native';
import TopAcademyCard from '../TopAcademyCard/TopAcademyCard.jsx';
import axios from 'axios';
import { NEXT_PUBLIC_BASE_URL } from '@env';

const TopAcademies = () => {
  const [academies, setAcademies] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAcademies = async () => {
      try {
        const response = await axios.get(`${NEXT_PUBLIC_BASE_URL}/api/cms/academy`);
        const sorted = response.data.sort((a, b) => a.position - b.position);
        console.log("Acamdemy ----------------------->\n\n\n\n\n\n\n", sorted[0]);
        setAcademies(sorted);
      } catch (error) {
        setAcademies([]);
      } finally {
        setLoading(false);
      }
    };
    fetchAcademies();
  }, []);

  const getAcademyImage = (academy) => {
    // Priority: profileImg > profileImage > academyImages[0] > placeholder
    if (academy.profileImg) {
      return { uri: academy.profileImg };
    }
    
    if (academy.profileImage) {
      return { uri: academy.profileImage };
    }
    
    if (academy.academyImages && Array.isArray(academy.academyImages) && academy.academyImages.length > 0) {
      return { uri: academy.academyImages[0] };
    }
    
    // Return null for placeholder - will be handled in TopAcademyCard
    return null;
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Text style={styles.heading}>Top Academies</Text>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  if (!academies.length) return null;

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>Top Academies</Text>
      <FlatList
        data={academies}
        keyExtractor={item => item._id}
        renderItem={({ item }) => {
          const academy = typeof item.academy === 'object' ? item.academy : {};
          const image = getAcademyImage(academy);
          
          return (
            <TopAcademyCard
              image={image}
              academyImages={academy.academyImages}
              title={academy.name || ''}
              academyId={academy._id}
            />
          );
        }}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ gap: 16 }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    paddingHorizontal: '5%',
    paddingTop: '5%',
    paddingBottom: '2%',
  },
  heading: {
    fontSize: 20,
    color: '#333',
    marginBottom: 16,
    fontFamily: 'Lato-Bold',
  },
});

export default TopAcademies;