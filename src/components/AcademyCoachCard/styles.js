import { StyleSheet } from 'react-native';

export default StyleSheet.create({
  card: {
    width: 250,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    backgroundColor: '#FFF',
  },
  image: {
    width: '100%',
    height: 150,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    objectFit: 'fill',
  },
  cardContent: {
    padding: 8,
    justifyContent: 'space-between',
    height: 150,
  },
  coachName: {
    fontSize: 16,
    color: 'black',
    fontFamily: 'Lato-Bold',
  },
  category: {
    fontSize: 14,
    color: 'black',
    fontFamily: 'Lato-Regular',
  },
  description: {
    fontSize: 14,
    color: 'grey',
    marginTop: 4,
    fontFamily: 'Lato-Regular',
  },
  learnMore: {
    borderRadius: 4,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  learnMoreText: {
    fontWeight: 'bold',
    color: '#000',
    paddingBottom: '0.5%',
    borderBottomWidth: 1,
    borderBottomColor: '#000',
  },
}); 