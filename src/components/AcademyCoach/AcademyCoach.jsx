import React from 'react';
import { View, Text, ScrollView } from 'react-native';
import AcademyCoachCard from '../AcademyCoachCard/AcademyCoachCard';
import styles from './styles';

const AcademyCoach = ({ coaches, title }) => {
  return (
    <View style={styles.container}>
      {title && <Text style={styles.heading}>{title}</Text>}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.scrollViewContent}>
        {coaches.map((coach, idx) => (
          <AcademyCoachCard key={coach._id} coach={coach} isLast={idx === coaches.length - 1} />
        ))}
      </ScrollView>
    </View>
  );
};

export default AcademyCoach; 