import React, { useEffect, useState, useRef } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Modal, ActivityIndicator, Image, TextBase, TextInput, TouchableWithoutFeedback, KeyboardAvoidingView, Keyboard, SafeAreaView, Animated, Easing } from 'react-native';
import { NEXT_PUBLIC_BASE_URL } from '@env';
import { Divider } from 'react-native-paper';
import axios from 'axios';
import CheckBox from '@react-native-community/checkbox';
import Slider from '@react-native-community/slider';
const plus = require("../../assets/plus.png");
const minus = require("../../assets/minus.png");
const remove = require("../../assets/cross.png")

const staticFilters = [
  {
    id: 'classType',
    name: 'Class Type',
    options: [
      { value: 'course', label: 'Course' },
      { value: 'class', label: 'Class' },
    ],
  },
  {
    id: 'sessionType',
    name: 'Session Type',
    options: [
      { value: 'individual', label: 'Individual' },
      { value: 'group', label: 'Group' },
    ],
  },
  {
    id: 'priceRange',
    name: 'Price',
    options: [],
    min: 0,
    max: 5000,
  },
  {
    id: 'proficiency',
    name: 'Proficiency Level',
    options: [
      { value: 'beginner', label: 'Beginner' },
      { value: 'intermediate', label: 'Intermediate' },
      { value: 'advance', label: 'Advance' },
    ],
  },
];

const Filter = ({ visible, onClose, applyFilter, showFilterButton, selectedSport, selectedFilters }) => {
  const [selectedOptions, setSelectedOptions] = useState(selectedFilters || {});
  const [dynamicFilters, setDynamicFilters] = useState(staticFilters);
  const [isLoading, setIsLoading] = useState(true);
  const [expandedSections, setExpandedSections] = useState({});
  const [priceRange, setPriceRange] = useState({ min: 0, max: 5000 });
  const [sliderValue, setSliderValue] = useState(2500);
  const [minPrice, setMinPrice] = useState('');
  const [maxPrice, setMaxPrice] = useState('');
  const maxPriceInputRef = useRef(null);
  const [filterModalVisible, setFilterModalVisible] = useState(false);
  const filterAnim = useRef(new Animated.Value(0)).current;

  const openFilterModal = () => {
    setFilterModalVisible(true);
    Animated.timing(filterAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
      easing: Easing.out(Easing.ease),
    }).start();
  };

  const closeFilterModal = () => {
    Animated.timing(filterAnim, {
      toValue: 0,
      duration: 250,
      useNativeDriver: true,
      easing: Easing.in(Easing.ease),
    }).start(() => {
      setFilterModalVisible(false);
      onClose();
    });
  };

  useEffect(() => {
    if (visible) {
      openFilterModal();
    } else if (filterModalVisible) {
      closeFilterModal();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios.get(`${NEXT_PUBLIC_BASE_URL}/api/category`);

        const fetchedCategories = response.data?.data?.map(cat => ({
          value: cat.name.toLowerCase(),
          label: cat.name.toLowerCase(),
        }));
        setDynamicFilters([
          ...staticFilters,
          {
            id: 'category',
            name: 'Categories',
            options: fetchedCategories,
          },
        ]);
      } catch (error) {
                setIsLoading(false);
        console.error('Error fetching data:', error);
      }
    };
    fetchData();
  }, []);

  useEffect(() => {
    setSelectedOptions((prevOptions) => {
      if (typeof selectedFilters === 'object' && selectedFilters !== null) {
        // Always use the latest selectedFilters from the parent component
        return { ...selectedFilters };
      }
      return prevOptions;
    });
  }, [selectedFilters]);
  useEffect(() => {
    if (minPrice && maxPrice) {
      console.log(`Min Price: ${minPrice}, Max Price: ${maxPrice}`);
    }
  }, [minPrice, maxPrice]);

  const handlePriceChange = (value, type) => {
    if (type === 'min') {
      setMinPrice(value);
    } else {
      setMaxPrice(value);
    }
  };


  const toggleSection = (sectionId) => {
    setExpandedSections(currentSection =>
      currentSection === sectionId ? "" : sectionId
    );
  };
  const handleOptionChange = (filterId, optionValue) => {
    const updatedOptions = { ...selectedOptions };

    if (!updatedOptions[filterId]) {
      updatedOptions[filterId] = [];
    }

    // Function to compare two ranges
    const rangeIndex = updatedOptions[filterId].findIndex(range =>
      range[0] === optionValue[0] && range[1] === optionValue[1]
    );

    if (rangeIndex === -1) {
      // If the range is not already selected, add it
      updatedOptions[filterId].push(optionValue);
    } else {
      // If the range is already selected, remove it
      updatedOptions[filterId].splice(rangeIndex, 1);
    }
    setSelectedOptions(updatedOptions);
  };


  const handleApplyFilter = () => {
    applyFilter(selectedOptions);
    onClose();
  };

  const handleClearFilter = () => {
    setSelectedOptions({});
    applyFilter({});
    onClose();
  };
  const { min: minPriceRange, max: maxPriceRange } = dynamicFilters.find(filter => filter.id === 'priceRange');

  const stepSize = 1000;

  const generatePriceRangeOptions = () => {
    const options = [];
    for (let i = priceRange.min; i < priceRange.max; i += stepSize) {
      const label = `${i} - ${Math.min(i + stepSize, priceRange.max)}`;
      options.push({ label, value: [i, Math.min(i + stepSize, priceRange.max)] });
    }
    return options;
  };
  const handlePriceRangeChange = (filterId, range) => {
    setSelectedOptions({ ...selectedOptions, range });
  };
  return (
    <View>
      {showFilterButton && (
        <TouchableOpacity onPress={closeFilterModal} style={styles.filterButton}>
          <Text style={styles.filterButtonText}>Hide Filter</Text>
        </TouchableOpacity>
      )}
      <Modal visible={filterModalVisible} animationType="none" transparent={true} onRequestClose={closeFilterModal}>
        <TouchableWithoutFeedback onPress={closeFilterModal}>
          <View style={{flex: 1, backgroundColor: 'rgba(0,0,0,0.5)'}}>
            <TouchableWithoutFeedback>
              <SafeAreaView style={{flex: 1}}>
                <Animated.View style={[
                  styles.filterContainer,
                  {
                    opacity: filterAnim,
                    transform: [{
                      translateY: filterAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: [100, 0],
                      })
                    }]
                  }
                ]}>
                <ScrollView contentContainerStyle={{flexGrow: 1}}>
                  {dynamicFilters.map((filter, index) => (
                    <View key={filter.id} style={styles.filterSection}>
                      <TouchableOpacity
                        onPress={() => toggleSection(filter.id)}
                        style={styles.sectionHeader}>
                        <Text style={styles.filterHeader}>{filter.name}</Text>
                        <Image
                          source={expandedSections===filter.id ? minus : plus}
                          style={styles.icon}
                        />
                      </TouchableOpacity>

                      {expandedSections === filter.id && (
                        <View style={styles.optionsContainer}>
                          {filter.id === 'priceRange' ? (
                            <View style={styles.optionsContainer}>
                              {generatePriceRangeOptions().map(({label, value}) => (
                                <TouchableOpacity
                                  key={label}
                                  onPress={() =>
                                    handleOptionChange('priceRange', value)
                                  } // Always pass 'priceRange' as filterId
                                  style={styles.filterOption}>
                                  <CheckBox
                                    value={selectedOptions.priceRange?.some(
                                      range =>
                                        range[0] === value[0] &&
                                        range[1] === value[1],
                                    )}
                                    onValueChange={() =>
                                      handleOptionChange('priceRange', value)
                                    }
                                    tintColors={{true: 'black', false: 'black'}}
                                  />
                                  <Text style={styles.filterLabel}>{label}</Text>
                                </TouchableOpacity>
                              ))}
                            </View>
                          ) : (
                            filter.options.map(option => (
                              <TouchableOpacity
                                key={option.value}
                                onPress={() =>
                                  handleOptionChange(filter.id, option.value)
                                }
                                style={styles.filterOption}>
                                <CheckBox
                                  value={selectedOptions[filter?.id]?.includes(
                                    option.value,
                                  )}
                                  onValueChange={() =>
                                    handleOptionChange(filter.id, option.value)
                                  }
                                  tintColors={{true: 'black', false: 'black'}}
                                />
                                <Text style={styles.filterLabel}>{option.label}</Text>
                              </TouchableOpacity>
                            ))
                          )}
                        </View>
                      )}
                      <Divider />
                    </View>
                  ))}
                </ScrollView>
                <View style={[styles.horizontalLine, {width: '100%'}]} />
                <View
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-evenly',
                  }}>
                  <TouchableOpacity style={styles.cancelButton} onPress={handleClearFilter}>
                    <Text style={[styles.buttonText, {color: '#000'}]}>CLEAR</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.applyButton}
                    onPress={handleApplyFilter}>
                    <Text style={styles.buttonText}>APPLY</Text>
                  </TouchableOpacity>
                </View>
                </Animated.View>
              </SafeAreaView>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  filterButton: {
    margin: 10,
    backgroundColor: '#00AEEF',
    borderRadius: 20,
    padding: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterButtonText: {
    color: 'white',
    fontSize: 16,
    fontFamily:"Lato-Regular"
  },
  filterContainer: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 20,
  },
  filterSection: {
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  filterHeader: {
    fontSize: 18,
    marginBottom: 10,
    color: '#000',
    fontFamily:"Lato-Regular"

  },
  sectionIcon: {
    fontSize: 20,
  },
  optionsContainer: {
    marginLeft: 20,
  },
  filterOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  filterLabel: {
    fontSize: 16,
    color: 'black',
    marginLeft: 8,
    fontFamily:"Lato-Regular"

  },
  applyButton: {
    backgroundColor: '#000',
    paddingHorizontal: "10%",
    paddingVertical: "3%",
    borderRadius: 5,
    marginTop: 10,
  },
  cancelButton: {
    // backgroundColor: '#000',
    paddingHorizontal: "10%",
    paddingVertical: "3%",
    marginTop: 10,
    borderRadius: 5,
    borderColor: "#000",
    borderWidth: 1

  },
  buttonText: {
    color: '#fff',
    textAlign: 'center',
    fontSize: 14,
    fontFamily:"Lato-Regular"

  },

  icon: {
    width: 12,
    height: 10,
  },
  horizontalLine: {
    backgroundColor: "#000",
    height: 0.5,
    width: "95%",
    alignSelf: "center",
    marginTop: "2%",
  },
  input: {
    marginBottom: '1%',
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 10,
    borderRadius: 5,
    width: '50%',
    color: '#000',
  },
});

export default Filter;