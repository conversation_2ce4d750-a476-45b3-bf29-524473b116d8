import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
    // 1. Layout/Container
    container: {
      flex: 1,
    },
    listingCardsContainer: {
      flex: 1,
      marginTop: 10,
      marginVertical: 15,
    },
    buttonsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      margin: 10,
    },
    divider: {
      width: 1,
      backgroundColor: '#000',
      alignSelf: 'center',
      height: '95%',
    },
    horizontalLine: {
      backgroundColor: '#000',
      height: 1,
      width: '95%',
      alignSelf: 'center',
    },
  
    // 2. Modal
    modalOverlay: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalView: {
      backgroundColor: 'white',
      borderRadius: 20,
      padding: 20,
      width: '80%',
      maxHeight: '80%',
      alignItems: 'center',
    },
    modalTitle: {
      fontSize: 22,
      lineHeight: 28,
      marginBottom: 15,
      color: "#000",
      fontFamily: "Lato-Bold"
    },
    modalActions: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      marginTop: 15,
      width: '100%',
    },
  
    // 3. Buttons
    filterButton: {
      display: "flex",
      flexDirection: "row",
      alignItems: "center"
    },
    cancelButton: {
      backgroundColor: '#000',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 5,
      marginTop: 15,
      width: 100,
      height: 35,
    },
  
    // 4. Text
    buttonText: {
      color: '#000',
      fontSize: 16,
      lineHeight: 22,
      fontFamily: "Lato-Regular"
    },
    cancelButtonText: {
      color: '#fff',
      textAlign: 'center',
      fontSize: 14,
      lineHeight: 20,
      fontFamily: "Lato-Regular"
    },
    sortOptionText: {
      fontSize: 18,
      lineHeight: 24,
      color: "#000",
      fontFamily: "Lato-Regular"
    },
    tagText: {
      color: '#000',
      fontSize: 15,
      marginRight: 8,
      fontFamily: "Lato-Regular"
    },
    tagGroupName: {
      fontSize: 16,
      fontFamily: "Lato-Bold"
    },
  
    // 5. Tags
    tagcontainer: {
      marginLeft: "5%",
      paddingTop: "2%",
    },
    tag: {
      display: "flex",
      flexDirection: "row",
      backgroundColor: '#BCBEC0',
      borderRadius: 3,
      paddingVertical: 5,
      paddingHorizontal: 10,
      marginRight: 5,
      marginBottom: 5,
      alignItems: "center",
    },
    tagGroup: {
      display: "flex",
      alignContent: "center",
    },
  
    // 6. Miscellaneous
    labelWrapper: {
      flex: 1,
    },
    sortOption: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      width: '100%',
      paddingVertical: 10,
    },
  });