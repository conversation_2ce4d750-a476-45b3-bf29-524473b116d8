import React, { useEffect, useState } from "react";
import {
  Modal,
  View,
  Text,
  Button,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  SafeAreaView,
} from "react-native";
import DatePicker from "react-native-modern-datepicker";
// import DatePicker from 'react-modern-calendar-datepicker';
// import DatePicker from "react-datepicker";
import { DateTimePickerAndroid } from '@react-native-community/datetimepicker';

import DateTimePicker from '@react-native-community/datetimepicker';
import { getToday, getFormatedDate } from "react-native-modern-datepicker";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import CustomCalendar from "./Calendar";
import { NEXT_PUBLIC_BASE_URL } from "@env";
import axios from "axios";
import CalendarPicker from "react-native-calendar-picker";
import TimePicker from "../screens/TimePicker";
import { yupToFormErrors } from "formik";
import { calendarFormat } from "moment";
import { useAuth } from "../Context/AuthContext";

const BookingModal = ({
  dates,
  isVisible,
  onClose,
  coachId,
  coach_id,
  setSelectedSlots,
  selectedSlots,
  totalPrice,
  setTotalPrice,
  fees,
  setModalVisible,
  course,
}) => {
  const { userToken } = useAuth()
  const [date, setDate] = useState(new Date());
  const [selectedDateSlot, setSelectedDateSlot] = useState(new Date())
  const [time, setTime] = useState("");
  const [openDatePicker, setOpenDatePicker] = useState(false);
  const [openTimePicker, setOpenTimePicker] = useState(false);
  const [selectedDuration, setSelectedDuration] = useState("");
  const [showTimeSlotWarning, setShowTimeSlotWarning] = useState(false);
  const [formatDate, setFormtatDate] = useState("");
  const [eventsTime, setEventsTime] = useState([]);
  const [selectedAmPm, setSelectedAmPm] = useState("AM");
  const [bookedSlot, setBookedSlot] = useState(null);
  const [selectedTime, setSelectedTime] = useState(null);
  const [slotTiming, setSlotTiming] = useState("HH:MM");
  const [timePickerModal, setTimePickerModal] = useState(false);
  const today = new Date();

  const handleSelectTime = (time) => {
    const { overlap, message } = checkForTimeOverlap(formatTime12Hour(time), date);
    if (overlap) {
      Alert.alert("Warning", message);
    } else {
      formatTime12HourFinal(time)
    }
  };

  const formatTime12Hour = (date) => {
    let hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours || 12; // Convert '0' hour to '12'
    const minutesFormatted = minutes < 10 ? `0${minutes}` : minutes;
    timeSlotSelected = `${hours}:${minutesFormatted} ${ampm}`;
    // setSlotTiming(timeSlotSelected)
    // setTime(timeSlotSelected)
    return `${hours}:${minutesFormatted} ${ampm}`;
  };
  const formatTime12HourFinal = (date) => {
    let hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours || 12; // Convert '0' hour to '12'
    const minutesFormatted = minutes < 10 ? `0${minutes}` : minutes;
    timeSlotSelected = `${hours}:${minutesFormatted} ${ampm}`;
    setSlotTiming(timeSlotSelected)
    setTime(timeSlotSelected)
    return `${hours}:${minutesFormatted} ${ampm}`;
  };
  const startDate = dates.startDate;
  const endDate = dates.endDate;
  const [bookings, setBookings] = useState([]);
  const fetchBooking = async () => {
    try {
      const requestOptions = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userToken}`,
        },
      };
      const response = await axios.get(`${NEXT_PUBLIC_BASE_URL}/api/booking/`, requestOptions);
      setBookings(response.data);
    } catch (error) {
      console.log("Error fetching data: 99");
    }
  };
  useEffect(() => {
    fetchBooking();
  }, []);

  const getDisabledDates = () => {
    const startDate = new Date(dates.startDate);
    const endDate = new Date(dates.endDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const days = dates?.days; // ensure this is provided
    const dayOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const disabledDays = dayOfWeek.filter((day) => !days.includes(day));

    const disabledDates = [];

    let currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      // Disable dates that are either in the past or on disabled days of the week
      if (currentDate < today || disabledDays.includes(dayOfWeek[currentDate.getDay()])) {
        disabledDates.push(new Date(currentDate));
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }
    return disabledDates;
  };

  let event = [];

  const formatTime = (isoString) => {
    const date = new Date(isoString);
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    return `${hours}:${minutes}`;
  };
  const bookedSlots = eventsTime.map((event) => ({

    start: formatTime(event.start),
    end: formatTime(event.end),
    title: event.title,
  }));

  useEffect(() => {
    setBookedSlot(bookedSlots);
  }, [eventsTime])
  const handleOpenDatePicker = () => {
    setOpenDatePicker(!openDatePicker);
    if (openTimePicker) {
      setOpenTimePicker(false);
      setTimePickerModal(false);
    }
  };

  const handleSaveDate = () => {
    setOpenDatePicker(false);
  };

  const handleOpenTimePicker = () => {
    setOpenTimePicker(!openTimePicker);
    setTimePickerModal(true)
    if (openDatePicker) {
      setOpenDatePicker(false);
    }
  };
  const handleCloseTimePicker = () => {
    setTimePickerModal(false)
    setSelectedTime(null)

  }
  const handleSaveTime = () => {
    const selectedTimeInMinutes =
      parseInt(selectedHour, 10) * 60 + parseInt(selectedMinute, 10);
    setOpenTimePicker(false);
  };
  const handleTimeDateState = () => {
    setModalVisible(false);
    setDate(new Date());
    setSelectedDateSlot(new Date())
    setTime("");
    setSelectedDuration("");
  };
  const getTimeAsDate = (timeString, amPm) => {
    const [hour, minute] = timeString.split(':').map(Number);
    const adjustedHour = amPm === "PM" ? (hour === 12 ? 12 : hour + 12) : (hour === 12 ? 0 : hour);
    const date = new Date();
    date.setHours(adjustedHour, minute, 0);
    return date;
  };
  const handleBookSlot = () => {
    if (!date || !time) {
      Alert.alert("Error", "Please select both date and time.");
      return;
    }
    const selectedTimeInMinutes = parseInt(selectedHour, 10) * 60 + parseInt(selectedMinute, 10);
    let endTimeInMinutes;
    if (selectedDuration === "30min") {
      endTimeInMinutes = addMinutes(time, 30)
    } else if (selectedDuration === "45min") {
      endTimeInMinutes = addMinutes(time, 45);
    } else {
      endTimeInMinutes = addHours(time, 1);
    }
    const endHour = Math.floor(endTimeInMinutes / 60);
    const endMinute = endTimeInMinutes % 60;

    const endTimeString = `${endHour}:${endMinute < 10 ? '0' : ''}${endMinute} ${selectedAmPm}`;
    const endTimeDate = getTimeAsDate(endTimeInMinutes.split(' ')[0], endTimeInMinutes.split(' ')[1]);
    const endTimeAmPmDate = getTimeAsDate(endTimeAmPm.split(' ')[0], endTimeAmPm.split(' ')[1]);
    if (endTimeDate > endTimeAmPmDate) {
      Alert.alert("Warning", "The selected time and duration exceed the available booking window. Please select a shorter duration or an earlier time.");
      return;
    }
    const parts = date.split('-');
    const formattedDate = `${parts[2]}-${parts[1]}-${parts[0]}`;
    const d = new Date(`${parts[2]}-${parts[1]}-${parts[0]}T00:00:00Z`);
    const ds = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const dow = ds[d.getUTCDay()];
    const currentDate = new Date().toLocaleDateString("en-IN");
    let duration;
    if (selectedDuration === "45min") {
      endDateTime = addMinutes(time, 45);
      duration = "45 mins";
    } else if (selectedDuration === "30min") {
      endDateTime = addMinutes(time, 30);
      duration = "30 mins";
    } else {
      endDateTime = addHours(time, 1);
      duration = "60 mins";
    }
    let price = 0;
    if (selectedDuration === "30min") {
      price = fees?.fees30;
    } else if (selectedDuration === "45min") {
      price = fees?.fees45;
    } else {
      price = fees?.fees60;
    }
    const newBookedSlot = {
      date: formattedDate,
      duration: duration,
      start: time,
      end:
        selectedDuration === "45min"
          ? addMinutes(time, 45)
          : selectedDuration === "30min"
            ? addMinutes(time, 30)
            : addHours(time, 1),
      days: dow,
      fees: price
    };
    setSelectedSlots([...selectedSlots, newBookedSlot]);
    setTotalPrice((prevTotalPrice) => prevTotalPrice + price);
    handleTimeDateState();
    setSlotTiming("HH:MM")

  };
  const formatDateTwo = (date) => {
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear();

    const formattedDateTwo = `${year}/${month}/${day}`;
    return formattedDateTwo;
  };
  const addMinutes = (time, minutesToAdd) => {
    const [hourStr, minuteStr, ampm] = time.split(/:| /);
    let hours = parseInt(hourStr);
    let minutes = parseInt(minuteStr);

    // Convert the hour to a 24-hour format for easier manipulation.
    if (ampm === "PM" && hours !== 12) {
      hours += 12;
    } else if (ampm === "AM" && hours === 12) {
      hours = 0;  // Midnight is 0 hours in 24-hour time.
    }

    // Calculate total minutes after addition
    let totalMinutes = hours * 60 + minutes + minutesToAdd;

    // Convert back to hours and minutes
    hours = Math.floor(totalMinutes / 60) % 24;  // Get hour in 24-hour format.
    minutes = totalMinutes % 60;

    // Determine AM or PM and convert hour back to 12-hour format.
    let newAmPm = hours >= 12 ? "PM" : "AM";
    hours = hours % 12;
    if (hours === 0) hours = 12;  // Adjust the 12-hour format specifics.

    const newTime = `${hours}:${minutes < 10 ? "0" + minutes : minutes} ${newAmPm}`;
    return newTime;
  };



  const addHours = (time, hours) => {
    const [selectedHour, selectedMinute] = time.split(":");
    const newTime = `${parseInt(selectedHour) + hours}:${selectedMinute}`;
    return newTime;
  };
  const hours = Array.from({ length: 24 }, (_, i) =>
    i < 10 ? `0${i}` : `${i}`
  );
  const minutes = Array.from({ length: 60 }, (_, i) =>
    i < 10 ? `0${i}` : `${i}`
  );

  const [selectedHour, selectedMinute] = time.split(":");
  const [timeHour, setTimeHour] = useState();
  const [timeMinute, setTimeMinute] = useState();
  useEffect(() => {
    if (timeHour) {
      setTime(timeHour)
    }
    if (timeHour && timeMinute) {
      const selectedTimezone = `${timeHour}:${timeMinute}`;
      const timeIs = isTimeEditable(timeHour, timeMinute);
      if (timeIs) {
        setTime(selectedTimezone)
      } else {
        Alert.alert("Selected time is outside the allowed range");
      }
    }
  }, [timeHour, timeMinute])

  const isTimeEditable = (timeHour, timeMinute) => {

    const [startHour, startMinute] = dates.startTime.split(":").map(Number);
    const [endHour, endMinute] = dates.endTime.split(":").map(Number);

    const currentTimeInMinutes = (Number(timeHour) * 60) + Number(timeMinute);
    const startTimeInMinutes = (Number(startHour) * 60) + Number(startMinute);
    const endTimeInMinutes = (Number(endHour) * 60) + Number(endMinute);

    return (
      currentTimeInMinutes >= startTimeInMinutes &&
      currentTimeInMinutes <= endTimeInMinutes
    );
  };
  //-----------------------------------------------------------
  const dayss = dates.days;
  const convertToAmPm = (time) => {
    const [hours, minutes] = time.split(":").map(Number);
    const period = hours >= 12 ? "PM" : "AM";
    const hours12 = hours % 12 || 12;
    return `${hours12}:${minutes < 10 ? "0" : ""}${minutes} ${period}`;
  };

  const startTimeAmPm = convertToAmPm(dates.startTime);
  const endTimeAmPm = convertToAmPm(dates.endTime);
  const onDateChange = (date) => {
    setSelectedDateSlot(date)
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear();
    const formattedDate = `${day}-${month}-${year}`;
    const formattedDateTwo = `${year}/${month}/${day}`;
    setDate(formattedDate);
    handleDateValue(formattedDateTwo);
  };
  const [isTimePickerVisible, setTimePickerVisibility] = useState(false);
  const [disabledTimes, setDisabledTimes] = useState([]);
  const getDisabledTimes = () => {
    const { startTime, endTime } = dates;

    const [startHour, startMinute] = startTime.split(":").map(Number);
    const [endHour, endMinute] = endTime.split(":").map(Number);

    const disabledTimes = [];

    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute++) {
        const hourString = hour.toString().padStart(2, "0");
        const minuteString = minute.toString().padStart(2, "0");
        const time = `${hourString}:${minuteString}`;
        const timeInMinutes = hour * 60 + minute;
        const startTimeInMinutes = startHour * 60 + startMinute;
        const endTimeInMinutes = endHour * 60 + endMinute;
        if (
          timeInMinutes < startTimeInMinutes ||
          timeInMinutes > endTimeInMinutes
        ) {
          disabledTimes.push(time);
        }
      }
    }

    return disabledTimes;
  };
  const handleDateValue = (value) => {
    let newDate;
    if (value) {
      const [year, month, day] = value.split("/");
      newDate = `${year}-${month}-${day}`;
    }
    const myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");
    myHeaders.append("Authorization", `Bearer ${userToken}`);
    // const raw = JSON.stringify({
    //   coach_id: coachId,
    //   startDate: `${newDate}T00:00:00`,
    //   endDate: `${newDate}T23:59:00`,
    //   email: coach_id?.googleEmail,
    //   course_id: course?._id,
    // });
    const raw = JSON.stringify({
      coachId: coachId,
      startDate: `${newDate}T00:00:00`,
      endDate: `${newDate}T23:59:00`,
      // email: coach_id?.googleEmail,
      courseId: course?._id,
    });
    const requestOptions = {
      method: "POST",
      headers: myHeaders,
      body: raw,
      redirect: "follow",
    };

    fetch(`${NEXT_PUBLIC_BASE_URL}/api/booking/events`, requestOptions)
      .then((response) => response.json())
      .then((result) => {
        const events = result ? result.map((event) => {
          const startDate = new Date(date);
          const startTime = event.startTime;
          const [startHour, startMinute] = startTime?.split(":").map(Number);
          startDate.setHours(startHour, startMinute, 0, 0);
          const endDate = new Date(date);
          const endTime = event.endTime;
          const [endHour, endMinute] = endTime?.split(":").map(Number);
          endDate.setHours(endHour, endMinute, 0, 0);

          return {
            start: startDate,
            end: endDate,
          };
        })
          : null;
        setEventsTime(events);
      })
      .catch((error) => console.error(error));
  };
  const showTimePicker = () => {
    setTimePickerVisibility(true);
  };

  const hideTimePicker = () => {
    setTimePickerVisibility(false);
  };

  const handleConfirm = (time) => {
    hideTimePicker();
  };
  const checkForTimeOverlap = (selectedTime, selectedDate) => {
    const formattedSelectedDate = formatDateToMatch(selectedDate);
    const selectedTimeInMinutes = parseInt(selectedTime.split(':')[0], 10) * 60 + parseInt(selectedTime.split(':')[1], 10);
    const selectedDateSlots = selectedSlots.filter(slot => slot.date === formattedSelectedDate);
    const overlaps = selectedDateSlots.some(slot => {
      const slotStartTime = parseInt(slot.start.split(':')[0], 10) * 60 + parseInt(slot.start.split(':')[1], 10);
      const slotEndTime = parseInt(slot.end.split(':')[0], 10) * 60 + parseInt(slot.end.split(':')[1], 10);
      return selectedTimeInMinutes >= slotStartTime && selectedTimeInMinutes < slotEndTime;
    });
    const overlapsWithBooked = bookedSlot.some(slot => {
      const slotStartTimeBooked = formatTimeWithAmPm(slot.start);
      const slotEndTimeBooked = formatTimeWithAmPm(slot.end);
      return (selectedTime >= slotStartTimeBooked && selectedTime < slotEndTimeBooked)
    })
    if (overlaps) {
      return { overlap: true, message: "The selected time overlaps with an recently booked slot." };
    } else if (overlapsWithBooked) {
      return { overlap: true, message: "The selected time overlaps with a slot which is already booked for coach." };
    } else {
      return { overlap: false, message: "" }; // No overlap
    }
  };
  const formatDateToMatch = (dateString) => {
    // Convert the date string from '29-04-2024' to '2024-04-29' format
    const [day, month, year] = dateString.split('-');
    return `${year}-${month}-${day}`;
  };
  const formatTimeWithAmPm = (time) => {
    const [hours, minutes] = time.split(':');
    const hoursInt = parseInt(hours, 10);
    const suffix = hoursInt >= 12 ? 'PM' : 'AM';
    const formattedHours = ((hoursInt + 11) % 12 + 1);
    return `${formattedHours}:${minutes} ${suffix}`;
  };
  return (
    <ScrollView>
      <Modal
        animationType="slide"
        transparent={false}
        visible={isVisible}
        onRequestClose={onClose}
      >
        <SafeAreaView style={{flex:1}}>
        <View style={styles.fullScreenView}>
        <TouchableOpacity onPress={handleOpenDatePicker}>
          <View style={styles.datePickerSection}>
            <View style={styles.inputLabel}>
              <Text style={{ color: "#000" }}>Choose Date</Text>
            </View>

           
              <TextInput
                style={styles.input}
                placeholder="DD-MM-YYYY"
                value={date}
                editable={false}
                placeholderTextColor="#9CA3AF"
                pointerEvents="none"
              />
              <Text style={{ color: "#64b5f6", fontWeight: "500", marginBottom: "5%", marginLeft: "4%", }}>
                The available time slots are between {dates.days.join("-")} from{" "}
                {startTimeAmPm} to {endTimeAmPm}
              </Text>
           
          </View>
          </TouchableOpacity>
          {openDatePicker && (
            <View >
              <CalendarPicker
                onDateChange={onDateChange}
                disabledDates={getDisabledDates()}
                minDate={startDate}
                maxDate={endDate}
                previousTitleStyle={{ color: "#000" }}
                nextTitleStyle={{ color: "#000" }}
              />

              <View style={styles.buttonContainer}>
                <Button
                  title="Save Date"
                  onPress={() => setOpenDatePicker(false)}
                />
              </View>
            </View>
          )}

          <View>
            <View style={styles.inputLabel}>
              <Text style={{ color: "#000" }}>Choose Time</Text>
            </View>
            <TouchableOpacity onPress={handleOpenTimePicker}>
              <View style={styles.inputLabel}></View>
              <TextInput
                style={styles.input}
                placeholder="HH:MM"
                value={slotTiming}
                placeholderTextColor="#9CA3AF"
                editable={isTimeEditable(time)}
                pointerEvents="none"
              />
            </TouchableOpacity>
          </View>

          {openTimePicker && (

            <View style={styles.timePickerModal}>
              <TimePicker selectedTime={selectedTime} setSelectedTime={setSelectedTime} startTime={dates.startTime} endTime={dates.endTime} handleSelectTime={handleSelectTime} handleCloseTimePicker={handleCloseTimePicker} timePickerModal={timePickerModal} setTimePickerModal={setTimePickerModal}  bookedSlots={bookedSlots}  date={date} selectedDateSlot={selectedDateSlot}/>
              {/* <View style={styles.buttonContainer} >
                <Button title="Save Time" onPress={handleSaveTime} />
              </View> */}
              {showTimeSlotWarning && (
                <Text style={styles.warningText}>
                  The selected time slot is already booked. Please choose
                  another time.
                </Text>
              )}
            </View>
          )}

          <View style={styles.durationContainer}>
            <View style={styles.durationSelector}>
              <TouchableOpacity
                disabled={fees.fees30 <= 0}
                style={[
                  // { opacity: fees.fees30 <= 0 ? 1 : 0.2},
                  styles.durationButton,
                  { opacity: fees.fees30 <= 0 ? 0.5 : 1 },
                  selectedDuration === "30min" && styles.selectedDurationButton,
                ]}
                onPress={() => setSelectedDuration("30min")}

              >
                <Text style={[styles.durationButtonText, selectedDuration === "30min" && styles.selectedDurationButtonText]}>30 mins</Text>
                <Text style={[styles.durationButtonText, selectedDuration === "30min" && styles.selectedDurationButtonText]}> ₹{fees?.fees30 ? fees?.fees30 : "N/A"}</Text>
              </TouchableOpacity>


              <TouchableOpacity
                disabled={fees.fees45 <= 0}
                style={[
                  // { opacity: fees?.fees45 <= 0 ? 1 : 0.2},
                  styles.durationButton,
                  { opacity: fees.fees45 <= 0 ? 0.5 : 1 },
                  selectedDuration === "45min" && styles.selectedDurationButton,
                ]}

                onPress={() => setSelectedDuration("45min")}
              >
                <Text style={[styles.durationButtonText, selectedDuration === "45min" && styles.selectedDurationButtonText]}>45 mins</Text>
                <Text style={[styles.durationButtonText, selectedDuration === "45min" && styles.selectedDurationButtonText]}> ₹ {fees?.fees45 ? fees?.fees45 : "N/A"}</Text>
              </TouchableOpacity>


              <TouchableOpacity
                disabled={fees.fees60 <= 0}
                style={[
                  styles.durationButton,
                  { opacity: fees.fees60 <= 0 ? 0.5 : 1 },
                  selectedDuration === "1hr" && styles.selectedDurationButton,
                ]}
                onPress={() => setSelectedDuration("1hr")}
              >
                <Text style={[styles.durationButtonText, selectedDuration === "1hr" && styles.selectedDurationButtonText]}>1 hour</Text>
                <Text style={[styles.durationButtonText, selectedDuration === "1hr" && styles.selectedDurationButtonText]}> ₹ {fees?.fees60 ? fees.fees60 : "N/A"}</Text>
              </TouchableOpacity>
            </View>
          </View>
          <CustomCalendar bookedSlots={bookedSlots} />
          <View style={styles.buttonContainer}>
            <Button title="Book Slot" onPress={handleBookSlot} />
            <Button title="Close" onPress={handleTimeDateState} />
          </View>
        </View>
        </SafeAreaView>
      </Modal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  fullScreenView: {
    flex: 1,
    padding: 20,
  },
  card: {
    backgroundColor: "#FAFBFCFF",
    width: "100%",
    padding: 10,
    borderRadius: 10,
    marginBottom: 10,
    elevation: 1,
  },
  input: {
    margin: 12,
    borderWidth: 1,
    padding: 10,
    color: "#000",
  },
  inputLabel: {
    color: "#000",
    fontSize: 16,
    fontWeight: "500"
  },
  datePickerSection: {
    color: "#000",
  },
  buttonContainer: {
    justifyContent: "space-around",
    flexDirection: "row",
    alignItems: "center",
    marginVertical: "5%",
  },
  timePickerContainer: {
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    color: "#000",
  },
  timePicker: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 20,
    color: "#000",
  },
  numberSelector: {
    height: 200,
    width: 100,
    color: "#000",
  },
  numberItem: {
    alignItems: "center",
    justifyContent: "center",
    height: 40,
    color: "#000",
  },
  selectedNumberItem: {
    backgroundColor: "#ddd",
  },
  numberItemText: {
    fontSize: 18,
    color: "#000",
  },
  selectedNumberItemText: {
    fontSize: 18,
    color: "blue",
  },
  warningText: {
    color: "red",
    marginTop: 10,
    textAlign: "center",
  },
  durationContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-evenly",
    marginTop: 10,
    marginBottom: 10,
  },
  durationLabel: {
    fontSize: 16,
    fontWeight: "bold",
  },
  durationSelector: {
    flexDirection: "row",
  },
  durationButton: {
    paddingHorizontal: 15,
    paddingVertical: 5,
    borderRadius: 7,
    marginHorizontal: 5,
    borderColor: "#000",
    borderWidth: 1,
  },
  selectedDurationButton: {
    backgroundColor: "#64b5f6",
    color: "#fff"
  },
  durationButtonText: {
    color: "#000",
    fontSize: 14,
  },
  selectedDurationButtonText: {
    color: "#fff",
    fontSize: 14,
  },
  timePickerModal: {
    position: "absolute"
  }
});

export default BookingModal;
