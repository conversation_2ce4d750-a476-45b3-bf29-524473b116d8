import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import axios from 'axios';
import { NEXT_PUBLIC_BASE_URL } from '@env';
import AsyncStorage from "@react-native-async-storage/async-storage";
import { act } from 'react-test-renderer';

const redirect = require("../assets/redirect.png");
const CoachCourseCards = ({ ID, coachName }) => {
  const [courses, setCourses] = useState([]);
  const navigation = useNavigation();
  const [activeCourses, setActiveCourses] = useState([])
  useEffect(() => {
    const fetchCourses = async () => {
      let url = `${NEXT_PUBLIC_BASE_URL}/api/course/coach/${ID}`;
      try {
        const response = await axios.get(url);
        setCourses(response.data.data);
      } catch (error) {
        console.error('Error fetching data: 31');
      }
    };

    if (ID) {
      fetchCourses();
    }
  }, [ID]);

  useEffect(() => {
    const filteredActiveCourses = courses.filter(course => course.status === 'active');
    setActiveCourses(filteredActiveCourses);
  }, [courses]);
  const renderDescription = (description) => {
    const maxChars = 150; // Maximum number of characters to display
    let truncatedDescription = description.replace(/<[^>]+>/g, ''); // Remove HTML tags
    truncatedDescription = truncatedDescription.substring(0, maxChars); // Limit characters
    if (description.length > maxChars) {
      truncatedDescription += "...";
    }
    return <Text style={styles.description}>{truncatedDescription}</Text>;
  };
  const renderTitle = (title) => {
    const maxChar = 20;
    truncatedTitle = title.substring(0, maxChar);
    if (title.length > maxChar) {
      truncatedTitle += "...";
    }

    return <Text style={styles.courseName}>{truncatedTitle}</Text>

  }
  return (
    <View style={styles.container}>
      {activeCourses.length > 0  ? (
        <View>
          <Text style={styles.heading}>More Course by {coachName}</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.scrollViewContent}>
            {activeCourses.map((course, index) => (
              <TouchableOpacity
                key={index}
                style={styles.courseItem}
                onPress={() =>
                  navigation.navigate('Courses', { courseId: course._id })
                }>
                <Image
                  source={
                    course.images.length > 0
                      ? { uri: course.images[0].url }
                      : require('../assets/placeholder.png')
                  }
                  style={styles.image}
                />
                <View style={styles.courseDetails}>
                  <Text style={styles.price}>
                    {course.fees?.feesCourse
                      ? `₹ ${Math.round(course.fees?.feesCourse * 1.10)}`
                      : 'Explore'}
                  </Text>
                  <Text style={styles.courseName} numberOfLines={2}>
                    {renderTitle(course.courseName)}
                  </Text>
                  <View style={{ minHeight: 40 }}>
                    <Text style={styles.description} numberOfLines={2}>
                      {renderDescription(course.description)}
                    </Text>
                  </View>
                  <View style={styles.learnMore}>
                    <Text style={styles.learnMoreText}>What will you learn</Text>
                    <Image source={redirect} style={{ width: 15, height: 15 }} />
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      ):""}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    paddingBottom: '10%',
    paddingHorizontal: '5%',
    marginTop: '10%',
  },
  heading: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#000',
  },
  scrollViewContent: {
    paddingRight: 16,
  },
  courseItem: {
    width: 250,
    marginRight: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    backgroundColor: '#FAFBFC',
    overflow: 'hidden',

  },
  image: {
    width: '100%',
    height: 180,
  },
  courseDetails: {
    borderTopWidth: 1,
    borderColor: '#e2e2e2',
    paddingTop: 8,
    padding: 8,
  },
  price: {
    backgroundColor: '#E31F26',
    color: '#FAFBFCff',
    paddingHorizontal: "5%",
    paddingVertical: "3%",
    borderRadius: 4,
    marginBottom: 8,
    alignSelf: 'flex-start',
  },
  courseName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#000',
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  learnMore: {
    // padding: 8,
    borderRadius: 4,
    display: "flex",
    flexDirection: "row",
    alignItems: "center"
    
  },
  learnMoreText: {
    fontWeight: 'bold',
    color: '#000',
    paddingBottom: "0.5%",
    borderBottomWidth: 1,
    borderBottomColor: '#000',
  },
});

export default CoachCourseCards