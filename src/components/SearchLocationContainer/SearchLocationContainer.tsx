import { TextInput, TouchableOpacity, View } from "react-native";
import { GOOGLE_PLACES_API_KEY } from '@env';
import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';
import { useEffect, useRef, useState } from "react";
import { styles, googlePlacesStyles } from "./styles";
import { useNavigation } from "@react-navigation/native";
import {
    getCurrentLocationSilently,
    checkLocationPermissionStatus,
    LocationPermissionStatus
} from "../../helpers/locationHelper";

const SearchLocationContainer = () => {
    const navigation = useNavigation();
    const autoCompleteRef = useRef<any>(null);
    const [cityName, setCityName] = useState('');

    const getCurrentLocation = async () => {
        try {
            // Check permission status first - don't request permission
            const status = await checkLocationPermissionStatus();

            if (status === LocationPermissionStatus.GRANTED) {
                // Only attempt to get location if permission is granted
                const result = await getCurrentLocationSilently();
                if (result) {
                    setSearchLocationObj({
                        lat: result.coordinates.lat,
                        lan: result.coordinates.lng
                    });
                    setCityName(result.cityName);
                    autoCompleteRef.current?.setAddressText(result.cityName);
                }
            } else {
                // Silently skip location fetching if permission not granted or location services disabled
                if (status === LocationPermissionStatus.LOCATION_SERVICES_DISABLED) {
                    console.log('Location services disabled, skipping location fetch');
                } else {
                    console.log('Location permission not granted, skipping location fetch');
                }
            }
        } catch (error) {
            console.warn('Error getting current location:', error);
            // Silently fail - no user-facing error messages
        }
    };

    const [searchLocationObj, setSearchLocationObj] = useState<{
        lat: number | null;
        lan: number | null;
    }>({
        lat: null,
        lan: null
    });

    // Remove handleSelectLocation since we don't want user to update location

    // Navigate to search screen with location data
    const navigateToSearch = () => {
        console.log('Navigating to SearchScreen');
        // Only pass location data if we have valid coordinates and city name
        const hasValidLocation = searchLocationObj.lat && searchLocationObj.lan && cityName.trim();

        console.log('Navigating to SearchScreen with:', {
            hasValidLocation,
            locationObj: hasValidLocation ? searchLocationObj : { lat: null, lan: null },
            cityName: hasValidLocation ? cityName : ''
        });

        (navigation as any).navigate('SearchScreen', {
            locationObj: hasValidLocation ? searchLocationObj : { lat: null, lan: null },
            cityName: hasValidLocation ? cityName : ''
        });
    };
    useEffect(() => {
        getCurrentLocation();
    }, []);

    return (
        <View style={styles.container}>
            <TouchableOpacity style={styles.googlePlacesWrapper} onPress={navigateToSearch}>
                <GooglePlacesAutocomplete
                    placeholder="City Name"
                    placeholderTextColor="#000"
                    ref={autoCompleteRef}
                    onPress={navigateToSearch}
                    fetchDetails={false}
                    query={{
                        key: GOOGLE_PLACES_API_KEY,
                        language: 'en',
                        components: 'country:in',
                    }}
                    listViewDisplayed={false}
                    textInputProps={{
                        editable: false,
                        onPressIn: navigateToSearch,
                        onPressOut: navigateToSearch,
                        onFocus: navigateToSearch,
                    }}
                    debounce={200}
                    styles={googlePlacesStyles}
                />
            </TouchableOpacity>
            <TouchableOpacity
                style={styles.inputContainer}
                activeOpacity={0.8}
                onPress={navigateToSearch}
            >
                <TextInput
                    onPressIn={navigateToSearch}
                    onPressOut={navigateToSearch}
                    style={styles.input}
                    placeholder="Course/Coach/Sports"
                    placeholderTextColor="#000"
                    editable={false}
                />
            </TouchableOpacity>
        </View>
    );
};

export default SearchLocationContainer;