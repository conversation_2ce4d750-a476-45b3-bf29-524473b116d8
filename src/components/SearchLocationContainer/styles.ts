import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
    container: {
        backgroundColor: "#0EA5E9",
        paddingHorizontal: "5%",
        paddingVertical: "5%",
        zIndex: 2,
        gap: 20
    },
    inputContainer: {
        flexDirection: 'row',
        borderColor: '#ccc',
        backgroundColor: '#fff',
        borderWidth: 1,
        borderRadius: 5,
        color: '#000',
  },
    input: {
        flex: 1,
        padding: 10,
        color: '#000',
        fontSize: 16,
        fontFamily: 'Lato-Regular'
    },
    locationInputContainer: {
        backgroundColor: 'transparent',
        borderBottomWidth: 0,
        borderTopWidth: 0,
        borderRadius: 5,
    },
    locationTextInput: {
        marginLeft: 0,
        marginRight: 0,
        height: 48,
        color: '#000',
        borderColor: '#ccc',
        fontSize: 16,
        borderRadius: 5,
        borderWidth: 1,
        padding: 10,
        fontFamily: 'Lato-Regular'
    },

    // GooglePlaces wrapper to prevent overlap
    googlePlacesWrapper: {
        position: 'relative',
        zIndex: 1000,
    },

    // Custom suggestion row styles
    suggestionRow: {
        backgroundColor: '#fff',
        padding: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    suggestionText: {
        color: '#000',
        fontSize: 16,
        fontFamily: 'Lato-Regular',
    },
});

// Attractive GooglePlaces styles - matching SearchScreen.tsx
export const googlePlacesStyles = {
    container: {
        flex: 0,
        position: 'relative',
        zIndex: 1000,
    },
    textInputContainer: {
        backgroundColor: 'transparent',
        borderBottomWidth: 0,
        borderTopWidth: 0,
        borderRadius: 5,
    },
    textInput: {
        marginLeft: 0,
        marginRight: 0,
        height: 48,
        color: '#000',
        borderColor: '#ccc',
        fontSize: 16,
        borderRadius: 5,
        borderWidth: 1,
        padding: 10,
        fontFamily: 'Lato-Regular'
    },
    listView: {
        backgroundColor: '#fff',
        position: 'absolute',
        top: 48,
        left: 0,
        right: 0,
        zIndex: 1001,
        borderColor: '#ccc',
        borderWidth: 1,
        borderTopWidth: 0,
        borderRadius: 5,
        borderTopLeftRadius: 0,
        borderTopRightRadius: 0,
        maxHeight: 200,
    },
    row: {
        backgroundColor: '#fff',
        padding: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    description: {
        color: '#000',
        fontSize: 16,
        fontFamily: 'Lato-Regular',
    },
    separator: {
        height: 1,
        backgroundColor: '#eee',
    },
};