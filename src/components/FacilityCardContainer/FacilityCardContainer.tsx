import React from 'react';
import { View, Text, ScrollView } from 'react-native';
import FacilityCard from '../FacilityCard/FacilityCard';
import styles from './styles';

interface FacilityCardContainerProps {
  facilities: Array<any>;
  title?: string;
}

const FacilityCardContainer: React.FC<FacilityCardContainerProps> = ({ facilities, title }) => {
  if (!facilities || facilities.length === 0) return null;
  return (
    <View style={styles.container}>
      <Text style={styles.heading}>{title || 'FACILITIES'}</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.scrollViewContent}>
        {facilities.map((facility: any, idx: number) => {
          const coords = facility?.facilityDetails?.location?.coordinates;
          const latitude = coords ? coords[1] : undefined;
          const longitude = coords ? coords[0] : undefined;
          return (
            <FacilityCard
              key={facility._id || idx}
              name={facility?.facilityDetails?.name || ''}
              addressLine1={facility?.facilityDetails?.addressLine1 || ''}
              addressLine2={facility?.facilityDetails?.addressLine2 || ''}
              latitude={latitude}
              longitude={longitude}
            />
          );
        })}
      </ScrollView>
    </View>
  );
};

export default FacilityCardContainer; 