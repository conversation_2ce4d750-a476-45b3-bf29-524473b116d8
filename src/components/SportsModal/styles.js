import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFBFCFF',
  },
  scrollView: {
    flexDirection: 'column',
    alignItems: 'center',
  },
  modalContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FAFBFCFF',
    borderRadius: 10,
    marginTop: '10%',
    marginBottom: '5%',
    gap: 10,
  },
  modalHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
  },
  editCardHeading: {
    fontSize: 18,
    color: '#000',
    fontWeight: '500',
  },
  horizontalLine: {
    backgroundColor: '#BCBEC0',
    height: 1,
    width: '100%',
    alignSelf: 'center',
    marginVertical: '4%',
  },
  modalPickerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-evenly',
  },
  modalPickerLabel: {
    fontWeight: '500',
    fontSize: 16,
    color: '#000',
    minWidth: 80,
  },
  modalPicker: {
    height: 40,
    width: 200,
    color: '#000',
  },
  modalPickerDelete: {
    width: 20,
    height: 20,
  },
  addSportContainer: {
    marginVertical: 10,
    alignItems: 'center',
  },
  addSportButton: {
    backgroundColor: '#fff',
    borderWidth: 1.5,
    borderColor: '#000',
    borderRadius: 5,
    paddingVertical: 12,
    paddingHorizontal: 20,
    minHeight: 45,
    justifyContent: 'center',
    alignItems: 'center',
    width: 200,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  addSportButtonText: {
    color: '#000',
    fontWeight: '500',
    fontSize: 16,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
    flexWrap: 'wrap',
    width: '100%',
    marginTop: 20,
  },
});
