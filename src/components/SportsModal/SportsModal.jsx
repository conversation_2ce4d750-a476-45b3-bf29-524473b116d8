import React from 'react';
import {
  View,
  Text,
  Modal,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Image,
  Button,
} from 'react-native';
import CustomSportsPicker from '../CustomSportsPicker';
import { styles } from './styles';

const SportsModal = ({
  visible,
  onClose,
  playerData,
  sports,
  onAddSport,
  onDeleteHobby,
  onSportSelection,
  onSaveChanges,
  getSportName,
}) => {
  const closeIcon = require('../../assets/delete.png');

  return (
    <Modal visible={visible} animationType="slide">
      <SafeAreaView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollView}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeaderRow}>
              <Text style={styles.editCardHeading}>Edit Sports</Text>
            </View>
            <View style={styles.horizontalLine} />
            
            {playerData?.hobbies?.map((hobby, index) => (
              <View key={index} style={styles.modalPickerRow}>
                <Text style={styles.modalPickerLabel}>Sport {index + 1} : </Text>
                <CustomSportsPicker
                  data={sports?.data?.filter(sport => {
                    const isSelected = playerData.hobbies.some(h => h.id === sport._id);
                    return !isSelected || sport._id === hobby.id;
                  }) || []}
                  selectedValue={hobby.id}
                  onSelect={(selectedItem) => {
                    if (selectedItem && selectedItem._id) {
                      onSportSelection(selectedItem._id, index);
                    }
                  }}
                  placeholder="Select a sport"
                  style={styles.modalPicker}
                />
                <TouchableOpacity onPress={() => onDeleteHobby(index)}>
                  <Image source={closeIcon} style={styles.modalPickerDelete} />
                </TouchableOpacity>
              </View>
            ))}
            
            {playerData?.hobbies?.length < 5 && (
              <View style={styles.addSportContainer}>
                <TouchableOpacity
                  style={styles.addSportButton}
                  onPress={onAddSport}
                >
                  <Text style={styles.addSportButtonText}>+ Add Another Sport</Text>
                </TouchableOpacity>
              </View>
            )}
            
            <View style={styles.buttonContainer}>
              <Button color="#000" title="Save Changes" onPress={onSaveChanges} />
              <Button title="Close" color="#000" onPress={onClose} />
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
};

export default SportsModal;
