import React, { useState, useEffect, useRef } from "react";
import { View, Text, TouchableOpacity, FlatList, StyleSheet, Dimensions } from "react-native";
import Image from '@d11/react-native-fast-image';
import LinearGradient from 'react-native-linear-gradient';

export default function WhyPeopleLoveKhelSports({ data = [], blockData }) {
  const [activeIndex, setActiveIndex] = useState(0);
  const [homeWhyPeopleLoveKhelSports, setHomeWhyPeopleLoveKhelSports] = useState([]);
  const flatListRef = useRef(null);

  useEffect(() => {
    const sortedData = data.sort((a, b) => a.position - b.position);
    const extendedData = [...sortedData, ...sortedData, ...sortedData];
    setHomeWhyPeopleLoveKhelSports(extendedData);
    setTimeout(() => {
      if (flatListRef.current && sortedData.length > 0) {
        try {
          flatListRef.current.scrollToIndex({ animated: false, index: sortedData.length });
        } catch (error) {
          console.warn("Error scrolling to index:", error);
          // Fallback to first item if scroll fails
          flatListRef.current.scrollToOffset({ animated: false, offset: 0 });
        }
      }
    }, 100); // Increased timeout to ensure FlatList is properly rendered
  }, [data]);

  const screenWidth = Dimensions.get('window').width;
  const cardWidth = screenWidth - 30;
  const cardMargin = 10;

  const handleDotClick = (index) => {
    setActiveIndex(index);
    if (flatListRef.current && data.length > 0) {
      try {
        // Make sure the target index is within bounds
        const targetIndex = Math.min(index + data.length, homeWhyPeopleLoveKhelSports.length - 1);
        flatListRef.current.scrollToIndex({ animated: true, index: targetIndex });
      } catch (error) {
        console.warn("Error scrolling to index in handleDotClick:", error);
        // Fallback to scrollToOffset if scrollToIndex fails
        const offset = (cardWidth + (2 * cardMargin)) * (index + data.length);
        flatListRef.current.scrollToOffset({ animated: true, offset });
      }
    }
  };

  const handleCardSwipe = (event) => {
    const { contentOffset } = event.nativeEvent;
    const index = Math.round(contentOffset.x / (cardWidth + (2 * cardMargin)));

    // The middle set starts at data.length and ends at data.length * 2 - 1
    if (index < data.length) {
      setTimeout(() => {
        flatListRef.current?.scrollToIndex({ index: index + data.length, animated: false });
      }, 10);
      setActiveIndex(index % data.length);
    } else if (index >= data.length * 2) {
      setTimeout(() => {
        flatListRef.current?.scrollToIndex({ index: index - data.length, animated: false });
      }, 10);
      setActiveIndex(index % data.length);
    } else {
      setActiveIndex(index % data.length);
    }
  };

  const handleScroll = (event) => {
    const { contentOffset } = event.nativeEvent;
    const index = Math.round(contentOffset.x / cardWidth);
    if (data.length > 0) {
      setActiveIndex(index % data.length);
    }
  };


  return (
    <View style={{ marginTop: "5%" }}>
      {homeWhyPeopleLoveKhelSports.length > 0 && (
        <View style={styles.container}>
          <LinearGradient
            colors={['rgba(227, 31, 38, 0.3)', 'rgba(0, 174, 339, 0.5)']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.linearGradient}
          >
            <View style={styles.titleContainer}>
              <View style={styles.alignCenter}>
                <Text style={styles.titleText}>
                {blockData?.title}
                </Text>
              </View>
              <FlatList
                ref={flatListRef}
                horizontal
                data={homeWhyPeopleLoveKhelSports}
                keyExtractor={(_, index) => index.toString()}
                renderItem={({ item, index }) => (
                  <TouchableOpacity
                    activeOpacity={1}
                    style={[styles.card, { width: cardWidth }]}
                    onPress={() => handleDotClick(index % (data.length || 1))}
                  >
                    <Text style={styles.cardText} numberOfLines={5}>{item?.description}</Text>
                    <View style={styles.imageContainer}>
                      {item?.image ? (
                        <>
                          <Image
                            source={{ uri: item?.image }}
                            style={styles.image}
                          />
                          <Text style={styles.nameText}>-{item?.name}</Text>
                        </>
                      ) : (
                        <Text style={styles.nameText}>-{item?.name}</Text>
                      )}
                    </View>
                  </TouchableOpacity>
                )}
                showsHorizontalScrollIndicator={false}
                getItemLayout={(_, index) => ({
                  length: cardWidth + (2 * cardMargin),
                  offset: (cardWidth + (2 * cardMargin)) * index,
                  index,
                })}
                initialScrollIndex={data.length > 0 ? 0 : undefined}
                snapToInterval={data.length > 1 ? cardWidth + (2 * cardMargin) : undefined}
                snapToAlignment={data.length > 1 ? "start" : undefined}
                decelerationRate={data.length > 1 ? "fast" : undefined}
                pagingEnabled={data.length > 1}
                scrollEnabled={data.length > 1}
                contentContainerStyle={styles.contentContainer}
                ListFooterComponent={<View style={{ width: 125 }} />}
                onMomentumScrollEnd={data.length > 1 ? handleCardSwipe : undefined}
                onScrollToIndexFailed={data.length > 1 ? (info => {
                  console.warn('Scroll to index failed:', info);
                  // Fallback to offset-based scrolling
                  const offset = info.index * (cardWidth + (2 * cardMargin));
                  flatListRef.current?.scrollToOffset({ offset, animated: info.animated });
                }) : undefined}
                onScroll={handleScroll}
                scrollEventThrottle={16}
              />
              <View style={styles.dotsContainer}>
                {data.map((_, index) => (
                  <TouchableOpacity
                    key={index}
                    onPress={() => handleDotClick(index)}
                    style={[
                      styles.dot,
                      { backgroundColor: index === activeIndex ? "#333" : "#fff" },
                    ]}
                  />
                ))}
              </View>
            </View>
          </LinearGradient>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  linearGradient: {
    flex: 1,
  },
  titleContainer: {
    paddingVertical: 20,
  },
  container: {
    width: "100%",
  },
  alignCenter: {
    alignItems: "center",
    marginBottom:"4%"
  },
  titleText: {
    fontSize: 18,
    marginBottom: 2,
    color: "#fff",
    fontFamily: 'Lato-Bold'
  },
  card: {
    backgroundColor: "#fff",
    borderRadius: 10,
    padding: 20,
    marginHorizontal: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 10,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  cardText: {
    fontSize: 18,
    color: "#333",
    marginBottom: 12,
    fontFamily: 'Lato-Regular'
  },
  imageContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  image: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  nameText: {
    fontSize: 16,
    marginLeft: 12,
    fontFamily: 'Lato-Bold',
    color: "#333"
  },
  contentContainer: {
    alignItems: "center",
    padding: "1%"
  },
  dotsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 10,
    marginBottom: 10, // Add margin bottom for spacing
  },
  dot: {
    width: 25, // Increase touch area
    height: 7, // Increase touch area
    borderRadius: 3,
    marginHorizontal: 5,
    justifyContent: 'center', // Center content
    alignItems: 'center', // Center content
  },
});