import {
  View,
  Text,
  ScrollView,
  Image,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useEffect, useState, useRef } from "react";

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const TopCategories = ({ data, blockData }) => {
  const navigation = useNavigation();
  const [homeTopCategories, setHomeTopCategories] = useState([]);

  useEffect(() => {
    // Filter out items with null category and then sort the remaining data
    const filteredData = data.filter(item => item.category !== null);
    const sortedData = filteredData.sort((a, b) => a.position - b.position);
    setHomeTopCategories(sortedData);
  }, [data]);
  return (
    <View style={styles.container}>
     {homeTopCategories.length > 0 ?( <View>
      <View style={styles.heading}>
        <Text style={[styles.headingText]}>{blockData?.title}</Text>
      </View>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollViewContent}>
        {homeTopCategories.map((item) => {
          return(
          <TouchableOpacity
            key={item._id}
            style={styles.category}
            activeOpacity={0.8}
            onPress={() =>
              navigation.navigate('Collection', { selectedOption: item?.category?.name?.toLowerCase() })
            }>
            <Image
              source={{ uri: item?.category?.image }}
              style={styles.image}
            />
            {/* <Text style={styles.categoryName}>{item?.category?.name}</Text> */}
          </TouchableOpacity>
        )})}
      </ScrollView>
      </View>):null }
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f0f0f0',
    // paddingBottom: "11%",
    paddingVertical: '6%',
    paddingHorizontal: '5%',
    width:"100%",
    // height:"15%"
  },
  heading: {
    width: '95%',
    marginRight: 'auto',
    // marginLeft: 'auto',
    // marginBottom: "3%",
  },
  headingText: {
    fontSize: 20,
    // fontWeight: 'bold',
    color: '#333',
    fontFamily:"Lato-Bold"
  },
  scrollViewContent: {
    // paddingLeft: 10,
    // paddingRight: 10,
  },
  category: {
    width: screenWidth * 0.45,
    marginRight: 10,
    alignItems: 'center',
  },
  image: {
    width: '80%',
    height: screenHeight * 0.22,
    borderRadius: 8,
    objectFit:"contain"
  },
  categoryName: {
    marginTop: 5,
    fontWeight: 'bold',
    color: '#000',
    fontSize:15
  },
});

export default TopCategories;