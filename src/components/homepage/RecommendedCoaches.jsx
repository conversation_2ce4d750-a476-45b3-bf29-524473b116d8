import React, { useEffect, useState } from 'react';
import {
  ScrollView,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import Image from '@d11/react-native-fast-image';
import { useNavigation } from '@react-navigation/native';
import HTML from 'react-native-render-html';
const redirect = require('../../assets/redirect.png')
// Example Component: RecommendedCoaches
// 'data' prop is expected to be passed into this component.
export default function RecommendedCoaches({ data , blockData}) {
  const [homeCoach, setHomeCoach] = useState([]);
  const [createSection, setCreateSection] = useState(true);
  const navigation = useNavigation();
  useEffect(() => {
    // Sort coaches based on their position
    const sortedData = data.sort((a, b) => a.position - b.position);
    setHomeCoach(sortedData);
  }, [data]);

  // useEffect(() => {
  //   // Check if there's any coach data to create the section
  //   if (homeCoach.length > 0) {
  //     const hasNullCoachId = homeCoach.some(coach => coach.coach === null);
  //     setCreateSection(!hasNullCoachId);
  //   }
  // }, [homeCoach]);
  return (
    <View style={styles.container}>
      {homeCoach.length > 0 && createSection && (
        <View style={styles.containerTwo}>
       <Text style={styles.heading}>{blockData?.title}</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.scrollViewContent}
          >
            {homeCoach.map((product, index) => (
              product.coach && (
                <TouchableOpacity
                  key={index}
                  style={styles.card}
                  onPress={() => {
                    navigation.navigate('CoachProfile', { ID: product.coach._id })
                  }}
                >
                  <Image
                    source={{ uri: product.coach.profileImg || 'default_image_uri' }}
                    style={styles.image}
                  />
                  <View style={styles.cardContent}>
                    <Text style={styles.coachName}>
                      {product.coach.firstName} {product?.coach?.lastName}
                    </Text>
                    <Text style={styles.category}>
                      {product.coach.sportsCategories[0]}
                    </Text>
                    <Text style={styles.description} numberOfLines={2}>
                      {product?.coach?.firstName} is well known in {product.coach.sportsCategories[0]} of India with {product.coach.experience} years of experience.
                    </Text>
                    <View style={styles.learnMore}>
                      <Text style={styles.learnMoreText}>What will you learn</Text>
                      <Image source={redirect} style={{ width: 15, height: 15 }} />
                    </View>
                  </View>
                </TouchableOpacity>
              )
            ))}
          </ScrollView>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    // paddingBottom: '10%',
    // paddingTop: '5%',
    // paddingHorizontal: '5%',
    // marginTop: "6%",
    // elevation: 2,
  },
  containerTwo:{marginTop: "6%",paddingHorizontal: '5%',paddingBottom: '10%',paddingTop: '5%'},

  heading: {
    fontSize: 20,
    // fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    fontFamily: 'Lato-Bold'
  },
  scrollViewContent: {
    paddingRight: 10,
  },
  card: {
    width: 250,
    marginRight: 15,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    backgroundColor: '#FFF',
  },
  image: {
    width: '100%',
    height: 150,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    objectFit: "fill"
  },
  cardContent: {
    padding: 8,
    justifyContent: 'space-between',
    height: 150
  },
  coachName: {
    fontSize: 16,
    // fontWeight: 'bold',
    color: 'black',
    fontFamily: 'Lato-Bold'
  },
  category: {
    fontSize: 14,
    color: 'black',
    fontFamily: 'Lato-Regular'
  },
  description: {
    fontSize: 14,
    color: 'grey',
    marginTop: 4,
    fontFamily: 'Lato-Regular'
  },
  learnMore: {
    // padding: 8,
    borderRadius: 4,
    display: "flex",
    flexDirection: "row",
    alignItems: "center"
  },
  learnMoreText: {
    fontWeight: 'bold',
    color: '#000',
    paddingBottom: "0.5%",
    borderBottomWidth: 1,
    borderBottomColor: '#000',
  },
});
