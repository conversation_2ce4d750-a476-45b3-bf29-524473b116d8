import React, { useEffect, useRef, useState } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
import Image from '@d11/react-native-fast-image';
import { useNavigation } from '@react-navigation/native';
const redirect = require('../../assets/redirect.png')
export default function TopCourses({ data, blockData }) {
  const [homeCourses, setHomeCourses] = useState([]);
  const containerRef = useRef(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  const [createSection, setCreateSection] = useState(true);
  const navigation = useNavigation();

  useEffect(() => {
    const sortedData = data.sort((a, b) => a.position - b.position);
    setHomeCourses(sortedData);
  }, [data]);

  const renderDescription = (description) => {
    const maxChars = 80; // Maximum number of characters to display
    let truncatedDescription = description.replace(/<[^>]+>/g, ''); // Remove HTML tags
    truncatedDescription = truncatedDescription.substring(0, maxChars); // Limit characters
    if (description.length > maxChars) {
      truncatedDescription += "...";
    }
    return <Text style={styles.description}>{truncatedDescription}</Text>;
  };

  const renderTitle = (title)=>{
    const maxChar = 20 ;
    truncatedTitle = title.substring(0, maxChar);
    if(title.length > maxChar){
      truncatedTitle += "...";
    }

    return <Text style={styles.courseName}>{truncatedTitle}</Text>

  }
  return (
    <View style={styles.container}>
      {homeCourses.length > 0 && createSection && (
        <View style={styles.containerTwo}>
          <Text style={styles.heading}>{blockData?.title}</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.scrollViewContent}
            ref={containerRef}
          >
            {homeCourses.map((product, index) => (
              product?.course ? (
                <TouchableOpacity
                  key={index}
                  style={styles.courseItem}
                  onPress={() =>
                    navigation.navigate('Courses', { courseId: product?.course?._id })
                  }
                >
                  <Image
                    source={{ uri: product?.course?.images[0]?.url }}
                    style={styles.image}
                  />
                  <View style={styles.courseDetails}>
                    <Text style={styles.price}>
                      {product?.course?.fees?.feesCourse
                        ? `₹ ${Math.round(product?.course?.fees?.feesCourse * 1.10)}`
                        : "Explore"}
                    </Text>
                    <Text style={styles.courseName} numberOfLines={2}>
                      {renderTitle(product?.course?.courseName)}
                    </Text>
                    {renderDescription(product?.course?.description)}
                    
                    <View style={styles.learnMore}>
                      <Text style={styles.learnMoreText}>What will you learn</Text>
                      <Image source={redirect} style={{ width: 15, height: 15 }} />
                    </View>
                  </View>
                </TouchableOpacity>
              ) : null
            ))}
          </ScrollView>
        </View>
      )}
    </View>
  );
  
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    paddingBottom: '10%',
    paddingHorizontal: '5%',
  },
  containerTwo:{},
  heading: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#000',
  },
  scrollViewContent: {
    paddingRight: 16,
  },
  courseItem: {
    width: 250,
    marginRight: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    backgroundColor: '#FAFBFC',
    overflow: 'hidden',
    
  },
  image: {
    width: '100%',
    height: 180,
  },
  courseDetails: {
    borderTopWidth: 1,
    borderColor: '#e2e2e2',
    paddingTop: 8,
    padding: 8,
  },
  price: {
    backgroundColor: '#E31F26',
    color: '#FAFBFCff',
    paddingHorizontal: "5%",
    paddingVertical: "3%",
    borderRadius: 4,
    marginBottom: 8,
    alignSelf: 'flex-start',
  },
  courseName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
    color:"#000"
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  learnMore: {
    // padding: 8,
    borderRadius: 4,
    display:"flex",
    flexDirection:"row",
    alignItems:"center"
  },
  learnMoreText: {
    fontWeight: 'bold',
    color: '#000',
    paddingBottom: "0.5%", 
    borderBottomWidth: 1, 
    borderBottomColor: '#000', 
  },
});
