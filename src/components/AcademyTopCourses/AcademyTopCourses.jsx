import React from 'react';
import { View, Text, ScrollView } from 'react-native';
import CourseCard from '../CourseCard/CourseCard';
import styles from './styles';

const AcademyTopCourses = ({ courses, title }) => {
  return (
    <View style={styles.container}>
      <Text style={styles.heading}>{title || 'TRAINING SCHEDULE'}</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.scrollViewContent}>
        {courses.map((item) => (
          <CourseCard key={item._id} course={item.course} />
        ))}
      </ScrollView>
    </View>
  );
};

export default AcademyTopCourses; 